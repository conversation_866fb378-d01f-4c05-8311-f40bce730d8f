{"version": 3, "file": "acorn.js", "sources": ["../src/identifier.js", "../src/tokentype.js", "../src/whitespace.js", "../src/util.js", "../src/locutil.js", "../src/options.js", "../src/scopeflags.js", "../src/state.js", "../src/parseutil.js", "../src/statement.js", "../src/lval.js", "../src/expression.js", "../src/location.js", "../src/scope.js", "../src/node.js", "../src/tokencontext.js", "../src/unicode-property-data.js", "../src/regexp.js", "../src/tokenize.js", "../src/index.js"], "sourcesContent": ["// Reserved word lists for various dialects of the language\n\nexport const reservedWords = {\n  3: \"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile\",\n  5: \"class enum extends super const export import\",\n  6: \"enum\",\n  strict: \"implements interface let package private protected public static yield\",\n  strictBind: \"eval arguments\"\n}\n\n// And the keywords\n\nconst ecma5AndLessKeywords = \"break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this\"\n\nexport const keywords = {\n  5: ecma5AndLessKeywords,\n  6: ecma5AndLessKeywords + \" const class extends export import super\"\n}\n\nexport const keywordRelationalOperator = /^in(stanceof)?$/\n\n// ## Character categories\n\n// Big ugly regular expressions that match characters in the\n// whitespace, identifier, and identifier-start categories. These\n// are only applied when a character is found to actually have a\n// code point above 128.\n// Generated by `bin/generate-identifier-regex.js`.\n\nlet nonASCIIidentifierStartChars = \"\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u037f\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086a\\u08a0-\\u08b4\\u08b6-\\u08bd\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u09fc\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0af9\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d\\u0c58-\\u0c5a\\u0c60\\u0c61\\u0c80\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d05-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d54-\\u0d56\\u0d5f-\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32\\u0e33\\u0e40-\\u0e46\\u0e81\\u0e82\\u0e84\\u0e87\\u0e88\\u0e8a\\u0e8d\\u0e94-\\u0e97\\u0e99-\\u0e9f\\u0ea1-\\u0ea3\\u0ea5\\u0ea7\\u0eaa\\u0eab\\u0ead-\\u0eb0\\u0eb2\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f5\\u13f8-\\u13fd\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f8\\u1700-\\u170c\\u170e-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1878\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191e\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19b0-\\u19c9\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4b\\u1b83-\\u1ba0\\u1bae\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1c80-\\u1c88\\u1c90-\\u1cba\\u1cbd-\\u1cbf\\u1ce9-\\u1cec\\u1cee-\\u1cf1\\u1cf5\\u1cf6\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2118-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2c2e\\u2c30-\\u2c5e\\u2c60-\\u2ce4\\u2ceb-\\u2cee\\u2cf2\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309b-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u31a0-\\u31ba\\u31f0-\\u31ff\\u3400-\\u4db5\\u4e00-\\u9fef\\ua000-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a\\ua62b\\ua640-\\ua66e\\ua67f-\\ua69d\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua7b9\\ua7f7-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua8fd\\ua8fe\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\ua9e0-\\ua9e4\\ua9e6-\\ua9ef\\ua9fa-\\ua9fe\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa7e-\\uaaaf\\uaab1\\uaab5\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uab30-\\uab5a\\uab5c-\\uab65\\uab70-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40\\ufb41\\ufb43\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc\"\nlet nonASCIIidentifierChars = \"\\u200c\\u200d\\xb7\\u0300-\\u036f\\u0387\\u0483-\\u0487\\u0591-\\u05bd\\u05bf\\u05c1\\u05c2\\u05c4\\u05c5\\u05c7\\u0610-\\u061a\\u064b-\\u0669\\u0670\\u06d6-\\u06dc\\u06df-\\u06e4\\u06e7\\u06e8\\u06ea-\\u06ed\\u06f0-\\u06f9\\u0711\\u0730-\\u074a\\u07a6-\\u07b0\\u07c0-\\u07c9\\u07eb-\\u07f3\\u07fd\\u0816-\\u0819\\u081b-\\u0823\\u0825-\\u0827\\u0829-\\u082d\\u0859-\\u085b\\u08d3-\\u08e1\\u08e3-\\u0903\\u093a-\\u093c\\u093e-\\u094f\\u0951-\\u0957\\u0962\\u0963\\u0966-\\u096f\\u0981-\\u0983\\u09bc\\u09be-\\u09c4\\u09c7\\u09c8\\u09cb-\\u09cd\\u09d7\\u09e2\\u09e3\\u09e6-\\u09ef\\u09fe\\u0a01-\\u0a03\\u0a3c\\u0a3e-\\u0a42\\u0a47\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a66-\\u0a71\\u0a75\\u0a81-\\u0a83\\u0abc\\u0abe-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ae2\\u0ae3\\u0ae6-\\u0aef\\u0afa-\\u0aff\\u0b01-\\u0b03\\u0b3c\\u0b3e-\\u0b44\\u0b47\\u0b48\\u0b4b-\\u0b4d\\u0b56\\u0b57\\u0b62\\u0b63\\u0b66-\\u0b6f\\u0b82\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd7\\u0be6-\\u0bef\\u0c00-\\u0c04\\u0c3e-\\u0c44\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55\\u0c56\\u0c62\\u0c63\\u0c66-\\u0c6f\\u0c81-\\u0c83\\u0cbc\\u0cbe-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5\\u0cd6\\u0ce2\\u0ce3\\u0ce6-\\u0cef\\u0d00-\\u0d03\\u0d3b\\u0d3c\\u0d3e-\\u0d44\\u0d46-\\u0d48\\u0d4a-\\u0d4d\\u0d57\\u0d62\\u0d63\\u0d66-\\u0d6f\\u0d82\\u0d83\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0de6-\\u0def\\u0df2\\u0df3\\u0e31\\u0e34-\\u0e3a\\u0e47-\\u0e4e\\u0e50-\\u0e59\\u0eb1\\u0eb4-\\u0eb9\\u0ebb\\u0ebc\\u0ec8-\\u0ecd\\u0ed0-\\u0ed9\\u0f18\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f3e\\u0f3f\\u0f71-\\u0f84\\u0f86\\u0f87\\u0f8d-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u102b-\\u103e\\u1040-\\u1049\\u1056-\\u1059\\u105e-\\u1060\\u1062-\\u1064\\u1067-\\u106d\\u1071-\\u1074\\u1082-\\u108d\\u108f-\\u109d\\u135d-\\u135f\\u1369-\\u1371\\u1712-\\u1714\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17b4-\\u17d3\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u1810-\\u1819\\u18a9\\u1920-\\u192b\\u1930-\\u193b\\u1946-\\u194f\\u19d0-\\u19da\\u1a17-\\u1a1b\\u1a55-\\u1a5e\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1ab0-\\u1abd\\u1b00-\\u1b04\\u1b34-\\u1b44\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1b80-\\u1b82\\u1ba1-\\u1bad\\u1bb0-\\u1bb9\\u1be6-\\u1bf3\\u1c24-\\u1c37\\u1c40-\\u1c49\\u1c50-\\u1c59\\u1cd0-\\u1cd2\\u1cd4-\\u1ce8\\u1ced\\u1cf2-\\u1cf4\\u1cf7-\\u1cf9\\u1dc0-\\u1df9\\u1dfb-\\u1dff\\u203f\\u2040\\u2054\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2cef-\\u2cf1\\u2d7f\\u2de0-\\u2dff\\u302a-\\u302f\\u3099\\u309a\\ua620-\\ua629\\ua66f\\ua674-\\ua67d\\ua69e\\ua69f\\ua6f0\\ua6f1\\ua802\\ua806\\ua80b\\ua823-\\ua827\\ua880\\ua881\\ua8b4-\\ua8c5\\ua8d0-\\ua8d9\\ua8e0-\\ua8f1\\ua8ff-\\ua909\\ua926-\\ua92d\\ua947-\\ua953\\ua980-\\ua983\\ua9b3-\\ua9c0\\ua9d0-\\ua9d9\\ua9e5\\ua9f0-\\ua9f9\\uaa29-\\uaa36\\uaa43\\uaa4c\\uaa4d\\uaa50-\\uaa59\\uaa7b-\\uaa7d\\uaab0\\uaab2-\\uaab4\\uaab7\\uaab8\\uaabe\\uaabf\\uaac1\\uaaeb-\\uaaef\\uaaf5\\uaaf6\\uabe3-\\uabea\\uabec\\uabed\\uabf0-\\uabf9\\ufb1e\\ufe00-\\ufe0f\\ufe20-\\ufe2f\\ufe33\\ufe34\\ufe4d-\\ufe4f\\uff10-\\uff19\\uff3f\"\n\nconst nonASCIIidentifierStart = new RegExp(\"[\" + nonASCIIidentifierStartChars + \"]\")\nconst nonASCIIidentifier = new RegExp(\"[\" + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"]\")\n\nnonASCIIidentifierStartChars = nonASCIIidentifierChars = null\n\n// These are a run-length and offset encoded representation of the\n// >0xffff code points that are a valid part of identifiers. The\n// offset starts at 0x10000, and each pair of numbers represents an\n// offset to the next range, and then a size of the range. They were\n// generated by bin/generate-identifier-regex.js\n\n// eslint-disable-next-line comma-spacing\nconst astralIdentifierStartCodes = [0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,477,28,11,0,9,21,190,52,76,44,33,24,27,35,30,0,12,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,54,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,86,26,230,43,117,63,32,0,257,0,11,39,8,0,22,0,12,39,3,3,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,270,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,68,12,0,67,12,65,1,31,6129,15,754,9486,286,82,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,4149,196,60,67,1213,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42710,42,4148,12,221,3,5761,15,7472,3104,541]\n\n// eslint-disable-next-line comma-spacing\nconst astralIdentifierCodes = [509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,525,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,4,9,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,280,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1016,45,17,3,19723,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,2214,6,110,6,6,9,792487,239]\n\n// This has a complexity linear to the value of the code. The\n// assumption is that looking up astral identifier characters is\n// rare.\nfunction isInAstralSet(code, set) {\n  let pos = 0x10000\n  for (let i = 0; i < set.length; i += 2) {\n    pos += set[i]\n    if (pos > code) return false\n    pos += set[i + 1]\n    if (pos >= code) return true\n  }\n}\n\n// Test whether a given character code starts an identifier.\n\nexport function isIdentifierStart(code, astral) {\n  if (code < 65) return code === 36\n  if (code < 91) return true\n  if (code < 97) return code === 95\n  if (code < 123) return true\n  if (code <= 0xffff) return code >= 0xaa && nonASCIIidentifierStart.test(String.fromCharCode(code))\n  if (astral === false) return false\n  return isInAstralSet(code, astralIdentifierStartCodes)\n}\n\n// Test whether a given character is part of an identifier.\n\nexport function isIdentifierChar(code, astral) {\n  if (code < 48) return code === 36\n  if (code < 58) return true\n  if (code < 65) return false\n  if (code < 91) return true\n  if (code < 97) return code === 95\n  if (code < 123) return true\n  if (code <= 0xffff) return code >= 0xaa && nonASCIIidentifier.test(String.fromCharCode(code))\n  if (astral === false) return false\n  return isInAstralSet(code, astralIdentifierStartCodes) || isInAstralSet(code, astralIdentifierCodes)\n}\n", "// ## Token types\n\n// The assignment of fine-grained, information-carrying type objects\n// allows the tokenizer to store the information it has about a\n// token in a way that is very cheap for the parser to look up.\n\n// All token type variables start with an underscore, to make them\n// easy to recognize.\n\n// The `beforeExpr` property is used to disambiguate between regular\n// expressions and divisions. It is set on all token types that can\n// be followed by an expression (thus, a slash after them would be a\n// regular expression).\n//\n// The `startsExpr` property is used to check if the token ends a\n// `yield` expression. It is set on all token types that either can\n// directly start an expression (like a quotation mark) or can\n// continue an expression (like the body of a string).\n//\n// `isLoop` marks a keyword as starting a loop, which is important\n// to know when parsing a label, in order to allow or disallow\n// continue jumps to that label.\n\nexport class TokenType {\n  constructor(label, conf = {}) {\n    this.label = label\n    this.keyword = conf.keyword\n    this.beforeExpr = !!conf.beforeExpr\n    this.startsExpr = !!conf.startsExpr\n    this.isLoop = !!conf.isLoop\n    this.isAssign = !!conf.isAssign\n    this.prefix = !!conf.prefix\n    this.postfix = !!conf.postfix\n    this.binop = conf.binop || null\n    this.updateContext = null\n  }\n}\n\nfunction binop(name, prec) {\n  return new TokenType(name, {beforeExpr: true, binop: prec})\n}\nconst beforeExpr = {beforeExpr: true}, startsExpr = {startsExpr: true}\n\n// Map keyword names to token types.\n\nexport const keywords = {}\n\n// Succinct definitions of keyword token types\nfunction kw(name, options = {}) {\n  options.keyword = name\n  return keywords[name] = new TokenType(name, options)\n}\n\nexport const types = {\n  num: new TokenType(\"num\", startsExpr),\n  regexp: new TokenType(\"regexp\", startsExpr),\n  string: new TokenType(\"string\", startsExpr),\n  name: new TokenType(\"name\", startsExpr),\n  eof: new TokenType(\"eof\"),\n\n  // Punctuation token types.\n  bracketL: new TokenType(\"[\", {beforeExpr: true, startsExpr: true}),\n  bracketR: new TokenType(\"]\"),\n  braceL: new TokenType(\"{\", {beforeExpr: true, startsExpr: true}),\n  braceR: new TokenType(\"}\"),\n  parenL: new TokenType(\"(\", {beforeExpr: true, startsExpr: true}),\n  parenR: new TokenType(\")\"),\n  comma: new TokenType(\",\", beforeExpr),\n  semi: new TokenType(\";\", beforeExpr),\n  colon: new TokenType(\":\", beforeExpr),\n  dot: new TokenType(\".\"),\n  question: new TokenType(\"?\", beforeExpr),\n  arrow: new TokenType(\"=>\", beforeExpr),\n  template: new TokenType(\"template\"),\n  invalidTemplate: new TokenType(\"invalidTemplate\"),\n  ellipsis: new TokenType(\"...\", beforeExpr),\n  backQuote: new TokenType(\"`\", startsExpr),\n  dollarBraceL: new TokenType(\"${\", {beforeExpr: true, startsExpr: true}),\n\n  // Operators. These carry several kinds of properties to help the\n  // parser use them properly (the presence of these properties is\n  // what categorizes them as operators).\n  //\n  // `binop`, when present, specifies that this operator is a binary\n  // operator, and will refer to its precedence.\n  //\n  // `prefix` and `postfix` mark the operator as a prefix or postfix\n  // unary operator.\n  //\n  // `isAssign` marks all of `=`, `+=`, `-=` etcetera, which act as\n  // binary operators with a very low precedence, that should result\n  // in AssignmentExpression nodes.\n\n  eq: new TokenType(\"=\", {beforeExpr: true, isAssign: true}),\n  assign: new TokenType(\"_=\", {beforeExpr: true, isAssign: true}),\n  incDec: new TokenType(\"++/--\", {prefix: true, postfix: true, startsExpr: true}),\n  prefix: new TokenType(\"!/~\", {beforeExpr: true, prefix: true, startsExpr: true}),\n  logicalOR: binop(\"||\", 1),\n  logicalAND: binop(\"&&\", 2),\n  bitwiseOR: binop(\"|\", 3),\n  bitwiseXOR: binop(\"^\", 4),\n  bitwiseAND: binop(\"&\", 5),\n  equality: binop(\"==/!=/===/!==\", 6),\n  relational: binop(\"</>/<=/>=\", 7),\n  bitShift: binop(\"<</>>/>>>\", 8),\n  plusMin: new TokenType(\"+/-\", {beforeExpr: true, binop: 9, prefix: true, startsExpr: true}),\n  modulo: binop(\"%\", 10),\n  star: binop(\"*\", 10),\n  slash: binop(\"/\", 10),\n  starstar: new TokenType(\"**\", {beforeExpr: true}),\n\n  // Keyword token types.\n  _break: kw(\"break\"),\n  _case: kw(\"case\", beforeExpr),\n  _catch: kw(\"catch\"),\n  _continue: kw(\"continue\"),\n  _debugger: kw(\"debugger\"),\n  _default: kw(\"default\", beforeExpr),\n  _do: kw(\"do\", {isLoop: true, beforeExpr: true}),\n  _else: kw(\"else\", beforeExpr),\n  _finally: kw(\"finally\"),\n  _for: kw(\"for\", {isLoop: true}),\n  _function: kw(\"function\", startsExpr),\n  _if: kw(\"if\"),\n  _return: kw(\"return\", beforeExpr),\n  _switch: kw(\"switch\"),\n  _throw: kw(\"throw\", beforeExpr),\n  _try: kw(\"try\"),\n  _var: kw(\"var\"),\n  _const: kw(\"const\"),\n  _while: kw(\"while\", {isLoop: true}),\n  _with: kw(\"with\"),\n  _new: kw(\"new\", {beforeExpr: true, startsExpr: true}),\n  _this: kw(\"this\", startsExpr),\n  _super: kw(\"super\", startsExpr),\n  _class: kw(\"class\", startsExpr),\n  _extends: kw(\"extends\", beforeExpr),\n  _export: kw(\"export\"),\n  _import: kw(\"import\"),\n  _null: kw(\"null\", startsExpr),\n  _true: kw(\"true\", startsExpr),\n  _false: kw(\"false\", startsExpr),\n  _in: kw(\"in\", {beforeExpr: true, binop: 7}),\n  _instanceof: kw(\"instanceof\", {beforeExpr: true, binop: 7}),\n  _typeof: kw(\"typeof\", {beforeExpr: true, prefix: true, startsExpr: true}),\n  _void: kw(\"void\", {beforeExpr: true, prefix: true, startsExpr: true}),\n  _delete: kw(\"delete\", {beforeExpr: true, prefix: true, startsExpr: true})\n}\n", "// Matches a whole line break (where CRLF is considered a single\n// line break). Used to count lines.\n\nexport const lineBreak = /\\r\\n?|\\n|\\u2028|\\u2029/\nexport const lineBreakG = new RegExp(lineBreak.source, \"g\")\n\nexport function isNewLine(code, ecma2019String) {\n  return code === 10 || code === 13 || (!ecma2019String && (code === 0x2028 || code === 0x2029))\n}\n\nexport const nonASCIIwhitespace = /[\\u1680\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff]/\n\nexport const skipWhiteSpace = /(?:\\s|\\/\\/.*|\\/\\*[^]*?\\*\\/)*/g\n", "const {hasOwnProperty, toString} = Object.prototype\n\n// Checks if an object has a property.\n\nexport function has(obj, propName) {\n  return hasOwnProperty.call(obj, propName)\n}\n\nexport const isArray = Array.isArray || ((obj) => (\n  toString.call(obj) === \"[object Array]\"\n))\n\nexport function wordsRegexp(words) {\n  return new RegExp(\"^(?:\" + words.replace(/ /g, \"|\") + \")$\")\n}\n", "import {lineBreakG} from \"./whitespace\"\n\n// These are used when `options.locations` is on, for the\n// `startLoc` and `endLoc` properties.\n\nexport class Position {\n  constructor(line, col) {\n    this.line = line\n    this.column = col\n  }\n\n  offset(n) {\n    return new Position(this.line, this.column + n)\n  }\n}\n\nexport class SourceLocation {\n  constructor(p, start, end) {\n    this.start = start\n    this.end = end\n    if (p.sourceFile !== null) this.source = p.sourceFile\n  }\n}\n\n// The `getLineInfo` function is mostly useful when the\n// `locations` option is off (for performance reasons) and you\n// want to find the line/column position for a given character\n// offset. `input` should be the code string that the offset refers\n// into.\n\nexport function getLineInfo(input, offset) {\n  for (let line = 1, cur = 0;;) {\n    lineBreakG.lastIndex = cur\n    let match = lineBreakG.exec(input)\n    if (match && match.index < offset) {\n      ++line\n      cur = match.index + match[0].length\n    } else {\n      return new Position(line, offset - cur)\n    }\n  }\n}\n", "import {has, isArray} from \"./util\"\nimport {SourceLocation} from \"./locutil\"\n\n// A second optional argument can be given to further configure\n// the parser process. These options are recognized:\n\nexport const defaultOptions = {\n  // `ecmaVersion` indicates the ECMAScript version to parse. Must be\n  // either 3, 5, 6 (2015), 7 (2016), 8 (2017), 9 (2018), or 10\n  // (2019). This influences support for strict mode, the set of\n  // reserved words, and support for new syntax features. The default\n  // is 9.\n  ecmaVersion: 9,\n  // `sourceType` indicates the mode the code should be parsed in.\n  // Can be either `\"script\"` or `\"module\"`. This influences global\n  // strict mode and parsing of `import` and `export` declarations.\n  sourceType: \"script\",\n  // `onInsertedSemicolon` can be a callback that will be called\n  // when a semicolon is automatically inserted. It will be passed\n  // the position of the comma as an offset, and if `locations` is\n  // enabled, it is given the location as a `{line, column}` object\n  // as second argument.\n  onInsertedSemicolon: null,\n  // `onTrailingComma` is similar to `onInsertedSemicolon`, but for\n  // trailing commas.\n  onTrailingComma: null,\n  // By default, reserved words are only enforced if ecmaVersion >= 5.\n  // Set `allowReserved` to a boolean value to explicitly turn this on\n  // an off. When this option has the value \"never\", reserved words\n  // and keywords can also not be used as property names.\n  allowReserved: null,\n  // When enabled, a return at the top level is not considered an\n  // error.\n  allowReturnOutsideFunction: false,\n  // When enabled, import/export statements are not constrained to\n  // appearing at the top of the program.\n  allowImportExportEverywhere: false,\n  // When enabled, await identifiers are allowed to appear at the top-level scope,\n  // but they are still not allowed in non-async functions.\n  allowAwaitOutsideFunction: false,\n  // When enabled, hashbang directive in the beginning of file\n  // is allowed and treated as a line comment.\n  allowHashBang: false,\n  // When `locations` is on, `loc` properties holding objects with\n  // `start` and `end` properties in `{line, column}` form (with\n  // line being 1-based and column 0-based) will be attached to the\n  // nodes.\n  locations: false,\n  // A function can be passed as `onToken` option, which will\n  // cause Acorn to call that function with object in the same\n  // format as tokens returned from `tokenizer().getToken()`. Note\n  // that you are not allowed to call the parser from the\n  // callback—that will corrupt its internal state.\n  onToken: null,\n  // A function can be passed as `onComment` option, which will\n  // cause Acorn to call that function with `(block, text, start,\n  // end)` parameters whenever a comment is skipped. `block` is a\n  // boolean indicating whether this is a block (`/* */`) comment,\n  // `text` is the content of the comment, and `start` and `end` are\n  // character offsets that denote the start and end of the comment.\n  // When the `locations` option is on, two more parameters are\n  // passed, the full `{line, column}` locations of the start and\n  // end of the comments. Note that you are not allowed to call the\n  // parser from the callback—that will corrupt its internal state.\n  onComment: null,\n  // Nodes have their start and end characters offsets recorded in\n  // `start` and `end` properties (directly on the node, rather than\n  // the `loc` object, which holds line/column data. To also add a\n  // [semi-standardized][range] `range` property holding a `[start,\n  // end]` array with the same numbers, set the `ranges` option to\n  // `true`.\n  //\n  // [range]: https://bugzilla.mozilla.org/show_bug.cgi?id=745678\n  ranges: false,\n  // It is possible to parse multiple files into a single AST by\n  // passing the tree produced by parsing the first file as\n  // `program` option in subsequent parses. This will add the\n  // toplevel forms of the parsed file to the `Program` (top) node\n  // of an existing parse tree.\n  program: null,\n  // When `locations` is on, you can pass this to record the source\n  // file in every node's `loc` object.\n  sourceFile: null,\n  // This value, if given, is stored in every node, whether\n  // `locations` is on or off.\n  directSourceFile: null,\n  // When enabled, parenthesized expressions are represented by\n  // (non-standard) ParenthesizedExpression nodes\n  preserveParens: false\n}\n\n// Interpret and default an options object\n\nexport function getOptions(opts) {\n  let options = {}\n\n  for (let opt in defaultOptions)\n    options[opt] = opts && has(opts, opt) ? opts[opt] : defaultOptions[opt]\n\n  if (options.ecmaVersion >= 2015)\n    options.ecmaVersion -= 2009\n\n  if (options.allowReserved == null)\n    options.allowReserved = options.ecmaVersion < 5\n\n  if (isArray(options.onToken)) {\n    let tokens = options.onToken\n    options.onToken = (token) => tokens.push(token)\n  }\n  if (isArray(options.onComment))\n    options.onComment = pushComment(options, options.onComment)\n\n  return options\n}\n\nfunction pushComment(options, array) {\n  return function(block, text, start, end, startLoc, endLoc) {\n    let comment = {\n      type: block ? \"Block\" : \"Line\",\n      value: text,\n      start: start,\n      end: end\n    }\n    if (options.locations)\n      comment.loc = new SourceLocation(this, startLoc, endLoc)\n    if (options.ranges)\n      comment.range = [start, end]\n    array.push(comment)\n  }\n}\n", "// Each scope gets a bitset that may contain these flags\nexport const\n    SCOPE_TOP = 1,\n    SCOPE_FUNCTION = 2,\n    SCOPE_VAR = SCOPE_TOP | SCOPE_FUNCTION,\n    SCOPE_ASYNC = 4,\n    SCOPE_GENERATOR = 8,\n    SCOPE_ARROW = 16,\n    SCOPE_SIMPLE_CATCH = 32,\n    SCOPE_SUPER = 64,\n    SCOPE_DIRECT_SUPER = 128\n\nexport function functionFlags(async, generator) {\n  return SCOPE_FUNCTION | (async ? SCOPE_ASYNC : 0) | (generator ? SCOPE_GENERATOR : 0)\n}\n\n// Used in checkLVal and declareName to determine the type of a binding\nexport const\n    BIND_NONE = 0, // Not a binding\n    BIND_VAR = 1, // Var-style binding\n    BIND_LEXICAL = 2, // Let- or const-style binding\n    BIND_FUNCTION = 3, // Function declaration\n    BIND_SIMPLE_CATCH = 4, // Simple (identifier pattern) catch binding\n    BIND_OUTSIDE = 5 // Special case for function names as bound inside the function\n", "import {reservedWords, keywords} from \"./identifier\"\nimport {types as tt} from \"./tokentype\"\nimport {lineBreak} from \"./whitespace\"\nimport {getOptions} from \"./options\"\nimport {wordsRegexp} from \"./util\"\nimport {SCOPE_TOP, SCOPE_FUNCTION, SCOPE_ASYNC, SCOPE_GENERATOR, SCOPE_SUPER, SCOPE_DIRECT_SUPER} from \"./scopeflags\"\n\nexport class Parser {\n  constructor(options, input, startPos) {\n    this.options = options = getOptions(options)\n    this.sourceFile = options.sourceFile\n    this.keywords = wordsRegexp(keywords[options.ecmaVersion >= 6 ? 6 : 5])\n    let reserved = \"\"\n    if (!options.allowReserved) {\n      for (let v = options.ecmaVersion;; v--)\n        if (reserved = reservedWords[v]) break\n      if (options.sourceType === \"module\") reserved += \" await\"\n    }\n    this.reservedWords = wordsRegexp(reserved)\n    let reservedStrict = (reserved ? reserved + \" \" : \"\") + reservedWords.strict\n    this.reservedWordsStrict = wordsRegexp(reservedStrict)\n    this.reservedWordsStrictBind = wordsRegexp(reservedStrict + \" \" + reservedWords.strictBind)\n    this.input = String(input)\n\n    // Used to signal to callers of `readWord1` whether the word\n    // contained any escape sequences. This is needed because words with\n    // escape sequences must not be interpreted as keywords.\n    this.containsEsc = false\n\n    // Set up token state\n\n    // The current position of the tokenizer in the input.\n    if (startPos) {\n      this.pos = startPos\n      this.lineStart = this.input.lastIndexOf(\"\\n\", startPos - 1) + 1\n      this.curLine = this.input.slice(0, this.lineStart).split(lineBreak).length\n    } else {\n      this.pos = this.lineStart = 0\n      this.curLine = 1\n    }\n\n    // Properties of the current token:\n    // Its type\n    this.type = tt.eof\n    // For tokens that include more information than their type, the value\n    this.value = null\n    // Its start and end offset\n    this.start = this.end = this.pos\n    // And, if locations are used, the {line, column} object\n    // corresponding to those offsets\n    this.startLoc = this.endLoc = this.curPosition()\n\n    // Position information for the previous token\n    this.lastTokEndLoc = this.lastTokStartLoc = null\n    this.lastTokStart = this.lastTokEnd = this.pos\n\n    // The context stack is used to superficially track syntactic\n    // context to predict whether a regular expression is allowed in a\n    // given position.\n    this.context = this.initialContext()\n    this.exprAllowed = true\n\n    // Figure out if it's a module code.\n    this.inModule = options.sourceType === \"module\"\n    this.strict = this.inModule || this.strictDirective(this.pos)\n\n    // Used to signify the start of a potential arrow function\n    this.potentialArrowAt = -1\n\n    // Positions to delayed-check that yield/await does not exist in default parameters.\n    this.yieldPos = this.awaitPos = this.awaitIdentPos = 0\n    // Labels in scope.\n    this.labels = []\n    // Thus-far undefined exports.\n    this.undefinedExports = {}\n\n    // If enabled, skip leading hashbang line.\n    if (this.pos === 0 && options.allowHashBang && this.input.slice(0, 2) === \"#!\")\n      this.skipLineComment(2)\n\n    // Scope tracking for duplicate variable names (see scope.js)\n    this.scopeStack = []\n    this.enterScope(SCOPE_TOP)\n\n    // For RegExp validation\n    this.regexpState = null\n  }\n\n  parse() {\n    let node = this.options.program || this.startNode()\n    this.nextToken()\n    return this.parseTopLevel(node)\n  }\n\n  get inFunction() { return (this.currentVarScope().flags & SCOPE_FUNCTION) > 0 }\n  get inGenerator() { return (this.currentVarScope().flags & SCOPE_GENERATOR) > 0 }\n  get inAsync() { return (this.currentVarScope().flags & SCOPE_ASYNC) > 0 }\n  get allowSuper() { return (this.currentThisScope().flags & SCOPE_SUPER) > 0 }\n  get allowDirectSuper() { return (this.currentThisScope().flags & SCOPE_DIRECT_SUPER) > 0 }\n  get treatFunctionsAsVar() { return this.treatFunctionsAsVarInScope(this.currentScope()) }\n\n  // Switch to a getter for 7.0.0.\n  inNonArrowFunction() { return (this.currentThisScope().flags & SCOPE_FUNCTION) > 0 }\n\n  static extend(...plugins) {\n    let cls = this\n    for (let i = 0; i < plugins.length; i++) cls = plugins[i](cls)\n    return cls\n  }\n\n  static parse(input, options) {\n    return new this(options, input).parse()\n  }\n\n  static parseExpressionAt(input, pos, options) {\n    let parser = new this(options, input, pos)\n    parser.nextToken()\n    return parser.parseExpression()\n  }\n\n  static tokenizer(input, options) {\n    return new this(options, input)\n  }\n}\n", "import {types as tt} from \"./tokentype\"\nimport {<PERSON>rse<PERSON>} from \"./state\"\nimport {lineBreak, skipWhiteSpace} from \"./whitespace\"\n\nconst pp = Parser.prototype\n\n// ## Parser utilities\n\nconst literal = /^(?:'((?:\\\\.|[^'])*?)'|\"((?:\\\\.|[^\"])*?)\")/\npp.strictDirective = function(start) {\n  for (;;) {\n    // Try to find string literal.\n    skipWhiteSpace.lastIndex = start\n    start += skipWhiteSpace.exec(this.input)[0].length\n    let match = literal.exec(this.input.slice(start))\n    if (!match) return false\n    if ((match[1] || match[2]) === \"use strict\") return true\n    start += match[0].length\n\n    // Skip semicolon, if any.\n    skipWhiteSpace.lastIndex = start\n    start += skipWhiteSpace.exec(this.input)[0].length\n    if (this.input[start] === \";\")\n      start++\n  }\n}\n\n// Predicate that tests whether the next token is of the given\n// type, and if yes, consumes it as a side effect.\n\npp.eat = function(type) {\n  if (this.type === type) {\n    this.next()\n    return true\n  } else {\n    return false\n  }\n}\n\n// Tests whether parsed token is a contextual keyword.\n\npp.isContextual = function(name) {\n  return this.type === tt.name && this.value === name && !this.containsEsc\n}\n\n// Consumes contextual keyword if possible.\n\npp.eatContextual = function(name) {\n  if (!this.isContextual(name)) return false\n  this.next()\n  return true\n}\n\n// Asserts that following token is given contextual keyword.\n\npp.expectContextual = function(name) {\n  if (!this.eatContextual(name)) this.unexpected()\n}\n\n// Test whether a semicolon can be inserted at the current position.\n\npp.canInsertSemicolon = function() {\n  return this.type === tt.eof ||\n    this.type === tt.braceR ||\n    lineBreak.test(this.input.slice(this.lastTokEnd, this.start))\n}\n\npp.insertSemicolon = function() {\n  if (this.canInsertSemicolon()) {\n    if (this.options.onInsertedSemicolon)\n      this.options.onInsertedSemicolon(this.lastTokEnd, this.lastTokEndLoc)\n    return true\n  }\n}\n\n// Consume a semicolon, or, failing that, see if we are allowed to\n// pretend that there is a semicolon at this position.\n\npp.semicolon = function() {\n  if (!this.eat(tt.semi) && !this.insertSemicolon()) this.unexpected()\n}\n\npp.afterTrailingComma = function(tokType, notNext) {\n  if (this.type === tokType) {\n    if (this.options.onTrailingComma)\n      this.options.onTrailingComma(this.lastTokStart, this.lastTokStartLoc)\n    if (!notNext)\n      this.next()\n    return true\n  }\n}\n\n// Expect a token of a given type. If found, consume it, otherwise,\n// raise an unexpected token error.\n\npp.expect = function(type) {\n  this.eat(type) || this.unexpected()\n}\n\n// Raise an unexpected token error.\n\npp.unexpected = function(pos) {\n  this.raise(pos != null ? pos : this.start, \"Unexpected token\")\n}\n\nexport function DestructuringErrors() {\n  this.shorthandAssign =\n  this.trailingComma =\n  this.parenthesizedAssign =\n  this.parenthesizedBind =\n  this.doubleProto =\n    -1\n}\n\npp.checkPatternErrors = function(refDestructuringErrors, isAssign) {\n  if (!refDestructuringErrors) return\n  if (refDestructuringErrors.trailingComma > -1)\n    this.raiseRecoverable(refDestructuringErrors.trailingComma, \"Comma is not permitted after the rest element\")\n  let parens = isAssign ? refDestructuringErrors.parenthesizedAssign : refDestructuringErrors.parenthesizedBind\n  if (parens > -1) this.raiseRecoverable(parens, \"Parenthesized pattern\")\n}\n\npp.checkExpressionErrors = function(refDestructuringErrors, andThrow) {\n  if (!refDestructuringErrors) return false\n  let {shorthandAssign, doubleProto} = refDestructuringErrors\n  if (!andThrow) return shorthandAssign >= 0 || doubleProto >= 0\n  if (shorthandAssign >= 0)\n    this.raise(shorthandAssign, \"Shorthand property assignments are valid only in destructuring patterns\")\n  if (doubleProto >= 0)\n    this.raiseRecoverable(doubleProto, \"Redefinition of __proto__ property\")\n}\n\npp.checkYieldAwaitInDefaultParams = function() {\n  if (this.yieldPos && (!this.awaitPos || this.yieldPos < this.awaitPos))\n    this.raise(this.yieldPos, \"Yield expression cannot be a default value\")\n  if (this.awaitPos)\n    this.raise(this.awaitPos, \"Await expression cannot be a default value\")\n}\n\npp.isSimpleAssignTarget = function(expr) {\n  if (expr.type === \"ParenthesizedExpression\")\n    return this.isSimpleAssignTarget(expr.expression)\n  return expr.type === \"Identifier\" || expr.type === \"MemberExpression\"\n}\n", "import {types as tt} from \"./tokentype\"\nimport {<PERSON><PERSON><PERSON>} from \"./state\"\nimport {lineBreak, skipWhiteSpace} from \"./whitespace\"\nimport {isIdentifierStart, isIdentifier<PERSON>har, keywordRelationalOperator} from \"./identifier\"\nimport {has} from \"./util\"\nimport {DestructuringErrors} from \"./parseutil\"\nimport {functionFlags, SCOPE_SIMPLE_CATCH, BIND_SIMPLE_CATCH, BIND_LEXICAL, BIND_VAR, BIND_FUNCTION} from \"./scopeflags\"\n\nconst pp = Parser.prototype\n\n// ### Statement parsing\n\n// Parse a program. Initializes the parser, reads any number of\n// statements, and wraps them in a Program node.  Optionally takes a\n// `program` argument.  If present, the statements will be appended\n// to its body instead of creating a new node.\n\npp.parseTopLevel = function(node) {\n  let exports = {}\n  if (!node.body) node.body = []\n  while (this.type !== tt.eof) {\n    let stmt = this.parseStatement(null, true, exports)\n    node.body.push(stmt)\n  }\n  if (this.inModule)\n    for (let name of Object.keys(this.undefinedExports))\n      this.raiseRecoverable(this.undefinedExports[name].start, `Export '${name}' is not defined`)\n  this.adaptDirectivePrologue(node.body)\n  this.next()\n  if (this.options.ecmaVersion >= 6) {\n    node.sourceType = this.options.sourceType\n  }\n  return this.finishNode(node, \"Program\")\n}\n\nconst loopLabel = {kind: \"loop\"}, switchLabel = {kind: \"switch\"}\n\npp.isLet = function(context) {\n  if (this.options.ecmaVersion < 6 || !this.isContextual(\"let\")) return false\n  skipWhiteSpace.lastIndex = this.pos\n  let skip = skipWhiteSpace.exec(this.input)\n  let next = this.pos + skip[0].length, nextCh = this.input.charCodeAt(next)\n  // For ambiguous cases, determine if a LexicalDeclaration (or only a\n  // Statement) is allowed here. If context is not empty then only a Statement\n  // is allowed. However, `let [` is an explicit negative lookahead for\n  // ExpressionStatement, so special-case it first.\n  if (nextCh === 91) return true // '['\n  if (context) return false\n\n  if (nextCh === 123) return true // '{'\n  if (isIdentifierStart(nextCh, true)) {\n    let pos = next + 1\n    while (isIdentifierChar(this.input.charCodeAt(pos), true)) ++pos\n    let ident = this.input.slice(next, pos)\n    if (!keywordRelationalOperator.test(ident)) return true\n  }\n  return false\n}\n\n// check 'async [no LineTerminator here] function'\n// - 'async /*foo*/ function' is OK.\n// - 'async /*\\n*/ function' is invalid.\npp.isAsyncFunction = function() {\n  if (this.options.ecmaVersion < 8 || !this.isContextual(\"async\"))\n    return false\n\n  skipWhiteSpace.lastIndex = this.pos\n  let skip = skipWhiteSpace.exec(this.input)\n  let next = this.pos + skip[0].length\n  return !lineBreak.test(this.input.slice(this.pos, next)) &&\n    this.input.slice(next, next + 8) === \"function\" &&\n    (next + 8 === this.input.length || !isIdentifierChar(this.input.charAt(next + 8)))\n}\n\n// Parse a single statement.\n//\n// If expecting a statement and finding a slash operator, parse a\n// regular expression literal. This is to handle cases like\n// `if (foo) /blah/.exec(foo)`, where looking at the previous token\n// does not help.\n\npp.parseStatement = function(context, topLevel, exports) {\n  let starttype = this.type, node = this.startNode(), kind\n\n  if (this.isLet(context)) {\n    starttype = tt._var\n    kind = \"let\"\n  }\n\n  // Most types of statements are recognized by the keyword they\n  // start with. Many are trivial to parse, some require a bit of\n  // complexity.\n\n  switch (starttype) {\n  case tt._break: case tt._continue: return this.parseBreakContinueStatement(node, starttype.keyword)\n  case tt._debugger: return this.parseDebuggerStatement(node)\n  case tt._do: return this.parseDoStatement(node)\n  case tt._for: return this.parseForStatement(node)\n  case tt._function:\n    // Function as sole body of either an if statement or a labeled statement\n    // works, but not when it is part of a labeled statement that is the sole\n    // body of an if statement.\n    if ((context && (this.strict || context !== \"if\" && context !== \"label\")) && this.options.ecmaVersion >= 6) this.unexpected()\n    return this.parseFunctionStatement(node, false, !context)\n  case tt._class:\n    if (context) this.unexpected()\n    return this.parseClass(node, true)\n  case tt._if: return this.parseIfStatement(node)\n  case tt._return: return this.parseReturnStatement(node)\n  case tt._switch: return this.parseSwitchStatement(node)\n  case tt._throw: return this.parseThrowStatement(node)\n  case tt._try: return this.parseTryStatement(node)\n  case tt._const: case tt._var:\n    kind = kind || this.value\n    if (context && kind !== \"var\") this.unexpected()\n    return this.parseVarStatement(node, kind)\n  case tt._while: return this.parseWhileStatement(node)\n  case tt._with: return this.parseWithStatement(node)\n  case tt.braceL: return this.parseBlock(true, node)\n  case tt.semi: return this.parseEmptyStatement(node)\n  case tt._export:\n  case tt._import:\n    if (!this.options.allowImportExportEverywhere) {\n      if (!topLevel)\n        this.raise(this.start, \"'import' and 'export' may only appear at the top level\")\n      if (!this.inModule)\n        this.raise(this.start, \"'import' and 'export' may appear only with 'sourceType: module'\")\n    }\n    return starttype === tt._import ? this.parseImport(node) : this.parseExport(node, exports)\n\n    // If the statement does not start with a statement keyword or a\n    // brace, it's an ExpressionStatement or LabeledStatement. We\n    // simply start parsing an expression, and afterwards, if the\n    // next token is a colon and the expression was a simple\n    // Identifier node, we switch to interpreting it as a label.\n  default:\n    if (this.isAsyncFunction()) {\n      if (context) this.unexpected()\n      this.next()\n      return this.parseFunctionStatement(node, true, !context)\n    }\n\n    let maybeName = this.value, expr = this.parseExpression()\n    if (starttype === tt.name && expr.type === \"Identifier\" && this.eat(tt.colon))\n      return this.parseLabeledStatement(node, maybeName, expr, context)\n    else return this.parseExpressionStatement(node, expr)\n  }\n}\n\npp.parseBreakContinueStatement = function(node, keyword) {\n  let isBreak = keyword === \"break\"\n  this.next()\n  if (this.eat(tt.semi) || this.insertSemicolon()) node.label = null\n  else if (this.type !== tt.name) this.unexpected()\n  else {\n    node.label = this.parseIdent()\n    this.semicolon()\n  }\n\n  // Verify that there is an actual destination to break or\n  // continue to.\n  let i = 0\n  for (; i < this.labels.length; ++i) {\n    let lab = this.labels[i]\n    if (node.label == null || lab.name === node.label.name) {\n      if (lab.kind != null && (isBreak || lab.kind === \"loop\")) break\n      if (node.label && isBreak) break\n    }\n  }\n  if (i === this.labels.length) this.raise(node.start, \"Unsyntactic \" + keyword)\n  return this.finishNode(node, isBreak ? \"BreakStatement\" : \"ContinueStatement\")\n}\n\npp.parseDebuggerStatement = function(node) {\n  this.next()\n  this.semicolon()\n  return this.finishNode(node, \"DebuggerStatement\")\n}\n\npp.parseDoStatement = function(node) {\n  this.next()\n  this.labels.push(loopLabel)\n  node.body = this.parseStatement(\"do\")\n  this.labels.pop()\n  this.expect(tt._while)\n  node.test = this.parseParenExpression()\n  if (this.options.ecmaVersion >= 6)\n    this.eat(tt.semi)\n  else\n    this.semicolon()\n  return this.finishNode(node, \"DoWhileStatement\")\n}\n\n// Disambiguating between a `for` and a `for`/`in` or `for`/`of`\n// loop is non-trivial. Basically, we have to parse the init `var`\n// statement or expression, disallowing the `in` operator (see\n// the second parameter to `parseExpression`), and then check\n// whether the next token is `in` or `of`. When there is no init\n// part (semicolon immediately after the opening parenthesis), it\n// is a regular `for` loop.\n\npp.parseForStatement = function(node) {\n  this.next()\n  let awaitAt = (this.options.ecmaVersion >= 9 && (this.inAsync || (!this.inFunction && this.options.allowAwaitOutsideFunction)) && this.eatContextual(\"await\")) ? this.lastTokStart : -1\n  this.labels.push(loopLabel)\n  this.enterScope(0)\n  this.expect(tt.parenL)\n  if (this.type === tt.semi) {\n    if (awaitAt > -1) this.unexpected(awaitAt)\n    return this.parseFor(node, null)\n  }\n  let isLet = this.isLet()\n  if (this.type === tt._var || this.type === tt._const || isLet) {\n    let init = this.startNode(), kind = isLet ? \"let\" : this.value\n    this.next()\n    this.parseVar(init, true, kind)\n    this.finishNode(init, \"VariableDeclaration\")\n    if ((this.type === tt._in || (this.options.ecmaVersion >= 6 && this.isContextual(\"of\"))) && init.declarations.length === 1 &&\n        !(kind !== \"var\" && init.declarations[0].init)) {\n      if (this.options.ecmaVersion >= 9) {\n        if (this.type === tt._in) {\n          if (awaitAt > -1) this.unexpected(awaitAt)\n        } else node.await = awaitAt > -1\n      }\n      return this.parseForIn(node, init)\n    }\n    if (awaitAt > -1) this.unexpected(awaitAt)\n    return this.parseFor(node, init)\n  }\n  let refDestructuringErrors = new DestructuringErrors\n  let init = this.parseExpression(true, refDestructuringErrors)\n  if (this.type === tt._in || (this.options.ecmaVersion >= 6 && this.isContextual(\"of\"))) {\n    if (this.options.ecmaVersion >= 9) {\n      if (this.type === tt._in) {\n        if (awaitAt > -1) this.unexpected(awaitAt)\n      } else node.await = awaitAt > -1\n    }\n    this.toAssignable(init, false, refDestructuringErrors)\n    this.checkLVal(init)\n    return this.parseForIn(node, init)\n  } else {\n    this.checkExpressionErrors(refDestructuringErrors, true)\n  }\n  if (awaitAt > -1) this.unexpected(awaitAt)\n  return this.parseFor(node, init)\n}\n\npp.parseFunctionStatement = function(node, isAsync, declarationPosition) {\n  this.next()\n  return this.parseFunction(node, FUNC_STATEMENT | (declarationPosition ? 0 : FUNC_HANGING_STATEMENT), false, isAsync)\n}\n\npp.parseIfStatement = function(node) {\n  this.next()\n  node.test = this.parseParenExpression()\n  // allow function declarations in branches, but only in non-strict mode\n  node.consequent = this.parseStatement(\"if\")\n  node.alternate = this.eat(tt._else) ? this.parseStatement(\"if\") : null\n  return this.finishNode(node, \"IfStatement\")\n}\n\npp.parseReturnStatement = function(node) {\n  if (!this.inFunction && !this.options.allowReturnOutsideFunction)\n    this.raise(this.start, \"'return' outside of function\")\n  this.next()\n\n  // In `return` (and `break`/`continue`), the keywords with\n  // optional arguments, we eagerly look for a semicolon or the\n  // possibility to insert one.\n\n  if (this.eat(tt.semi) || this.insertSemicolon()) node.argument = null\n  else { node.argument = this.parseExpression(); this.semicolon() }\n  return this.finishNode(node, \"ReturnStatement\")\n}\n\npp.parseSwitchStatement = function(node) {\n  this.next()\n  node.discriminant = this.parseParenExpression()\n  node.cases = []\n  this.expect(tt.braceL)\n  this.labels.push(switchLabel)\n  this.enterScope(0)\n\n  // Statements under must be grouped (by label) in SwitchCase\n  // nodes. `cur` is used to keep the node that we are currently\n  // adding statements to.\n\n  let cur\n  for (let sawDefault = false; this.type !== tt.braceR;) {\n    if (this.type === tt._case || this.type === tt._default) {\n      let isCase = this.type === tt._case\n      if (cur) this.finishNode(cur, \"SwitchCase\")\n      node.cases.push(cur = this.startNode())\n      cur.consequent = []\n      this.next()\n      if (isCase) {\n        cur.test = this.parseExpression()\n      } else {\n        if (sawDefault) this.raiseRecoverable(this.lastTokStart, \"Multiple default clauses\")\n        sawDefault = true\n        cur.test = null\n      }\n      this.expect(tt.colon)\n    } else {\n      if (!cur) this.unexpected()\n      cur.consequent.push(this.parseStatement(null))\n    }\n  }\n  this.exitScope()\n  if (cur) this.finishNode(cur, \"SwitchCase\")\n  this.next() // Closing brace\n  this.labels.pop()\n  return this.finishNode(node, \"SwitchStatement\")\n}\n\npp.parseThrowStatement = function(node) {\n  this.next()\n  if (lineBreak.test(this.input.slice(this.lastTokEnd, this.start)))\n    this.raise(this.lastTokEnd, \"Illegal newline after throw\")\n  node.argument = this.parseExpression()\n  this.semicolon()\n  return this.finishNode(node, \"ThrowStatement\")\n}\n\n// Reused empty array added for node fields that are always empty.\n\nconst empty = []\n\npp.parseTryStatement = function(node) {\n  this.next()\n  node.block = this.parseBlock()\n  node.handler = null\n  if (this.type === tt._catch) {\n    let clause = this.startNode()\n    this.next()\n    if (this.eat(tt.parenL)) {\n      clause.param = this.parseBindingAtom()\n      let simple = clause.param.type === \"Identifier\"\n      this.enterScope(simple ? SCOPE_SIMPLE_CATCH : 0)\n      this.checkLVal(clause.param, simple ? BIND_SIMPLE_CATCH : BIND_LEXICAL)\n      this.expect(tt.parenR)\n    } else {\n      if (this.options.ecmaVersion < 10) this.unexpected()\n      clause.param = null\n      this.enterScope(0)\n    }\n    clause.body = this.parseBlock(false)\n    this.exitScope()\n    node.handler = this.finishNode(clause, \"CatchClause\")\n  }\n  node.finalizer = this.eat(tt._finally) ? this.parseBlock() : null\n  if (!node.handler && !node.finalizer)\n    this.raise(node.start, \"Missing catch or finally clause\")\n  return this.finishNode(node, \"TryStatement\")\n}\n\npp.parseVarStatement = function(node, kind) {\n  this.next()\n  this.parseVar(node, false, kind)\n  this.semicolon()\n  return this.finishNode(node, \"VariableDeclaration\")\n}\n\npp.parseWhileStatement = function(node) {\n  this.next()\n  node.test = this.parseParenExpression()\n  this.labels.push(loopLabel)\n  node.body = this.parseStatement(\"while\")\n  this.labels.pop()\n  return this.finishNode(node, \"WhileStatement\")\n}\n\npp.parseWithStatement = function(node) {\n  if (this.strict) this.raise(this.start, \"'with' in strict mode\")\n  this.next()\n  node.object = this.parseParenExpression()\n  node.body = this.parseStatement(\"with\")\n  return this.finishNode(node, \"WithStatement\")\n}\n\npp.parseEmptyStatement = function(node) {\n  this.next()\n  return this.finishNode(node, \"EmptyStatement\")\n}\n\npp.parseLabeledStatement = function(node, maybeName, expr, context) {\n  for (let label of this.labels)\n    if (label.name === maybeName)\n      this.raise(expr.start, \"Label '\" + maybeName + \"' is already declared\")\n  let kind = this.type.isLoop ? \"loop\" : this.type === tt._switch ? \"switch\" : null\n  for (let i = this.labels.length - 1; i >= 0; i--) {\n    let label = this.labels[i]\n    if (label.statementStart === node.start) {\n      // Update information about previous labels on this node\n      label.statementStart = this.start\n      label.kind = kind\n    } else break\n  }\n  this.labels.push({name: maybeName, kind, statementStart: this.start})\n  node.body = this.parseStatement(context ? context.indexOf(\"label\") === -1 ? context + \"label\" : context : \"label\")\n  this.labels.pop()\n  node.label = expr\n  return this.finishNode(node, \"LabeledStatement\")\n}\n\npp.parseExpressionStatement = function(node, expr) {\n  node.expression = expr\n  this.semicolon()\n  return this.finishNode(node, \"ExpressionStatement\")\n}\n\n// Parse a semicolon-enclosed block of statements, handling `\"use\n// strict\"` declarations when `allowStrict` is true (used for\n// function bodies).\n\npp.parseBlock = function(createNewLexicalScope = true, node = this.startNode()) {\n  node.body = []\n  this.expect(tt.braceL)\n  if (createNewLexicalScope) this.enterScope(0)\n  while (!this.eat(tt.braceR)) {\n    let stmt = this.parseStatement(null)\n    node.body.push(stmt)\n  }\n  if (createNewLexicalScope) this.exitScope()\n  return this.finishNode(node, \"BlockStatement\")\n}\n\n// Parse a regular `for` loop. The disambiguation code in\n// `parseStatement` will already have parsed the init statement or\n// expression.\n\npp.parseFor = function(node, init) {\n  node.init = init\n  this.expect(tt.semi)\n  node.test = this.type === tt.semi ? null : this.parseExpression()\n  this.expect(tt.semi)\n  node.update = this.type === tt.parenR ? null : this.parseExpression()\n  this.expect(tt.parenR)\n  node.body = this.parseStatement(\"for\")\n  this.exitScope()\n  this.labels.pop()\n  return this.finishNode(node, \"ForStatement\")\n}\n\n// Parse a `for`/`in` and `for`/`of` loop, which are almost\n// same from parser's perspective.\n\npp.parseForIn = function(node, init) {\n  let type = this.type === tt._in ? \"ForInStatement\" : \"ForOfStatement\"\n  this.next()\n  if (type === \"ForInStatement\") {\n    if (init.type === \"AssignmentPattern\" ||\n      (init.type === \"VariableDeclaration\" && init.declarations[0].init != null &&\n       (this.strict || init.declarations[0].id.type !== \"Identifier\")))\n      this.raise(init.start, \"Invalid assignment in for-in loop head\")\n  }\n  node.left = init\n  node.right = type === \"ForInStatement\" ? this.parseExpression() : this.parseMaybeAssign()\n  this.expect(tt.parenR)\n  node.body = this.parseStatement(\"for\")\n  this.exitScope()\n  this.labels.pop()\n  return this.finishNode(node, type)\n}\n\n// Parse a list of variable declarations.\n\npp.parseVar = function(node, isFor, kind) {\n  node.declarations = []\n  node.kind = kind\n  for (;;) {\n    let decl = this.startNode()\n    this.parseVarId(decl, kind)\n    if (this.eat(tt.eq)) {\n      decl.init = this.parseMaybeAssign(isFor)\n    } else if (kind === \"const\" && !(this.type === tt._in || (this.options.ecmaVersion >= 6 && this.isContextual(\"of\")))) {\n      this.unexpected()\n    } else if (decl.id.type !== \"Identifier\" && !(isFor && (this.type === tt._in || this.isContextual(\"of\")))) {\n      this.raise(this.lastTokEnd, \"Complex binding patterns require an initialization value\")\n    } else {\n      decl.init = null\n    }\n    node.declarations.push(this.finishNode(decl, \"VariableDeclarator\"))\n    if (!this.eat(tt.comma)) break\n  }\n  return node\n}\n\npp.parseVarId = function(decl, kind) {\n  if ((kind === \"const\" || kind === \"let\") && this.isContextual(\"let\")) {\n    this.raiseRecoverable(this.start, \"let is disallowed as a lexically bound name\")\n  }\n  decl.id = this.parseBindingAtom()\n  this.checkLVal(decl.id, kind === \"var\" ? BIND_VAR : BIND_LEXICAL, false)\n}\n\nconst FUNC_STATEMENT = 1, FUNC_HANGING_STATEMENT = 2, FUNC_NULLABLE_ID = 4\n\n// Parse a function declaration or literal (depending on the\n// `statement & FUNC_STATEMENT`).\n\n// Remove `allowExpressionBody` for 7.0.0, as it is only called with false\npp.parseFunction = function(node, statement, allowExpressionBody, isAsync) {\n  this.initFunction(node)\n  if (this.options.ecmaVersion >= 9 || this.options.ecmaVersion >= 6 && !isAsync) {\n    if (this.type === tt.star && (statement & FUNC_HANGING_STATEMENT))\n      this.unexpected()\n    node.generator = this.eat(tt.star)\n  }\n  if (this.options.ecmaVersion >= 8)\n    node.async = !!isAsync\n\n  if (statement & FUNC_STATEMENT) {\n    node.id = (statement & FUNC_NULLABLE_ID) && this.type !== tt.name ? null : this.parseIdent()\n    if (node.id && !(statement & FUNC_HANGING_STATEMENT))\n      // If it is a regular function declaration in sloppy mode, then it is\n      // subject to Annex B semantics (BIND_FUNCTION). Otherwise, the binding\n      // mode depends on properties of the current scope (see\n      // treatFunctionsAsVar).\n      this.checkLVal(node.id, (this.strict || node.generator || node.async) ? this.treatFunctionsAsVar ? BIND_VAR : BIND_LEXICAL : BIND_FUNCTION)\n  }\n\n  let oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos\n  this.yieldPos = 0\n  this.awaitPos = 0\n  this.awaitIdentPos = 0\n  this.enterScope(functionFlags(node.async, node.generator))\n\n  if (!(statement & FUNC_STATEMENT))\n    node.id = this.type === tt.name ? this.parseIdent() : null\n\n  this.parseFunctionParams(node)\n  this.parseFunctionBody(node, allowExpressionBody, false)\n\n  this.yieldPos = oldYieldPos\n  this.awaitPos = oldAwaitPos\n  this.awaitIdentPos = oldAwaitIdentPos\n  return this.finishNode(node, (statement & FUNC_STATEMENT) ? \"FunctionDeclaration\" : \"FunctionExpression\")\n}\n\npp.parseFunctionParams = function(node) {\n  this.expect(tt.parenL)\n  node.params = this.parseBindingList(tt.parenR, false, this.options.ecmaVersion >= 8)\n  this.checkYieldAwaitInDefaultParams()\n}\n\n// Parse a class declaration or literal (depending on the\n// `isStatement` parameter).\n\npp.parseClass = function(node, isStatement) {\n  this.next()\n\n  // ecma-262 14.6 Class Definitions\n  // A class definition is always strict mode code.\n  const oldStrict = this.strict\n  this.strict = true\n\n  this.parseClassId(node, isStatement)\n  this.parseClassSuper(node)\n  let classBody = this.startNode()\n  let hadConstructor = false\n  classBody.body = []\n  this.expect(tt.braceL)\n  while (!this.eat(tt.braceR)) {\n    const element = this.parseClassElement(node.superClass !== null)\n    if (element) {\n      classBody.body.push(element)\n      if (element.type === \"MethodDefinition\" && element.kind === \"constructor\") {\n        if (hadConstructor) this.raise(element.start, \"Duplicate constructor in the same class\")\n        hadConstructor = true\n      }\n    }\n  }\n  node.body = this.finishNode(classBody, \"ClassBody\")\n  this.strict = oldStrict\n  return this.finishNode(node, isStatement ? \"ClassDeclaration\" : \"ClassExpression\")\n}\n\npp.parseClassElement = function(constructorAllowsSuper) {\n  if (this.eat(tt.semi)) return null\n\n  let method = this.startNode()\n  const tryContextual = (k, noLineBreak = false) => {\n    const start = this.start, startLoc = this.startLoc\n    if (!this.eatContextual(k)) return false\n    if (this.type !== tt.parenL && (!noLineBreak || !this.canInsertSemicolon())) return true\n    if (method.key) this.unexpected()\n    method.computed = false\n    method.key = this.startNodeAt(start, startLoc)\n    method.key.name = k\n    this.finishNode(method.key, \"Identifier\")\n    return false\n  }\n\n  method.kind = \"method\"\n  method.static = tryContextual(\"static\")\n  let isGenerator = this.eat(tt.star)\n  let isAsync = false\n  if (!isGenerator) {\n    if (this.options.ecmaVersion >= 8 && tryContextual(\"async\", true)) {\n      isAsync = true\n      isGenerator = this.options.ecmaVersion >= 9 && this.eat(tt.star)\n    } else if (tryContextual(\"get\")) {\n      method.kind = \"get\"\n    } else if (tryContextual(\"set\")) {\n      method.kind = \"set\"\n    }\n  }\n  if (!method.key) this.parsePropertyName(method)\n  let {key} = method\n  let allowsDirectSuper = false\n  if (!method.computed && !method.static && (key.type === \"Identifier\" && key.name === \"constructor\" ||\n      key.type === \"Literal\" && key.value === \"constructor\")) {\n    if (method.kind !== \"method\") this.raise(key.start, \"Constructor can't have get/set modifier\")\n    if (isGenerator) this.raise(key.start, \"Constructor can't be a generator\")\n    if (isAsync) this.raise(key.start, \"Constructor can't be an async method\")\n    method.kind = \"constructor\"\n    allowsDirectSuper = constructorAllowsSuper\n  } else if (method.static && key.type === \"Identifier\" && key.name === \"prototype\") {\n    this.raise(key.start, \"Classes may not have a static property named prototype\")\n  }\n  this.parseClassMethod(method, isGenerator, isAsync, allowsDirectSuper)\n  if (method.kind === \"get\" && method.value.params.length !== 0)\n    this.raiseRecoverable(method.value.start, \"getter should have no params\")\n  if (method.kind === \"set\" && method.value.params.length !== 1)\n    this.raiseRecoverable(method.value.start, \"setter should have exactly one param\")\n  if (method.kind === \"set\" && method.value.params[0].type === \"RestElement\")\n    this.raiseRecoverable(method.value.params[0].start, \"Setter cannot use rest params\")\n  return method\n}\n\npp.parseClassMethod = function(method, isGenerator, isAsync, allowsDirectSuper) {\n  method.value = this.parseMethod(isGenerator, isAsync, allowsDirectSuper)\n  return this.finishNode(method, \"MethodDefinition\")\n}\n\npp.parseClassId = function(node, isStatement) {\n  if (this.type === tt.name) {\n    node.id = this.parseIdent()\n    if (isStatement)\n      this.checkLVal(node.id, BIND_LEXICAL, false)\n  } else {\n    if (isStatement === true)\n      this.unexpected()\n    node.id = null\n  }\n}\n\npp.parseClassSuper = function(node) {\n  node.superClass = this.eat(tt._extends) ? this.parseExprSubscripts() : null\n}\n\n// Parses module export declaration.\n\npp.parseExport = function(node, exports) {\n  this.next()\n  // export * from '...'\n  if (this.eat(tt.star)) {\n    this.expectContextual(\"from\")\n    if (this.type !== tt.string) this.unexpected()\n    node.source = this.parseExprAtom()\n    this.semicolon()\n    return this.finishNode(node, \"ExportAllDeclaration\")\n  }\n  if (this.eat(tt._default)) { // export default ...\n    this.checkExport(exports, \"default\", this.lastTokStart)\n    let isAsync\n    if (this.type === tt._function || (isAsync = this.isAsyncFunction())) {\n      let fNode = this.startNode()\n      this.next()\n      if (isAsync) this.next()\n      node.declaration = this.parseFunction(fNode, FUNC_STATEMENT | FUNC_NULLABLE_ID, false, isAsync)\n    } else if (this.type === tt._class) {\n      let cNode = this.startNode()\n      node.declaration = this.parseClass(cNode, \"nullableID\")\n    } else {\n      node.declaration = this.parseMaybeAssign()\n      this.semicolon()\n    }\n    return this.finishNode(node, \"ExportDefaultDeclaration\")\n  }\n  // export var|const|let|function|class ...\n  if (this.shouldParseExportStatement()) {\n    node.declaration = this.parseStatement(null)\n    if (node.declaration.type === \"VariableDeclaration\")\n      this.checkVariableExport(exports, node.declaration.declarations)\n    else\n      this.checkExport(exports, node.declaration.id.name, node.declaration.id.start)\n    node.specifiers = []\n    node.source = null\n  } else { // export { x, y as z } [from '...']\n    node.declaration = null\n    node.specifiers = this.parseExportSpecifiers(exports)\n    if (this.eatContextual(\"from\")) {\n      if (this.type !== tt.string) this.unexpected()\n      node.source = this.parseExprAtom()\n    } else {\n      for (let spec of node.specifiers) {\n        // check for keywords used as local names\n        this.checkUnreserved(spec.local)\n        // check if export is defined\n        this.checkLocalExport(spec.local)\n      }\n\n      node.source = null\n    }\n    this.semicolon()\n  }\n  return this.finishNode(node, \"ExportNamedDeclaration\")\n}\n\npp.checkExport = function(exports, name, pos) {\n  if (!exports) return\n  if (has(exports, name))\n    this.raiseRecoverable(pos, \"Duplicate export '\" + name + \"'\")\n  exports[name] = true\n}\n\npp.checkPatternExport = function(exports, pat) {\n  let type = pat.type\n  if (type === \"Identifier\")\n    this.checkExport(exports, pat.name, pat.start)\n  else if (type === \"ObjectPattern\")\n    for (let prop of pat.properties)\n      this.checkPatternExport(exports, prop)\n  else if (type === \"ArrayPattern\")\n    for (let elt of pat.elements) {\n      if (elt) this.checkPatternExport(exports, elt)\n    }\n  else if (type === \"Property\")\n    this.checkPatternExport(exports, pat.value)\n  else if (type === \"AssignmentPattern\")\n    this.checkPatternExport(exports, pat.left)\n  else if (type === \"RestElement\")\n    this.checkPatternExport(exports, pat.argument)\n  else if (type === \"ParenthesizedExpression\")\n    this.checkPatternExport(exports, pat.expression)\n}\n\npp.checkVariableExport = function(exports, decls) {\n  if (!exports) return\n  for (let decl of decls)\n    this.checkPatternExport(exports, decl.id)\n}\n\npp.shouldParseExportStatement = function() {\n  return this.type.keyword === \"var\" ||\n    this.type.keyword === \"const\" ||\n    this.type.keyword === \"class\" ||\n    this.type.keyword === \"function\" ||\n    this.isLet() ||\n    this.isAsyncFunction()\n}\n\n// Parses a comma-separated list of module exports.\n\npp.parseExportSpecifiers = function(exports) {\n  let nodes = [], first = true\n  // export { x, y as z } [from '...']\n  this.expect(tt.braceL)\n  while (!this.eat(tt.braceR)) {\n    if (!first) {\n      this.expect(tt.comma)\n      if (this.afterTrailingComma(tt.braceR)) break\n    } else first = false\n\n    let node = this.startNode()\n    node.local = this.parseIdent(true)\n    node.exported = this.eatContextual(\"as\") ? this.parseIdent(true) : node.local\n    this.checkExport(exports, node.exported.name, node.exported.start)\n    nodes.push(this.finishNode(node, \"ExportSpecifier\"))\n  }\n  return nodes\n}\n\n// Parses import declaration.\n\npp.parseImport = function(node) {\n  this.next()\n  // import '...'\n  if (this.type === tt.string) {\n    node.specifiers = empty\n    node.source = this.parseExprAtom()\n  } else {\n    node.specifiers = this.parseImportSpecifiers()\n    this.expectContextual(\"from\")\n    node.source = this.type === tt.string ? this.parseExprAtom() : this.unexpected()\n  }\n  this.semicolon()\n  return this.finishNode(node, \"ImportDeclaration\")\n}\n\n// Parses a comma-separated list of module imports.\n\npp.parseImportSpecifiers = function() {\n  let nodes = [], first = true\n  if (this.type === tt.name) {\n    // import defaultObj, { x, y as z } from '...'\n    let node = this.startNode()\n    node.local = this.parseIdent()\n    this.checkLVal(node.local, BIND_LEXICAL)\n    nodes.push(this.finishNode(node, \"ImportDefaultSpecifier\"))\n    if (!this.eat(tt.comma)) return nodes\n  }\n  if (this.type === tt.star) {\n    let node = this.startNode()\n    this.next()\n    this.expectContextual(\"as\")\n    node.local = this.parseIdent()\n    this.checkLVal(node.local, BIND_LEXICAL)\n    nodes.push(this.finishNode(node, \"ImportNamespaceSpecifier\"))\n    return nodes\n  }\n  this.expect(tt.braceL)\n  while (!this.eat(tt.braceR)) {\n    if (!first) {\n      this.expect(tt.comma)\n      if (this.afterTrailingComma(tt.braceR)) break\n    } else first = false\n\n    let node = this.startNode()\n    node.imported = this.parseIdent(true)\n    if (this.eatContextual(\"as\")) {\n      node.local = this.parseIdent()\n    } else {\n      this.checkUnreserved(node.imported)\n      node.local = node.imported\n    }\n    this.checkLVal(node.local, BIND_LEXICAL)\n    nodes.push(this.finishNode(node, \"ImportSpecifier\"))\n  }\n  return nodes\n}\n\n// Set `ExpressionStatement#directive` property for directive prologues.\npp.adaptDirectivePrologue = function(statements) {\n  for (let i = 0; i < statements.length && this.isDirectiveCandidate(statements[i]); ++i) {\n    statements[i].directive = statements[i].expression.raw.slice(1, -1)\n  }\n}\npp.isDirectiveCandidate = function(statement) {\n  return (\n    statement.type === \"ExpressionStatement\" &&\n    statement.expression.type === \"Literal\" &&\n    typeof statement.expression.value === \"string\" &&\n    // Reject parenthesized strings.\n    (this.input[statement.start] === \"\\\"\" || this.input[statement.start] === \"'\")\n  )\n}\n", "import {types as tt} from \"./tokentype\"\nimport {<PERSON><PERSON><PERSON>} from \"./state\"\nimport {has} from \"./util\"\nimport {BIND_NONE, BIND_OUTSIDE} from \"./scopeflags\"\n\nconst pp = Parser.prototype\n\n// Convert existing expression atom to assignable pattern\n// if possible.\n\npp.toAssignable = function(node, isBinding, refDestructuringErrors) {\n  if (this.options.ecmaVersion >= 6 && node) {\n    switch (node.type) {\n    case \"Identifier\":\n      if (this.inAsync && node.name === \"await\")\n        this.raise(node.start, \"Cannot use 'await' as identifier inside an async function\")\n      break\n\n    case \"ObjectPattern\":\n    case \"ArrayPattern\":\n    case \"RestElement\":\n      break\n\n    case \"ObjectExpression\":\n      node.type = \"ObjectPattern\"\n      if (refDestructuringErrors) this.checkPatternErrors(refDestructuringErrors, true)\n      for (let prop of node.properties) {\n        this.toAssignable(prop, isBinding)\n        // Early error:\n        //   AssignmentRestProperty[<PERSON>eld, <PERSON>wait] :\n        //     `...` DestructuringAssignmentTarget[<PERSON><PERSON>, Await]\n        //\n        //   It is a Syntax Error if |DestructuringAssignmentTarget| is an |ArrayLiteral| or an |ObjectLiteral|.\n        if (\n          prop.type === \"RestElement\" &&\n          (prop.argument.type === \"ArrayPattern\" || prop.argument.type === \"ObjectPattern\")\n        ) {\n          this.raise(prop.argument.start, \"Unexpected token\")\n        }\n      }\n      break\n\n    case \"Property\":\n      // AssignmentProperty has type === \"Property\"\n      if (node.kind !== \"init\") this.raise(node.key.start, \"Object pattern can't contain getter or setter\")\n      this.toAssignable(node.value, isBinding)\n      break\n\n    case \"ArrayExpression\":\n      node.type = \"ArrayPattern\"\n      if (refDestructuringErrors) this.checkPatternErrors(refDestructuringErrors, true)\n      this.toAssignableList(node.elements, isBinding)\n      break\n\n    case \"SpreadElement\":\n      node.type = \"RestElement\"\n      this.toAssignable(node.argument, isBinding)\n      if (node.argument.type === \"AssignmentPattern\")\n        this.raise(node.argument.start, \"Rest elements cannot have a default value\")\n      break\n\n    case \"AssignmentExpression\":\n      if (node.operator !== \"=\") this.raise(node.left.end, \"Only '=' operator can be used for specifying default value.\")\n      node.type = \"AssignmentPattern\"\n      delete node.operator\n      this.toAssignable(node.left, isBinding)\n      // falls through to AssignmentPattern\n\n    case \"AssignmentPattern\":\n      break\n\n    case \"ParenthesizedExpression\":\n      this.toAssignable(node.expression, isBinding, refDestructuringErrors)\n      break\n\n    case \"MemberExpression\":\n      if (!isBinding) break\n\n    default:\n      this.raise(node.start, \"Assigning to rvalue\")\n    }\n  } else if (refDestructuringErrors) this.checkPatternErrors(refDestructuringErrors, true)\n  return node\n}\n\n// Convert list of expression atoms to binding list.\n\npp.toAssignableList = function(exprList, isBinding) {\n  let end = exprList.length\n  for (let i = 0; i < end; i++) {\n    let elt = exprList[i]\n    if (elt) this.toAssignable(elt, isBinding)\n  }\n  if (end) {\n    let last = exprList[end - 1]\n    if (this.options.ecmaVersion === 6 && isBinding && last && last.type === \"RestElement\" && last.argument.type !== \"Identifier\")\n      this.unexpected(last.argument.start)\n  }\n  return exprList\n}\n\n// Parses spread element.\n\npp.parseSpread = function(refDestructuringErrors) {\n  let node = this.startNode()\n  this.next()\n  node.argument = this.parseMaybeAssign(false, refDestructuringErrors)\n  return this.finishNode(node, \"SpreadElement\")\n}\n\npp.parseRestBinding = function() {\n  let node = this.startNode()\n  this.next()\n\n  // RestElement inside of a function parameter must be an identifier\n  if (this.options.ecmaVersion === 6 && this.type !== tt.name)\n    this.unexpected()\n\n  node.argument = this.parseBindingAtom()\n\n  return this.finishNode(node, \"RestElement\")\n}\n\n// Parses lvalue (assignable) atom.\n\npp.parseBindingAtom = function() {\n  if (this.options.ecmaVersion >= 6) {\n    switch (this.type) {\n    case tt.bracketL:\n      let node = this.startNode()\n      this.next()\n      node.elements = this.parseBindingList(tt.bracketR, true, true)\n      return this.finishNode(node, \"ArrayPattern\")\n\n    case tt.braceL:\n      return this.parseObj(true)\n    }\n  }\n  return this.parseIdent()\n}\n\npp.parseBindingList = function(close, allowEmpty, allowTrailingComma) {\n  let elts = [], first = true\n  while (!this.eat(close)) {\n    if (first) first = false\n    else this.expect(tt.comma)\n    if (allowEmpty && this.type === tt.comma) {\n      elts.push(null)\n    } else if (allowTrailingComma && this.afterTrailingComma(close)) {\n      break\n    } else if (this.type === tt.ellipsis) {\n      let rest = this.parseRestBinding()\n      this.parseBindingListItem(rest)\n      elts.push(rest)\n      if (this.type === tt.comma) this.raise(this.start, \"Comma is not permitted after the rest element\")\n      this.expect(close)\n      break\n    } else {\n      let elem = this.parseMaybeDefault(this.start, this.startLoc)\n      this.parseBindingListItem(elem)\n      elts.push(elem)\n    }\n  }\n  return elts\n}\n\npp.parseBindingListItem = function(param) {\n  return param\n}\n\n// Parses assignment pattern around given atom if possible.\n\npp.parseMaybeDefault = function(startPos, startLoc, left) {\n  left = left || this.parseBindingAtom()\n  if (this.options.ecmaVersion < 6 || !this.eat(tt.eq)) return left\n  let node = this.startNodeAt(startPos, startLoc)\n  node.left = left\n  node.right = this.parseMaybeAssign()\n  return this.finishNode(node, \"AssignmentPattern\")\n}\n\n// Verify that a node is an lval — something that can be assigned\n// to.\n// bindingType can be either:\n// 'var' indicating that the lval creates a 'var' binding\n// 'let' indicating that the lval creates a lexical ('let' or 'const') binding\n// 'none' indicating that the binding should be checked for illegal identifiers, but not for duplicate references\n\npp.checkLVal = function(expr, bindingType = BIND_NONE, checkClashes) {\n  switch (expr.type) {\n  case \"Identifier\":\n    if (this.strict && this.reservedWordsStrictBind.test(expr.name))\n      this.raiseRecoverable(expr.start, (bindingType ? \"Binding \" : \"Assigning to \") + expr.name + \" in strict mode\")\n    if (checkClashes) {\n      if (has(checkClashes, expr.name))\n        this.raiseRecoverable(expr.start, \"Argument name clash\")\n      checkClashes[expr.name] = true\n    }\n    if (bindingType !== BIND_NONE && bindingType !== BIND_OUTSIDE) this.declareName(expr.name, bindingType, expr.start)\n    break\n\n  case \"MemberExpression\":\n    if (bindingType) this.raiseRecoverable(expr.start, \"Binding member expression\")\n    break\n\n  case \"ObjectPattern\":\n    for (let prop of expr.properties)\n      this.checkLVal(prop, bindingType, checkClashes)\n    break\n\n  case \"Property\":\n    // AssignmentProperty has type === \"Property\"\n    this.checkLVal(expr.value, bindingType, checkClashes)\n    break\n\n  case \"ArrayPattern\":\n    for (let elem of expr.elements) {\n      if (elem) this.checkLVal(elem, bindingType, checkClashes)\n    }\n    break\n\n  case \"AssignmentPattern\":\n    this.checkLVal(expr.left, bindingType, checkClashes)\n    break\n\n  case \"RestElement\":\n    this.checkLVal(expr.argument, bindingType, checkClashes)\n    break\n\n  case \"ParenthesizedExpression\":\n    this.checkLVal(expr.expression, bindingType, checkClashes)\n    break\n\n  default:\n    this.raise(expr.start, (bindingType ? \"Binding\" : \"Assigning to\") + \" rvalue\")\n  }\n}\n", "// A recursive descent parser operates by defining functions for all\n// syntactic elements, and recursively calling those, each function\n// advancing the input stream and returning an AST node. Precedence\n// of constructs (for example, the fact that `!x[1]` means `!(x[1])`\n// instead of `(!x)[1]` is handled by the fact that the parser\n// function that parses unary prefix operators is called first, and\n// in turn calls the function that parses `[]` subscripts — that\n// way, it'll receive the node for `x[1]` already parsed, and wraps\n// *that* in the unary operator node.\n//\n// Acorn uses an [operator precedence parser][opp] to handle binary\n// operator precedence, because it is much more compact than using\n// the technique outlined above, which uses different, nesting\n// functions to specify precedence, for all of the ten binary\n// precedence levels that Java<PERSON> defines.\n//\n// [opp]: http://en.wikipedia.org/wiki/Operator-precedence_parser\n\nimport {types as tt} from \"./tokentype\"\nimport {Parser} from \"./state\"\nimport {DestructuringErrors} from \"./parseutil\"\nimport {lineBreak} from \"./whitespace\"\nimport {functionFlags, SCOPE_ARROW, SCOPE_SUPER, SCOPE_DIRECT_SUPER, BIND_OUTSIDE, BIND_VAR} from \"./scopeflags\"\n\nconst pp = Parser.prototype\n\n// Check if property name clashes with already added.\n// Object/class getters and setters are not allowed to clash —\n// either with each other or with an init property — and in\n// strict mode, init properties are also not allowed to be repeated.\n\npp.checkPropClash = function(prop, propHash, refDestructuringErrors) {\n  if (this.options.ecmaVersion >= 9 && prop.type === \"SpreadElement\")\n    return\n  if (this.options.ecmaVersion >= 6 && (prop.computed || prop.method || prop.shorthand))\n    return\n  let {key} = prop, name\n  switch (key.type) {\n  case \"Identifier\": name = key.name; break\n  case \"Literal\": name = String(key.value); break\n  default: return\n  }\n  let {kind} = prop\n  if (this.options.ecmaVersion >= 6) {\n    if (name === \"__proto__\" && kind === \"init\") {\n      if (propHash.proto) {\n        if (refDestructuringErrors && refDestructuringErrors.doubleProto < 0) refDestructuringErrors.doubleProto = key.start\n        // Backwards-compat kludge. Can be removed in version 6.0\n        else this.raiseRecoverable(key.start, \"Redefinition of __proto__ property\")\n      }\n      propHash.proto = true\n    }\n    return\n  }\n  name = \"$\" + name\n  let other = propHash[name]\n  if (other) {\n    let redefinition\n    if (kind === \"init\") {\n      redefinition = this.strict && other.init || other.get || other.set\n    } else {\n      redefinition = other.init || other[kind]\n    }\n    if (redefinition)\n      this.raiseRecoverable(key.start, \"Redefinition of property\")\n  } else {\n    other = propHash[name] = {\n      init: false,\n      get: false,\n      set: false\n    }\n  }\n  other[kind] = true\n}\n\n// ### Expression parsing\n\n// These nest, from the most general expression type at the top to\n// 'atomic', nondivisible expression types at the bottom. Most of\n// the functions will simply let the function(s) below them parse,\n// and, *if* the syntactic construct they handle is present, wrap\n// the AST node that the inner parser gave them in another node.\n\n// Parse a full expression. The optional arguments are used to\n// forbid the `in` operator (in for loops initalization expressions)\n// and provide reference for storing '=' operator inside shorthand\n// property assignment in contexts where both object expression\n// and object pattern might appear (so it's possible to raise\n// delayed syntax error at correct position).\n\npp.parseExpression = function(noIn, refDestructuringErrors) {\n  let startPos = this.start, startLoc = this.startLoc\n  let expr = this.parseMaybeAssign(noIn, refDestructuringErrors)\n  if (this.type === tt.comma) {\n    let node = this.startNodeAt(startPos, startLoc)\n    node.expressions = [expr]\n    while (this.eat(tt.comma)) node.expressions.push(this.parseMaybeAssign(noIn, refDestructuringErrors))\n    return this.finishNode(node, \"SequenceExpression\")\n  }\n  return expr\n}\n\n// Parse an assignment expression. This includes applications of\n// operators like `+=`.\n\npp.parseMaybeAssign = function(noIn, refDestructuringErrors, afterLeftParse) {\n  if (this.isContextual(\"yield\")) {\n    if (this.inGenerator) return this.parseYield(noIn)\n    // The tokenizer will assume an expression is allowed after\n    // `yield`, but this isn't that kind of yield\n    else this.exprAllowed = false\n  }\n\n  let ownDestructuringErrors = false, oldParenAssign = -1, oldTrailingComma = -1, oldShorthandAssign = -1\n  if (refDestructuringErrors) {\n    oldParenAssign = refDestructuringErrors.parenthesizedAssign\n    oldTrailingComma = refDestructuringErrors.trailingComma\n    oldShorthandAssign = refDestructuringErrors.shorthandAssign\n    refDestructuringErrors.parenthesizedAssign = refDestructuringErrors.trailingComma = refDestructuringErrors.shorthandAssign = -1\n  } else {\n    refDestructuringErrors = new DestructuringErrors\n    ownDestructuringErrors = true\n  }\n\n  let startPos = this.start, startLoc = this.startLoc\n  if (this.type === tt.parenL || this.type === tt.name)\n    this.potentialArrowAt = this.start\n  let left = this.parseMaybeConditional(noIn, refDestructuringErrors)\n  if (afterLeftParse) left = afterLeftParse.call(this, left, startPos, startLoc)\n  if (this.type.isAssign) {\n    let node = this.startNodeAt(startPos, startLoc)\n    node.operator = this.value\n    node.left = this.type === tt.eq ? this.toAssignable(left, false, refDestructuringErrors) : left\n    if (!ownDestructuringErrors) DestructuringErrors.call(refDestructuringErrors)\n    refDestructuringErrors.shorthandAssign = -1 // reset because shorthand default was used correctly\n    this.checkLVal(left)\n    this.next()\n    node.right = this.parseMaybeAssign(noIn)\n    return this.finishNode(node, \"AssignmentExpression\")\n  } else {\n    if (ownDestructuringErrors) this.checkExpressionErrors(refDestructuringErrors, true)\n  }\n  if (oldParenAssign > -1) refDestructuringErrors.parenthesizedAssign = oldParenAssign\n  if (oldTrailingComma > -1) refDestructuringErrors.trailingComma = oldTrailingComma\n  if (oldShorthandAssign > -1) refDestructuringErrors.shorthandAssign = oldShorthandAssign\n  return left\n}\n\n// Parse a ternary conditional (`?:`) operator.\n\npp.parseMaybeConditional = function(noIn, refDestructuringErrors) {\n  let startPos = this.start, startLoc = this.startLoc\n  let expr = this.parseExprOps(noIn, refDestructuringErrors)\n  if (this.checkExpressionErrors(refDestructuringErrors)) return expr\n  if (this.eat(tt.question)) {\n    let node = this.startNodeAt(startPos, startLoc)\n    node.test = expr\n    node.consequent = this.parseMaybeAssign()\n    this.expect(tt.colon)\n    node.alternate = this.parseMaybeAssign(noIn)\n    return this.finishNode(node, \"ConditionalExpression\")\n  }\n  return expr\n}\n\n// Start the precedence parser.\n\npp.parseExprOps = function(noIn, refDestructuringErrors) {\n  let startPos = this.start, startLoc = this.startLoc\n  let expr = this.parseMaybeUnary(refDestructuringErrors, false)\n  if (this.checkExpressionErrors(refDestructuringErrors)) return expr\n  return expr.start === startPos && expr.type === \"ArrowFunctionExpression\" ? expr : this.parseExprOp(expr, startPos, startLoc, -1, noIn)\n}\n\n// Parse binary operators with the operator precedence parsing\n// algorithm. `left` is the left-hand side of the operator.\n// `minPrec` provides context that allows the function to stop and\n// defer further parser to one of its callers when it encounters an\n// operator that has a lower precedence than the set it is parsing.\n\npp.parseExprOp = function(left, leftStartPos, leftStartLoc, minPrec, noIn) {\n  let prec = this.type.binop\n  if (prec != null && (!noIn || this.type !== tt._in)) {\n    if (prec > minPrec) {\n      let logical = this.type === tt.logicalOR || this.type === tt.logicalAND\n      let op = this.value\n      this.next()\n      let startPos = this.start, startLoc = this.startLoc\n      let right = this.parseExprOp(this.parseMaybeUnary(null, false), startPos, startLoc, prec, noIn)\n      let node = this.buildBinary(leftStartPos, leftStartLoc, left, right, op, logical)\n      return this.parseExprOp(node, leftStartPos, leftStartLoc, minPrec, noIn)\n    }\n  }\n  return left\n}\n\npp.buildBinary = function(startPos, startLoc, left, right, op, logical) {\n  let node = this.startNodeAt(startPos, startLoc)\n  node.left = left\n  node.operator = op\n  node.right = right\n  return this.finishNode(node, logical ? \"LogicalExpression\" : \"BinaryExpression\")\n}\n\n// Parse unary operators, both prefix and postfix.\n\npp.parseMaybeUnary = function(refDestructuringErrors, sawUnary) {\n  let startPos = this.start, startLoc = this.startLoc, expr\n  if (this.isContextual(\"await\") && (this.inAsync || (!this.inFunction && this.options.allowAwaitOutsideFunction))) {\n    expr = this.parseAwait()\n    sawUnary = true\n  } else if (this.type.prefix) {\n    let node = this.startNode(), update = this.type === tt.incDec\n    node.operator = this.value\n    node.prefix = true\n    this.next()\n    node.argument = this.parseMaybeUnary(null, true)\n    this.checkExpressionErrors(refDestructuringErrors, true)\n    if (update) this.checkLVal(node.argument)\n    else if (this.strict && node.operator === \"delete\" &&\n             node.argument.type === \"Identifier\")\n      this.raiseRecoverable(node.start, \"Deleting local variable in strict mode\")\n    else sawUnary = true\n    expr = this.finishNode(node, update ? \"UpdateExpression\" : \"UnaryExpression\")\n  } else {\n    expr = this.parseExprSubscripts(refDestructuringErrors)\n    if (this.checkExpressionErrors(refDestructuringErrors)) return expr\n    while (this.type.postfix && !this.canInsertSemicolon()) {\n      let node = this.startNodeAt(startPos, startLoc)\n      node.operator = this.value\n      node.prefix = false\n      node.argument = expr\n      this.checkLVal(expr)\n      this.next()\n      expr = this.finishNode(node, \"UpdateExpression\")\n    }\n  }\n\n  if (!sawUnary && this.eat(tt.starstar))\n    return this.buildBinary(startPos, startLoc, expr, this.parseMaybeUnary(null, false), \"**\", false)\n  else\n    return expr\n}\n\n// Parse call, dot, and `[]`-subscript expressions.\n\npp.parseExprSubscripts = function(refDestructuringErrors) {\n  let startPos = this.start, startLoc = this.startLoc\n  let expr = this.parseExprAtom(refDestructuringErrors)\n  let skipArrowSubscripts = expr.type === \"ArrowFunctionExpression\" && this.input.slice(this.lastTokStart, this.lastTokEnd) !== \")\"\n  if (this.checkExpressionErrors(refDestructuringErrors) || skipArrowSubscripts) return expr\n  let result = this.parseSubscripts(expr, startPos, startLoc)\n  if (refDestructuringErrors && result.type === \"MemberExpression\") {\n    if (refDestructuringErrors.parenthesizedAssign >= result.start) refDestructuringErrors.parenthesizedAssign = -1\n    if (refDestructuringErrors.parenthesizedBind >= result.start) refDestructuringErrors.parenthesizedBind = -1\n  }\n  return result\n}\n\npp.parseSubscripts = function(base, startPos, startLoc, noCalls) {\n  let maybeAsyncArrow = this.options.ecmaVersion >= 8 && base.type === \"Identifier\" && base.name === \"async\" &&\n      this.lastTokEnd === base.end && !this.canInsertSemicolon() && this.input.slice(base.start, base.end) === \"async\"\n  while (true) {\n    let element = this.parseSubscript(base, startPos, startLoc, noCalls, maybeAsyncArrow)\n    if (element === base || element.type === \"ArrowFunctionExpression\") return element\n    base = element\n  }\n}\n\npp.parseSubscript = function(base, startPos, startLoc, noCalls, maybeAsyncArrow) {\n  let computed = this.eat(tt.bracketL)\n  if (computed || this.eat(tt.dot)) {\n    let node = this.startNodeAt(startPos, startLoc)\n    node.object = base\n    node.property = computed ? this.parseExpression() : this.parseIdent(true)\n    node.computed = !!computed\n    if (computed) this.expect(tt.bracketR)\n    base = this.finishNode(node, \"MemberExpression\")\n  } else if (!noCalls && this.eat(tt.parenL)) {\n    let refDestructuringErrors = new DestructuringErrors, oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos\n    this.yieldPos = 0\n    this.awaitPos = 0\n    this.awaitIdentPos = 0\n    let exprList = this.parseExprList(tt.parenR, this.options.ecmaVersion >= 8, false, refDestructuringErrors)\n    if (maybeAsyncArrow && !this.canInsertSemicolon() && this.eat(tt.arrow)) {\n      this.checkPatternErrors(refDestructuringErrors, false)\n      this.checkYieldAwaitInDefaultParams()\n      if (this.awaitIdentPos > 0)\n        this.raise(this.awaitIdentPos, \"Cannot use 'await' as identifier inside an async function\")\n      this.yieldPos = oldYieldPos\n      this.awaitPos = oldAwaitPos\n      this.awaitIdentPos = oldAwaitIdentPos\n      return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), exprList, true)\n    }\n    this.checkExpressionErrors(refDestructuringErrors, true)\n    this.yieldPos = oldYieldPos || this.yieldPos\n    this.awaitPos = oldAwaitPos || this.awaitPos\n    this.awaitIdentPos = oldAwaitIdentPos || this.awaitIdentPos\n    let node = this.startNodeAt(startPos, startLoc)\n    node.callee = base\n    node.arguments = exprList\n    base = this.finishNode(node, \"CallExpression\")\n  } else if (this.type === tt.backQuote) {\n    let node = this.startNodeAt(startPos, startLoc)\n    node.tag = base\n    node.quasi = this.parseTemplate({isTagged: true})\n    base = this.finishNode(node, \"TaggedTemplateExpression\")\n  }\n  return base\n}\n\n// Parse an atomic expression — either a single token that is an\n// expression, an expression started by a keyword like `function` or\n// `new`, or an expression wrapped in punctuation like `()`, `[]`,\n// or `{}`.\n\npp.parseExprAtom = function(refDestructuringErrors) {\n  // If a division operator appears in an expression position, the\n  // tokenizer got confused, and we force it to read a regexp instead.\n  if (this.type === tt.slash) this.readRegexp()\n\n  let node, canBeArrow = this.potentialArrowAt === this.start\n  switch (this.type) {\n  case tt._super:\n    if (!this.allowSuper)\n      this.raise(this.start, \"'super' keyword outside a method\")\n    node = this.startNode()\n    this.next()\n    if (this.type === tt.parenL && !this.allowDirectSuper)\n      this.raise(node.start, \"super() call outside constructor of a subclass\")\n    // The `super` keyword can appear at below:\n    // SuperProperty:\n    //     super [ Expression ]\n    //     super . IdentifierName\n    // SuperCall:\n    //     super Arguments\n    if (this.type !== tt.dot && this.type !== tt.bracketL && this.type !== tt.parenL)\n      this.unexpected()\n    return this.finishNode(node, \"Super\")\n\n  case tt._this:\n    node = this.startNode()\n    this.next()\n    return this.finishNode(node, \"ThisExpression\")\n\n  case tt.name:\n    let startPos = this.start, startLoc = this.startLoc, containsEsc = this.containsEsc\n    let id = this.parseIdent(false)\n    if (this.options.ecmaVersion >= 8 && !containsEsc && id.name === \"async\" && !this.canInsertSemicolon() && this.eat(tt._function))\n      return this.parseFunction(this.startNodeAt(startPos, startLoc), 0, false, true)\n    if (canBeArrow && !this.canInsertSemicolon()) {\n      if (this.eat(tt.arrow))\n        return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), [id], false)\n      if (this.options.ecmaVersion >= 8 && id.name === \"async\" && this.type === tt.name && !containsEsc) {\n        id = this.parseIdent(false)\n        if (this.canInsertSemicolon() || !this.eat(tt.arrow))\n          this.unexpected()\n        return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), [id], true)\n      }\n    }\n    return id\n\n  case tt.regexp:\n    let value = this.value\n    node = this.parseLiteral(value.value)\n    node.regex = {pattern: value.pattern, flags: value.flags}\n    return node\n\n  case tt.num: case tt.string:\n    return this.parseLiteral(this.value)\n\n  case tt._null: case tt._true: case tt._false:\n    node = this.startNode()\n    node.value = this.type === tt._null ? null : this.type === tt._true\n    node.raw = this.type.keyword\n    this.next()\n    return this.finishNode(node, \"Literal\")\n\n  case tt.parenL:\n    let start = this.start, expr = this.parseParenAndDistinguishExpression(canBeArrow)\n    if (refDestructuringErrors) {\n      if (refDestructuringErrors.parenthesizedAssign < 0 && !this.isSimpleAssignTarget(expr))\n        refDestructuringErrors.parenthesizedAssign = start\n      if (refDestructuringErrors.parenthesizedBind < 0)\n        refDestructuringErrors.parenthesizedBind = start\n    }\n    return expr\n\n  case tt.bracketL:\n    node = this.startNode()\n    this.next()\n    node.elements = this.parseExprList(tt.bracketR, true, true, refDestructuringErrors)\n    return this.finishNode(node, \"ArrayExpression\")\n\n  case tt.braceL:\n    return this.parseObj(false, refDestructuringErrors)\n\n  case tt._function:\n    node = this.startNode()\n    this.next()\n    return this.parseFunction(node, 0)\n\n  case tt._class:\n    return this.parseClass(this.startNode(), false)\n\n  case tt._new:\n    return this.parseNew()\n\n  case tt.backQuote:\n    return this.parseTemplate()\n\n  default:\n    this.unexpected()\n  }\n}\n\npp.parseLiteral = function(value) {\n  let node = this.startNode()\n  node.value = value\n  node.raw = this.input.slice(this.start, this.end)\n  this.next()\n  return this.finishNode(node, \"Literal\")\n}\n\npp.parseParenExpression = function() {\n  this.expect(tt.parenL)\n  let val = this.parseExpression()\n  this.expect(tt.parenR)\n  return val\n}\n\npp.parseParenAndDistinguishExpression = function(canBeArrow) {\n  let startPos = this.start, startLoc = this.startLoc, val, allowTrailingComma = this.options.ecmaVersion >= 8\n  if (this.options.ecmaVersion >= 6) {\n    this.next()\n\n    let innerStartPos = this.start, innerStartLoc = this.startLoc\n    let exprList = [], first = true, lastIsComma = false\n    let refDestructuringErrors = new DestructuringErrors, oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, spreadStart\n    this.yieldPos = 0\n    this.awaitPos = 0\n    // Do not save awaitIdentPos to allow checking awaits nested in parameters\n    while (this.type !== tt.parenR) {\n      first ? first = false : this.expect(tt.comma)\n      if (allowTrailingComma && this.afterTrailingComma(tt.parenR, true)) {\n        lastIsComma = true\n        break\n      } else if (this.type === tt.ellipsis) {\n        spreadStart = this.start\n        exprList.push(this.parseParenItem(this.parseRestBinding()))\n        if (this.type === tt.comma) this.raise(this.start, \"Comma is not permitted after the rest element\")\n        break\n      } else {\n        exprList.push(this.parseMaybeAssign(false, refDestructuringErrors, this.parseParenItem))\n      }\n    }\n    let innerEndPos = this.start, innerEndLoc = this.startLoc\n    this.expect(tt.parenR)\n\n    if (canBeArrow && !this.canInsertSemicolon() && this.eat(tt.arrow)) {\n      this.checkPatternErrors(refDestructuringErrors, false)\n      this.checkYieldAwaitInDefaultParams()\n      this.yieldPos = oldYieldPos\n      this.awaitPos = oldAwaitPos\n      return this.parseParenArrowList(startPos, startLoc, exprList)\n    }\n\n    if (!exprList.length || lastIsComma) this.unexpected(this.lastTokStart)\n    if (spreadStart) this.unexpected(spreadStart)\n    this.checkExpressionErrors(refDestructuringErrors, true)\n    this.yieldPos = oldYieldPos || this.yieldPos\n    this.awaitPos = oldAwaitPos || this.awaitPos\n\n    if (exprList.length > 1) {\n      val = this.startNodeAt(innerStartPos, innerStartLoc)\n      val.expressions = exprList\n      this.finishNodeAt(val, \"SequenceExpression\", innerEndPos, innerEndLoc)\n    } else {\n      val = exprList[0]\n    }\n  } else {\n    val = this.parseParenExpression()\n  }\n\n  if (this.options.preserveParens) {\n    let par = this.startNodeAt(startPos, startLoc)\n    par.expression = val\n    return this.finishNode(par, \"ParenthesizedExpression\")\n  } else {\n    return val\n  }\n}\n\npp.parseParenItem = function(item) {\n  return item\n}\n\npp.parseParenArrowList = function(startPos, startLoc, exprList) {\n  return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), exprList)\n}\n\n// New's precedence is slightly tricky. It must allow its argument to\n// be a `[]` or dot subscript expression, but not a call — at least,\n// not without wrapping it in parentheses. Thus, it uses the noCalls\n// argument to parseSubscripts to prevent it from consuming the\n// argument list.\n\nconst empty = []\n\npp.parseNew = function() {\n  let node = this.startNode()\n  let meta = this.parseIdent(true)\n  if (this.options.ecmaVersion >= 6 && this.eat(tt.dot)) {\n    node.meta = meta\n    let containsEsc = this.containsEsc\n    node.property = this.parseIdent(true)\n    if (node.property.name !== \"target\" || containsEsc)\n      this.raiseRecoverable(node.property.start, \"The only valid meta property for new is new.target\")\n    if (!this.inNonArrowFunction())\n      this.raiseRecoverable(node.start, \"new.target can only be used in functions\")\n    return this.finishNode(node, \"MetaProperty\")\n  }\n  let startPos = this.start, startLoc = this.startLoc\n  node.callee = this.parseSubscripts(this.parseExprAtom(), startPos, startLoc, true)\n  if (this.eat(tt.parenL)) node.arguments = this.parseExprList(tt.parenR, this.options.ecmaVersion >= 8, false)\n  else node.arguments = empty\n  return this.finishNode(node, \"NewExpression\")\n}\n\n// Parse template expression.\n\npp.parseTemplateElement = function({isTagged}) {\n  let elem = this.startNode()\n  if (this.type === tt.invalidTemplate) {\n    if (!isTagged) {\n      this.raiseRecoverable(this.start, \"Bad escape sequence in untagged template literal\")\n    }\n    elem.value = {\n      raw: this.value,\n      cooked: null\n    }\n  } else {\n    elem.value = {\n      raw: this.input.slice(this.start, this.end).replace(/\\r\\n?/g, \"\\n\"),\n      cooked: this.value\n    }\n  }\n  this.next()\n  elem.tail = this.type === tt.backQuote\n  return this.finishNode(elem, \"TemplateElement\")\n}\n\npp.parseTemplate = function({isTagged = false} = {}) {\n  let node = this.startNode()\n  this.next()\n  node.expressions = []\n  let curElt = this.parseTemplateElement({isTagged})\n  node.quasis = [curElt]\n  while (!curElt.tail) {\n    if (this.type === tt.eof) this.raise(this.pos, \"Unterminated template literal\")\n    this.expect(tt.dollarBraceL)\n    node.expressions.push(this.parseExpression())\n    this.expect(tt.braceR)\n    node.quasis.push(curElt = this.parseTemplateElement({isTagged}))\n  }\n  this.next()\n  return this.finishNode(node, \"TemplateLiteral\")\n}\n\npp.isAsyncProp = function(prop) {\n  return !prop.computed && prop.key.type === \"Identifier\" && prop.key.name === \"async\" &&\n    (this.type === tt.name || this.type === tt.num || this.type === tt.string || this.type === tt.bracketL || this.type.keyword || (this.options.ecmaVersion >= 9 && this.type === tt.star)) &&\n    !lineBreak.test(this.input.slice(this.lastTokEnd, this.start))\n}\n\n// Parse an object literal or binding pattern.\n\npp.parseObj = function(isPattern, refDestructuringErrors) {\n  let node = this.startNode(), first = true, propHash = {}\n  node.properties = []\n  this.next()\n  while (!this.eat(tt.braceR)) {\n    if (!first) {\n      this.expect(tt.comma)\n      if (this.afterTrailingComma(tt.braceR)) break\n    } else first = false\n\n    const prop = this.parseProperty(isPattern, refDestructuringErrors)\n    if (!isPattern) this.checkPropClash(prop, propHash, refDestructuringErrors)\n    node.properties.push(prop)\n  }\n  return this.finishNode(node, isPattern ? \"ObjectPattern\" : \"ObjectExpression\")\n}\n\npp.parseProperty = function(isPattern, refDestructuringErrors) {\n  let prop = this.startNode(), isGenerator, isAsync, startPos, startLoc\n  if (this.options.ecmaVersion >= 9 && this.eat(tt.ellipsis)) {\n    if (isPattern) {\n      prop.argument = this.parseIdent(false)\n      if (this.type === tt.comma) {\n        this.raise(this.start, \"Comma is not permitted after the rest element\")\n      }\n      return this.finishNode(prop, \"RestElement\")\n    }\n    // To disallow parenthesized identifier via `this.toAssignable()`.\n    if (this.type === tt.parenL && refDestructuringErrors) {\n      if (refDestructuringErrors.parenthesizedAssign < 0) {\n        refDestructuringErrors.parenthesizedAssign = this.start\n      }\n      if (refDestructuringErrors.parenthesizedBind < 0) {\n        refDestructuringErrors.parenthesizedBind = this.start\n      }\n    }\n    // Parse argument.\n    prop.argument = this.parseMaybeAssign(false, refDestructuringErrors)\n    // To disallow trailing comma via `this.toAssignable()`.\n    if (this.type === tt.comma && refDestructuringErrors && refDestructuringErrors.trailingComma < 0) {\n      refDestructuringErrors.trailingComma = this.start\n    }\n    // Finish\n    return this.finishNode(prop, \"SpreadElement\")\n  }\n  if (this.options.ecmaVersion >= 6) {\n    prop.method = false\n    prop.shorthand = false\n    if (isPattern || refDestructuringErrors) {\n      startPos = this.start\n      startLoc = this.startLoc\n    }\n    if (!isPattern)\n      isGenerator = this.eat(tt.star)\n  }\n  let containsEsc = this.containsEsc\n  this.parsePropertyName(prop)\n  if (!isPattern && !containsEsc && this.options.ecmaVersion >= 8 && !isGenerator && this.isAsyncProp(prop)) {\n    isAsync = true\n    isGenerator = this.options.ecmaVersion >= 9 && this.eat(tt.star)\n    this.parsePropertyName(prop, refDestructuringErrors)\n  } else {\n    isAsync = false\n  }\n  this.parsePropertyValue(prop, isPattern, isGenerator, isAsync, startPos, startLoc, refDestructuringErrors, containsEsc)\n  return this.finishNode(prop, \"Property\")\n}\n\npp.parsePropertyValue = function(prop, isPattern, isGenerator, isAsync, startPos, startLoc, refDestructuringErrors, containsEsc) {\n  if ((isGenerator || isAsync) && this.type === tt.colon)\n    this.unexpected()\n\n  if (this.eat(tt.colon)) {\n    prop.value = isPattern ? this.parseMaybeDefault(this.start, this.startLoc) : this.parseMaybeAssign(false, refDestructuringErrors)\n    prop.kind = \"init\"\n  } else if (this.options.ecmaVersion >= 6 && this.type === tt.parenL) {\n    if (isPattern) this.unexpected()\n    prop.kind = \"init\"\n    prop.method = true\n    prop.value = this.parseMethod(isGenerator, isAsync)\n  } else if (!isPattern && !containsEsc &&\n             this.options.ecmaVersion >= 5 && !prop.computed && prop.key.type === \"Identifier\" &&\n             (prop.key.name === \"get\" || prop.key.name === \"set\") &&\n             (this.type !== tt.comma && this.type !== tt.braceR)) {\n    if (isGenerator || isAsync) this.unexpected()\n    prop.kind = prop.key.name\n    this.parsePropertyName(prop)\n    prop.value = this.parseMethod(false)\n    let paramCount = prop.kind === \"get\" ? 0 : 1\n    if (prop.value.params.length !== paramCount) {\n      let start = prop.value.start\n      if (prop.kind === \"get\")\n        this.raiseRecoverable(start, \"getter should have no params\")\n      else\n        this.raiseRecoverable(start, \"setter should have exactly one param\")\n    } else {\n      if (prop.kind === \"set\" && prop.value.params[0].type === \"RestElement\")\n        this.raiseRecoverable(prop.value.params[0].start, \"Setter cannot use rest params\")\n    }\n  } else if (this.options.ecmaVersion >= 6 && !prop.computed && prop.key.type === \"Identifier\") {\n    if (isGenerator || isAsync) this.unexpected()\n    this.checkUnreserved(prop.key)\n    if (prop.key.name === \"await\" && !this.awaitIdentPos)\n      this.awaitIdentPos = startPos\n    prop.kind = \"init\"\n    if (isPattern) {\n      prop.value = this.parseMaybeDefault(startPos, startLoc, prop.key)\n    } else if (this.type === tt.eq && refDestructuringErrors) {\n      if (refDestructuringErrors.shorthandAssign < 0)\n        refDestructuringErrors.shorthandAssign = this.start\n      prop.value = this.parseMaybeDefault(startPos, startLoc, prop.key)\n    } else {\n      prop.value = prop.key\n    }\n    prop.shorthand = true\n  } else this.unexpected()\n}\n\npp.parsePropertyName = function(prop) {\n  if (this.options.ecmaVersion >= 6) {\n    if (this.eat(tt.bracketL)) {\n      prop.computed = true\n      prop.key = this.parseMaybeAssign()\n      this.expect(tt.bracketR)\n      return prop.key\n    } else {\n      prop.computed = false\n    }\n  }\n  return prop.key = this.type === tt.num || this.type === tt.string ? this.parseExprAtom() : this.parseIdent(true)\n}\n\n// Initialize empty function node.\n\npp.initFunction = function(node) {\n  node.id = null\n  if (this.options.ecmaVersion >= 6) node.generator = node.expression = false\n  if (this.options.ecmaVersion >= 8) node.async = false\n}\n\n// Parse object or class method.\n\npp.parseMethod = function(isGenerator, isAsync, allowDirectSuper) {\n  let node = this.startNode(), oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos\n\n  this.initFunction(node)\n  if (this.options.ecmaVersion >= 6)\n    node.generator = isGenerator\n  if (this.options.ecmaVersion >= 8)\n    node.async = !!isAsync\n\n  this.yieldPos = 0\n  this.awaitPos = 0\n  this.awaitIdentPos = 0\n  this.enterScope(functionFlags(isAsync, node.generator) | SCOPE_SUPER | (allowDirectSuper ? SCOPE_DIRECT_SUPER : 0))\n\n  this.expect(tt.parenL)\n  node.params = this.parseBindingList(tt.parenR, false, this.options.ecmaVersion >= 8)\n  this.checkYieldAwaitInDefaultParams()\n  this.parseFunctionBody(node, false, true)\n\n  this.yieldPos = oldYieldPos\n  this.awaitPos = oldAwaitPos\n  this.awaitIdentPos = oldAwaitIdentPos\n  return this.finishNode(node, \"FunctionExpression\")\n}\n\n// Parse arrow function expression with given parameters.\n\npp.parseArrowExpression = function(node, params, isAsync) {\n  let oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos\n\n  this.enterScope(functionFlags(isAsync, false) | SCOPE_ARROW)\n  this.initFunction(node)\n  if (this.options.ecmaVersion >= 8) node.async = !!isAsync\n\n  this.yieldPos = 0\n  this.awaitPos = 0\n  this.awaitIdentPos = 0\n\n  node.params = this.toAssignableList(params, true)\n  this.parseFunctionBody(node, true, false)\n\n  this.yieldPos = oldYieldPos\n  this.awaitPos = oldAwaitPos\n  this.awaitIdentPos = oldAwaitIdentPos\n  return this.finishNode(node, \"ArrowFunctionExpression\")\n}\n\n// Parse function body and check parameters.\n\npp.parseFunctionBody = function(node, isArrowFunction, isMethod) {\n  let isExpression = isArrowFunction && this.type !== tt.braceL\n  let oldStrict = this.strict, useStrict = false\n\n  if (isExpression) {\n    node.body = this.parseMaybeAssign()\n    node.expression = true\n    this.checkParams(node, false)\n  } else {\n    let nonSimple = this.options.ecmaVersion >= 7 && !this.isSimpleParamList(node.params)\n    if (!oldStrict || nonSimple) {\n      useStrict = this.strictDirective(this.end)\n      // If this is a strict mode function, verify that argument names\n      // are not repeated, and it does not try to bind the words `eval`\n      // or `arguments`.\n      if (useStrict && nonSimple)\n        this.raiseRecoverable(node.start, \"Illegal 'use strict' directive in function with non-simple parameter list\")\n    }\n    // Start a new scope with regard to labels and the `inFunction`\n    // flag (restore them to their old value afterwards).\n    let oldLabels = this.labels\n    this.labels = []\n    if (useStrict) this.strict = true\n\n    // Add the params to varDeclaredNames to ensure that an error is thrown\n    // if a let/const declaration in the function clashes with one of the params.\n    this.checkParams(node, !oldStrict && !useStrict && !isArrowFunction && !isMethod && this.isSimpleParamList(node.params))\n    node.body = this.parseBlock(false)\n    node.expression = false\n    this.adaptDirectivePrologue(node.body.body)\n    this.labels = oldLabels\n  }\n  this.exitScope()\n\n  // Ensure the function name isn't a forbidden identifier in strict mode, e.g. 'eval'\n  if (this.strict && node.id) this.checkLVal(node.id, BIND_OUTSIDE)\n  this.strict = oldStrict\n}\n\npp.isSimpleParamList = function(params) {\n  for (let param of params)\n    if (param.type !== \"Identifier\") return false\n  return true\n}\n\n// Checks function params for various disallowed patterns such as using \"eval\"\n// or \"arguments\" and duplicate parameters.\n\npp.checkParams = function(node, allowDuplicates) {\n  let nameHash = {}\n  for (let param of node.params)\n    this.checkLVal(param, BIND_VAR, allowDuplicates ? null : nameHash)\n}\n\n// Parses a comma-separated list of expressions, and returns them as\n// an array. `close` is the token type that ends the list, and\n// `allowEmpty` can be turned on to allow subsequent commas with\n// nothing in between them to be parsed as `null` (which is needed\n// for array literals).\n\npp.parseExprList = function(close, allowTrailingComma, allowEmpty, refDestructuringErrors) {\n  let elts = [], first = true\n  while (!this.eat(close)) {\n    if (!first) {\n      this.expect(tt.comma)\n      if (allowTrailingComma && this.afterTrailingComma(close)) break\n    } else first = false\n\n    let elt\n    if (allowEmpty && this.type === tt.comma)\n      elt = null\n    else if (this.type === tt.ellipsis) {\n      elt = this.parseSpread(refDestructuringErrors)\n      if (refDestructuringErrors && this.type === tt.comma && refDestructuringErrors.trailingComma < 0)\n        refDestructuringErrors.trailingComma = this.start\n    } else {\n      elt = this.parseMaybeAssign(false, refDestructuringErrors)\n    }\n    elts.push(elt)\n  }\n  return elts\n}\n\npp.checkUnreserved = function({start, end, name}) {\n  if (this.inGenerator && name === \"yield\")\n    this.raiseRecoverable(start, \"Cannot use 'yield' as identifier inside a generator\")\n  if (this.inAsync && name === \"await\")\n    this.raiseRecoverable(start, \"Cannot use 'await' as identifier inside an async function\")\n  if (this.keywords.test(name))\n    this.raise(start, `Unexpected keyword '${name}'`)\n  if (this.options.ecmaVersion < 6 &&\n    this.input.slice(start, end).indexOf(\"\\\\\") !== -1) return\n  const re = this.strict ? this.reservedWordsStrict : this.reservedWords\n  if (re.test(name)) {\n    if (!this.inAsync && name === \"await\")\n      this.raiseRecoverable(start, \"Cannot use keyword 'await' outside an async function\")\n    this.raiseRecoverable(start, `The keyword '${name}' is reserved`)\n  }\n}\n\n// Parse the next token as an identifier. If `liberal` is true (used\n// when parsing properties), it will also convert keywords into\n// identifiers.\n\npp.parseIdent = function(liberal, isBinding) {\n  let node = this.startNode()\n  if (liberal && this.options.allowReserved === \"never\") liberal = false\n  if (this.type === tt.name) {\n    node.name = this.value\n  } else if (this.type.keyword) {\n    node.name = this.type.keyword\n\n    // To fix https://github.com/acornjs/acorn/issues/575\n    // `class` and `function` keywords push new context into this.context.\n    // But there is no chance to pop the context if the keyword is consumed as an identifier such as a property name.\n    // If the previous token is a dot, this does not apply because the context-managing code already ignored the keyword\n    if ((node.name === \"class\" || node.name === \"function\") &&\n        (this.lastTokEnd !== this.lastTokStart + 1 || this.input.charCodeAt(this.lastTokStart) !== 46)) {\n      this.context.pop()\n    }\n  } else {\n    this.unexpected()\n  }\n  this.next()\n  this.finishNode(node, \"Identifier\")\n  if (!liberal) {\n    this.checkUnreserved(node)\n    if (node.name === \"await\" && !this.awaitIdentPos)\n      this.awaitIdentPos = node.start\n  }\n  return node\n}\n\n// Parses yield expression inside generator.\n\npp.parseYield = function(noIn) {\n  if (!this.yieldPos) this.yieldPos = this.start\n\n  let node = this.startNode()\n  this.next()\n  if (this.type === tt.semi || this.canInsertSemicolon() || (this.type !== tt.star && !this.type.startsExpr)) {\n    node.delegate = false\n    node.argument = null\n  } else {\n    node.delegate = this.eat(tt.star)\n    node.argument = this.parseMaybeAssign(noIn)\n  }\n  return this.finishNode(node, \"YieldExpression\")\n}\n\npp.parseAwait = function() {\n  if (!this.awaitPos) this.awaitPos = this.start\n\n  let node = this.startNode()\n  this.next()\n  node.argument = this.parseMaybeUnary(null, true)\n  return this.finishNode(node, \"AwaitExpression\")\n}\n", "import {Parser} from \"./state\"\nimport {Position, getLineInfo} from \"./locutil\"\n\nconst pp = Parser.prototype\n\n// This function is used to raise exceptions on parse errors. It\n// takes an offset integer (into the current `input`) to indicate\n// the location of the error, attaches the position to the end\n// of the error message, and then raises a `SyntaxError` with that\n// message.\n\npp.raise = function(pos, message) {\n  let loc = getLineInfo(this.input, pos)\n  message += \" (\" + loc.line + \":\" + loc.column + \")\"\n  let err = new SyntaxError(message)\n  err.pos = pos; err.loc = loc; err.raisedAt = this.pos\n  throw err\n}\n\npp.raiseRecoverable = pp.raise\n\npp.curPosition = function() {\n  if (this.options.locations) {\n    return new Position(this.curLine, this.pos - this.lineStart)\n  }\n}\n", "import {<PERSON><PERSON><PERSON>} from \"./state\"\nimport {SCOPE_VAR, SCOPE_FUNCTION, SCOPE_TOP, SCOPE_ARROW, SCOPE_SIMPLE_CATCH, BIND_LEXICAL, BIND_SIMPLE_CATCH, BIND_FUNCTION} from \"./scopeflags\"\n\nconst pp = Parser.prototype\n\nclass Scope {\n  constructor(flags) {\n    this.flags = flags\n    // A list of var-declared names in the current lexical scope\n    this.var = []\n    // A list of lexically-declared names in the current lexical scope\n    this.lexical = []\n    // A list of lexically-declared FunctionDeclaration names in the current lexical scope\n    this.functions = []\n  }\n}\n\n// The functions in this module keep track of declared variables in the current scope in order to detect duplicate variable names.\n\npp.enterScope = function(flags) {\n  this.scopeStack.push(new Scope(flags))\n}\n\npp.exitScope = function() {\n  this.scopeStack.pop()\n}\n\n// The spec says:\n// > At the top level of a function, or script, function declarations are\n// > treated like var declarations rather than like lexical declarations.\npp.treatFunctionsAsVarInScope = function(scope) {\n  return (scope.flags & SCOPE_FUNCTION) || !this.inModule && (scope.flags & SCOPE_TOP)\n}\n\npp.declareName = function(name, bindingType, pos) {\n  let redeclared = false\n  if (bindingType === BIND_LEXICAL) {\n    const scope = this.currentScope()\n    redeclared = scope.lexical.indexOf(name) > -1 || scope.functions.indexOf(name) > -1 || scope.var.indexOf(name) > -1\n    scope.lexical.push(name)\n    if (this.inModule && (scope.flags & SCOPE_TOP))\n      delete this.undefinedExports[name]\n  } else if (bindingType === BIND_SIMPLE_CATCH) {\n    const scope = this.currentScope()\n    scope.lexical.push(name)\n  } else if (bindingType === BIND_FUNCTION) {\n    const scope = this.currentScope()\n    if (this.treatFunctionsAsVar)\n      redeclared = scope.lexical.indexOf(name) > -1\n    else\n      redeclared = scope.lexical.indexOf(name) > -1 || scope.var.indexOf(name) > -1\n    scope.functions.push(name)\n  } else {\n    for (let i = this.scopeStack.length - 1; i >= 0; --i) {\n      const scope = this.scopeStack[i]\n      if (scope.lexical.indexOf(name) > -1 && !((scope.flags & SCOPE_SIMPLE_CATCH) && scope.lexical[0] === name) ||\n          !this.treatFunctionsAsVarInScope(scope) && scope.functions.indexOf(name) > -1) {\n        redeclared = true\n        break\n      }\n      scope.var.push(name)\n      if (this.inModule && (scope.flags & SCOPE_TOP))\n        delete this.undefinedExports[name]\n      if (scope.flags & SCOPE_VAR) break\n    }\n  }\n  if (redeclared) this.raiseRecoverable(pos, `Identifier '${name}' has already been declared`)\n}\n\npp.checkLocalExport = function(id) {\n  // scope.functions must be empty as Module code is always strict.\n  if (this.scopeStack[0].lexical.indexOf(id.name) === -1 &&\n      this.scopeStack[0].var.indexOf(id.name) === -1) {\n    this.undefinedExports[id.name] = id\n  }\n}\n\npp.currentScope = function() {\n  return this.scopeStack[this.scopeStack.length - 1]\n}\n\npp.currentVarScope = function() {\n  for (let i = this.scopeStack.length - 1;; i--) {\n    let scope = this.scopeStack[i]\n    if (scope.flags & SCOPE_VAR) return scope\n  }\n}\n\n// Could be useful for `this`, `new.target`, `super()`, `super.property`, and `super[property]`.\npp.currentThisScope = function() {\n  for (let i = this.scopeStack.length - 1;; i--) {\n    let scope = this.scopeStack[i]\n    if (scope.flags & SCOPE_VAR && !(scope.flags & SCOPE_ARROW)) return scope\n  }\n}\n", "import {<PERSON><PERSON><PERSON>} from \"./state\"\nimport {SourceLocation} from \"./locutil\"\n\nexport class Node {\n  constructor(parser, pos, loc) {\n    this.type = \"\"\n    this.start = pos\n    this.end = 0\n    if (parser.options.locations)\n      this.loc = new SourceLocation(parser, loc)\n    if (parser.options.directSourceFile)\n      this.sourceFile = parser.options.directSourceFile\n    if (parser.options.ranges)\n      this.range = [pos, 0]\n  }\n}\n\n// Start an AST node, attaching a start offset.\n\nconst pp = Parser.prototype\n\npp.startNode = function() {\n  return new Node(this, this.start, this.startLoc)\n}\n\npp.startNodeAt = function(pos, loc) {\n  return new Node(this, pos, loc)\n}\n\n// Finish an AST node, adding `type` and `end` properties.\n\nfunction finishNodeAt(node, type, pos, loc) {\n  node.type = type\n  node.end = pos\n  if (this.options.locations)\n    node.loc.end = loc\n  if (this.options.ranges)\n    node.range[1] = pos\n  return node\n}\n\npp.finishNode = function(node, type) {\n  return finishNodeAt.call(this, node, type, this.lastTokEnd, this.lastTokEndLoc)\n}\n\n// Finish node at given position\n\npp.finishNodeAt = function(node, type, pos, loc) {\n  return finishNodeAt.call(this, node, type, pos, loc)\n}\n", "// The algorithm used to determine whether a regexp can appear at a\n// given point in the program is loosely based on sweet.js' approach.\n// See https://github.com/mozilla/sweet.js/wiki/design\n\nimport {Parser} from \"./state\"\nimport {types as tt} from \"./tokentype\"\nimport {lineBreak} from \"./whitespace\"\n\nexport class TokContext {\n  constructor(token, isExpr, preserveSpace, override, generator) {\n    this.token = token\n    this.isExpr = !!isExpr\n    this.preserveSpace = !!preserveSpace\n    this.override = override\n    this.generator = !!generator\n  }\n}\n\nexport const types = {\n  b_stat: new TokContext(\"{\", false),\n  b_expr: new TokContext(\"{\", true),\n  b_tmpl: new TokContext(\"${\", false),\n  p_stat: new TokContext(\"(\", false),\n  p_expr: new TokContext(\"(\", true),\n  q_tmpl: new TokContext(\"`\", true, true, p => p.tryReadTemplateToken()),\n  f_stat: new TokContext(\"function\", false),\n  f_expr: new TokContext(\"function\", true),\n  f_expr_gen: new TokContext(\"function\", true, false, null, true),\n  f_gen: new TokContext(\"function\", false, false, null, true)\n}\n\nconst pp = Parser.prototype\n\npp.initialContext = function() {\n  return [types.b_stat]\n}\n\npp.braceIsBlock = function(prevType) {\n  let parent = this.curContext()\n  if (parent === types.f_expr || parent === types.f_stat)\n    return true\n  if (prevType === tt.colon && (parent === types.b_stat || parent === types.b_expr))\n    return !parent.isExpr\n\n  // The check for `tt.name && exprAllowed` detects whether we are\n  // after a `yield` or `of` construct. See the `updateContext` for\n  // `tt.name`.\n  if (prevType === tt._return || prevType === tt.name && this.exprAllowed)\n    return lineBreak.test(this.input.slice(this.lastTokEnd, this.start))\n  if (prevType === tt._else || prevType === tt.semi || prevType === tt.eof || prevType === tt.parenR || prevType === tt.arrow)\n    return true\n  if (prevType === tt.braceL)\n    return parent === types.b_stat\n  if (prevType === tt._var || prevType === tt._const || prevType === tt.name)\n    return false\n  return !this.exprAllowed\n}\n\npp.inGeneratorContext = function() {\n  for (let i = this.context.length - 1; i >= 1; i--) {\n    let context = this.context[i]\n    if (context.token === \"function\")\n      return context.generator\n  }\n  return false\n}\n\npp.updateContext = function(prevType) {\n  let update, type = this.type\n  if (type.keyword && prevType === tt.dot)\n    this.exprAllowed = false\n  else if (update = type.updateContext)\n    update.call(this, prevType)\n  else\n    this.exprAllowed = type.beforeExpr\n}\n\n// Token-specific context update code\n\ntt.parenR.updateContext = tt.braceR.updateContext = function() {\n  if (this.context.length === 1) {\n    this.exprAllowed = true\n    return\n  }\n  let out = this.context.pop()\n  if (out === types.b_stat && this.curContext().token === \"function\") {\n    out = this.context.pop()\n  }\n  this.exprAllowed = !out.isExpr\n}\n\ntt.braceL.updateContext = function(prevType) {\n  this.context.push(this.braceIsBlock(prevType) ? types.b_stat : types.b_expr)\n  this.exprAllowed = true\n}\n\ntt.dollarBraceL.updateContext = function() {\n  this.context.push(types.b_tmpl)\n  this.exprAllowed = true\n}\n\ntt.parenL.updateContext = function(prevType) {\n  let statementParens = prevType === tt._if || prevType === tt._for || prevType === tt._with || prevType === tt._while\n  this.context.push(statementParens ? types.p_stat : types.p_expr)\n  this.exprAllowed = true\n}\n\ntt.incDec.updateContext = function() {\n  // tokExprAllowed stays unchanged\n}\n\ntt._function.updateContext = tt._class.updateContext = function(prevType) {\n  if (prevType.beforeExpr && prevType !== tt.semi && prevType !== tt._else &&\n      !(prevType === tt._return && lineBreak.test(this.input.slice(this.lastTokEnd, this.start))) &&\n      !((prevType === tt.colon || prevType === tt.braceL) && this.curContext() === types.b_stat))\n    this.context.push(types.f_expr)\n  else\n    this.context.push(types.f_stat)\n  this.exprAllowed = false\n}\n\ntt.backQuote.updateContext = function() {\n  if (this.curContext() === types.q_tmpl)\n    this.context.pop()\n  else\n    this.context.push(types.q_tmpl)\n  this.exprAllowed = false\n}\n\ntt.star.updateContext = function(prevType) {\n  if (prevType === tt._function) {\n    let index = this.context.length - 1\n    if (this.context[index] === types.f_expr)\n      this.context[index] = types.f_expr_gen\n    else\n      this.context[index] = types.f_gen\n  }\n  this.exprAllowed = true\n}\n\ntt.name.updateContext = function(prevType) {\n  let allowed = false\n  if (this.options.ecmaVersion >= 6 && prevType !== tt.dot) {\n    if (this.value === \"of\" && !this.exprAllowed ||\n        this.value === \"yield\" && this.inGeneratorContext())\n      allowed = true\n  }\n  this.exprAllowed = allowed\n}\n", "import {wordsRegexp} from \"./util.js\"\n\n// This file contains Unicode properties extracted from the ECMAScript\n// specification. The lists are extracted like so:\n// $$('#table-binary-unicode-properties > figure > table > tbody > tr > td:nth-child(1) code').map(el => el.innerText)\n\n// #table-binary-unicode-properties\nconst ecma9BinaryProperties = \"ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS\"\nconst unicodeBinaryProperties = {\n  9: ecma9BinaryProperties,\n  10: ecma9BinaryProperties + \" Extended_Pictographic\"\n}\n\n// #table-unicode-general-category-values\nconst unicodeGeneralCategoryValues = \"Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu\"\n\n// #table-unicode-script-values\nconst ecma9ScriptValues = \"Adlam Adlm Ahom Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb\"\nconst unicodeScriptValues = {\n  9: ecma9ScriptValues,\n  10: ecma9ScriptValues + \" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd\"\n}\n\nconst data = {}\nfunction buildUnicodeData(ecmaVersion) {\n  let d = data[ecmaVersion] = {\n    binary: wordsRegexp(unicodeBinaryProperties[ecmaVersion] + \" \" + unicodeGeneralCategoryValues),\n    nonBinary: {\n      General_Category: wordsRegexp(unicodeGeneralCategoryValues),\n      Script: wordsRegexp(unicodeScriptValues[ecmaVersion])\n    }\n  }\n  d.nonBinary.Script_Extensions = d.nonBinary.Script\n\n  d.nonBinary.gc = d.nonBinary.General_Category\n  d.nonBinary.sc = d.nonBinary.Script\n  d.nonBinary.scx = d.nonBinary.Script_Extensions\n}\nbuildUnicodeData(9)\nbuildUnicodeData(10)\n\nexport default data\n", "import {isIdentifierStart, isIdentifierChar} from \"./identifier.js\"\nimport {<PERSON>rse<PERSON>} from \"./state.js\"\nimport UNICODE_PROPERTY_VALUES from \"./unicode-property-data.js\"\nimport {has} from \"./util.js\"\n\nconst pp = Parser.prototype\n\nexport class RegExpValidationState {\n  constructor(parser) {\n    this.parser = parser\n    this.validFlags = `gim${parser.options.ecmaVersion >= 6 ? \"uy\" : \"\"}${parser.options.ecmaVersion >= 9 ? \"s\" : \"\"}`\n    this.unicodeProperties = UNICODE_PROPERTY_VALUES[parser.options.ecmaVersion >= 10 ? 10 : parser.options.ecmaVersion]\n    this.source = \"\"\n    this.flags = \"\"\n    this.start = 0\n    this.switchU = false\n    this.switchN = false\n    this.pos = 0\n    this.lastIntValue = 0\n    this.lastStringValue = \"\"\n    this.lastAssertionIsQuantifiable = false\n    this.numCapturingParens = 0\n    this.maxBackReference = 0\n    this.groupNames = []\n    this.backReferenceNames = []\n  }\n\n  reset(start, pattern, flags) {\n    const unicode = flags.indexOf(\"u\") !== -1\n    this.start = start | 0\n    this.source = pattern + \"\"\n    this.flags = flags\n    this.switchU = unicode && this.parser.options.ecmaVersion >= 6\n    this.switchN = unicode && this.parser.options.ecmaVersion >= 9\n  }\n\n  raise(message) {\n    this.parser.raiseRecoverable(this.start, `Invalid regular expression: /${this.source}/: ${message}`)\n  }\n\n  // If u flag is given, this returns the code point at the index (it combines a surrogate pair).\n  // Otherwise, this returns the code unit of the index (can be a part of a surrogate pair).\n  at(i) {\n    const s = this.source\n    const l = s.length\n    if (i >= l) {\n      return -1\n    }\n    const c = s.charCodeAt(i)\n    if (!this.switchU || c <= 0xD7FF || c >= 0xE000 || i + 1 >= l) {\n      return c\n    }\n    return (c << 10) + s.charCodeAt(i + 1) - 0x35FDC00\n  }\n\n  nextIndex(i) {\n    const s = this.source\n    const l = s.length\n    if (i >= l) {\n      return l\n    }\n    const c = s.charCodeAt(i)\n    if (!this.switchU || c <= 0xD7FF || c >= 0xE000 || i + 1 >= l) {\n      return i + 1\n    }\n    return i + 2\n  }\n\n  current() {\n    return this.at(this.pos)\n  }\n\n  lookahead() {\n    return this.at(this.nextIndex(this.pos))\n  }\n\n  advance() {\n    this.pos = this.nextIndex(this.pos)\n  }\n\n  eat(ch) {\n    if (this.current() === ch) {\n      this.advance()\n      return true\n    }\n    return false\n  }\n}\n\nfunction codePointToString(ch) {\n  if (ch <= 0xFFFF) return String.fromCharCode(ch)\n  ch -= 0x10000\n  return String.fromCharCode((ch >> 10) + 0xD800, (ch & 0x03FF) + 0xDC00)\n}\n\n/**\n * Validate the flags part of a given RegExpLiteral.\n *\n * @param {RegExpValidationState} state The state to validate RegExp.\n * @returns {void}\n */\npp.validateRegExpFlags = function(state) {\n  const validFlags = state.validFlags\n  const flags = state.flags\n\n  for (let i = 0; i < flags.length; i++) {\n    const flag = flags.charAt(i)\n    if (validFlags.indexOf(flag) === -1) {\n      this.raise(state.start, \"Invalid regular expression flag\")\n    }\n    if (flags.indexOf(flag, i + 1) > -1) {\n      this.raise(state.start, \"Duplicate regular expression flag\")\n    }\n  }\n}\n\n/**\n * Validate the pattern part of a given RegExpLiteral.\n *\n * @param {RegExpValidationState} state The state to validate RegExp.\n * @returns {void}\n */\npp.validateRegExpPattern = function(state) {\n  this.regexp_pattern(state)\n\n  // The goal symbol for the parse is |Pattern[~U, ~N]|. If the result of\n  // parsing contains a |GroupName|, reparse with the goal symbol\n  // |Pattern[~U, +N]| and use this result instead. Throw a *SyntaxError*\n  // exception if _P_ did not conform to the grammar, if any elements of _P_\n  // were not matched by the parse, or if any Early Error conditions exist.\n  if (!state.switchN && this.options.ecmaVersion >= 9 && state.groupNames.length > 0) {\n    state.switchN = true\n    this.regexp_pattern(state)\n  }\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Pattern\npp.regexp_pattern = function(state) {\n  state.pos = 0\n  state.lastIntValue = 0\n  state.lastStringValue = \"\"\n  state.lastAssertionIsQuantifiable = false\n  state.numCapturingParens = 0\n  state.maxBackReference = 0\n  state.groupNames.length = 0\n  state.backReferenceNames.length = 0\n\n  this.regexp_disjunction(state)\n\n  if (state.pos !== state.source.length) {\n    // Make the same messages as V8.\n    if (state.eat(0x29 /* ) */)) {\n      state.raise(\"Unmatched ')'\")\n    }\n    if (state.eat(0x5D /* [ */) || state.eat(0x7D /* } */)) {\n      state.raise(\"Lone quantifier brackets\")\n    }\n  }\n  if (state.maxBackReference > state.numCapturingParens) {\n    state.raise(\"Invalid escape\")\n  }\n  for (const name of state.backReferenceNames) {\n    if (state.groupNames.indexOf(name) === -1) {\n      state.raise(\"Invalid named capture referenced\")\n    }\n  }\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Disjunction\npp.regexp_disjunction = function(state) {\n  this.regexp_alternative(state)\n  while (state.eat(0x7C /* | */)) {\n    this.regexp_alternative(state)\n  }\n\n  // Make the same message as V8.\n  if (this.regexp_eatQuantifier(state, true)) {\n    state.raise(\"Nothing to repeat\")\n  }\n  if (state.eat(0x7B /* { */)) {\n    state.raise(\"Lone quantifier brackets\")\n  }\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Alternative\npp.regexp_alternative = function(state) {\n  while (state.pos < state.source.length && this.regexp_eatTerm(state))\n    ;\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-Term\npp.regexp_eatTerm = function(state) {\n  if (this.regexp_eatAssertion(state)) {\n    // Handle `QuantifiableAssertion Quantifier` alternative.\n    // `state.lastAssertionIsQuantifiable` is true if the last eaten Assertion\n    // is a QuantifiableAssertion.\n    if (state.lastAssertionIsQuantifiable && this.regexp_eatQuantifier(state)) {\n      // Make the same message as V8.\n      if (state.switchU) {\n        state.raise(\"Invalid quantifier\")\n      }\n    }\n    return true\n  }\n\n  if (state.switchU ? this.regexp_eatAtom(state) : this.regexp_eatExtendedAtom(state)) {\n    this.regexp_eatQuantifier(state)\n    return true\n  }\n\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-Assertion\npp.regexp_eatAssertion = function(state) {\n  const start = state.pos\n  state.lastAssertionIsQuantifiable = false\n\n  // ^, $\n  if (state.eat(0x5E /* ^ */) || state.eat(0x24 /* $ */)) {\n    return true\n  }\n\n  // \\b \\B\n  if (state.eat(0x5C /* \\ */)) {\n    if (state.eat(0x42 /* B */) || state.eat(0x62 /* b */)) {\n      return true\n    }\n    state.pos = start\n  }\n\n  // Lookahead / Lookbehind\n  if (state.eat(0x28 /* ( */) && state.eat(0x3F /* ? */)) {\n    let lookbehind = false\n    if (this.options.ecmaVersion >= 9) {\n      lookbehind = state.eat(0x3C /* < */)\n    }\n    if (state.eat(0x3D /* = */) || state.eat(0x21 /* ! */)) {\n      this.regexp_disjunction(state)\n      if (!state.eat(0x29 /* ) */)) {\n        state.raise(\"Unterminated group\")\n      }\n      state.lastAssertionIsQuantifiable = !lookbehind\n      return true\n    }\n  }\n\n  state.pos = start\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Quantifier\npp.regexp_eatQuantifier = function(state, noError = false) {\n  if (this.regexp_eatQuantifierPrefix(state, noError)) {\n    state.eat(0x3F /* ? */)\n    return true\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-QuantifierPrefix\npp.regexp_eatQuantifierPrefix = function(state, noError) {\n  return (\n    state.eat(0x2A /* * */) ||\n    state.eat(0x2B /* + */) ||\n    state.eat(0x3F /* ? */) ||\n    this.regexp_eatBracedQuantifier(state, noError)\n  )\n}\npp.regexp_eatBracedQuantifier = function(state, noError) {\n  const start = state.pos\n  if (state.eat(0x7B /* { */)) {\n    let min = 0, max = -1\n    if (this.regexp_eatDecimalDigits(state)) {\n      min = state.lastIntValue\n      if (state.eat(0x2C /* , */) && this.regexp_eatDecimalDigits(state)) {\n        max = state.lastIntValue\n      }\n      if (state.eat(0x7D /* } */)) {\n        // SyntaxError in https://www.ecma-international.org/ecma-262/8.0/#sec-term\n        if (max !== -1 && max < min && !noError) {\n          state.raise(\"numbers out of order in {} quantifier\")\n        }\n        return true\n      }\n    }\n    if (state.switchU && !noError) {\n      state.raise(\"Incomplete quantifier\")\n    }\n    state.pos = start\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Atom\npp.regexp_eatAtom = function(state) {\n  return (\n    this.regexp_eatPatternCharacters(state) ||\n    state.eat(0x2E /* . */) ||\n    this.regexp_eatReverseSolidusAtomEscape(state) ||\n    this.regexp_eatCharacterClass(state) ||\n    this.regexp_eatUncapturingGroup(state) ||\n    this.regexp_eatCapturingGroup(state)\n  )\n}\npp.regexp_eatReverseSolidusAtomEscape = function(state) {\n  const start = state.pos\n  if (state.eat(0x5C /* \\ */)) {\n    if (this.regexp_eatAtomEscape(state)) {\n      return true\n    }\n    state.pos = start\n  }\n  return false\n}\npp.regexp_eatUncapturingGroup = function(state) {\n  const start = state.pos\n  if (state.eat(0x28 /* ( */)) {\n    if (state.eat(0x3F /* ? */) && state.eat(0x3A /* : */)) {\n      this.regexp_disjunction(state)\n      if (state.eat(0x29 /* ) */)) {\n        return true\n      }\n      state.raise(\"Unterminated group\")\n    }\n    state.pos = start\n  }\n  return false\n}\npp.regexp_eatCapturingGroup = function(state) {\n  if (state.eat(0x28 /* ( */)) {\n    if (this.options.ecmaVersion >= 9) {\n      this.regexp_groupSpecifier(state)\n    } else if (state.current() === 0x3F /* ? */) {\n      state.raise(\"Invalid group\")\n    }\n    this.regexp_disjunction(state)\n    if (state.eat(0x29 /* ) */)) {\n      state.numCapturingParens += 1\n      return true\n    }\n    state.raise(\"Unterminated group\")\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ExtendedAtom\npp.regexp_eatExtendedAtom = function(state) {\n  return (\n    state.eat(0x2E /* . */) ||\n    this.regexp_eatReverseSolidusAtomEscape(state) ||\n    this.regexp_eatCharacterClass(state) ||\n    this.regexp_eatUncapturingGroup(state) ||\n    this.regexp_eatCapturingGroup(state) ||\n    this.regexp_eatInvalidBracedQuantifier(state) ||\n    this.regexp_eatExtendedPatternCharacter(state)\n  )\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-InvalidBracedQuantifier\npp.regexp_eatInvalidBracedQuantifier = function(state) {\n  if (this.regexp_eatBracedQuantifier(state, true)) {\n    state.raise(\"Nothing to repeat\")\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-SyntaxCharacter\npp.regexp_eatSyntaxCharacter = function(state) {\n  const ch = state.current()\n  if (isSyntaxCharacter(ch)) {\n    state.lastIntValue = ch\n    state.advance()\n    return true\n  }\n  return false\n}\nfunction isSyntaxCharacter(ch) {\n  return (\n    ch === 0x24 /* $ */ ||\n    ch >= 0x28 /* ( */ && ch <= 0x2B /* + */ ||\n    ch === 0x2E /* . */ ||\n    ch === 0x3F /* ? */ ||\n    ch >= 0x5B /* [ */ && ch <= 0x5E /* ^ */ ||\n    ch >= 0x7B /* { */ && ch <= 0x7D /* } */\n  )\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-PatternCharacter\n// But eat eager.\npp.regexp_eatPatternCharacters = function(state) {\n  const start = state.pos\n  let ch = 0\n  while ((ch = state.current()) !== -1 && !isSyntaxCharacter(ch)) {\n    state.advance()\n  }\n  return state.pos !== start\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ExtendedPatternCharacter\npp.regexp_eatExtendedPatternCharacter = function(state) {\n  const ch = state.current()\n  if (\n    ch !== -1 &&\n    ch !== 0x24 /* $ */ &&\n    !(ch >= 0x28 /* ( */ && ch <= 0x2B /* + */) &&\n    ch !== 0x2E /* . */ &&\n    ch !== 0x3F /* ? */ &&\n    ch !== 0x5B /* [ */ &&\n    ch !== 0x5E /* ^ */ &&\n    ch !== 0x7C /* | */\n  ) {\n    state.advance()\n    return true\n  }\n  return false\n}\n\n// GroupSpecifier[U] ::\n//   [empty]\n//   `?` GroupName[?U]\npp.regexp_groupSpecifier = function(state) {\n  if (state.eat(0x3F /* ? */)) {\n    if (this.regexp_eatGroupName(state)) {\n      if (state.groupNames.indexOf(state.lastStringValue) !== -1) {\n        state.raise(\"Duplicate capture group name\")\n      }\n      state.groupNames.push(state.lastStringValue)\n      return\n    }\n    state.raise(\"Invalid group\")\n  }\n}\n\n// GroupName[U] ::\n//   `<` RegExpIdentifierName[?U] `>`\n// Note: this updates `state.lastStringValue` property with the eaten name.\npp.regexp_eatGroupName = function(state) {\n  state.lastStringValue = \"\"\n  if (state.eat(0x3C /* < */)) {\n    if (this.regexp_eatRegExpIdentifierName(state) && state.eat(0x3E /* > */)) {\n      return true\n    }\n    state.raise(\"Invalid capture group name\")\n  }\n  return false\n}\n\n// RegExpIdentifierName[U] ::\n//   RegExpIdentifierStart[?U]\n//   RegExpIdentifierName[?U] RegExpIdentifierPart[?U]\n// Note: this updates `state.lastStringValue` property with the eaten name.\npp.regexp_eatRegExpIdentifierName = function(state) {\n  state.lastStringValue = \"\"\n  if (this.regexp_eatRegExpIdentifierStart(state)) {\n    state.lastStringValue += codePointToString(state.lastIntValue)\n    while (this.regexp_eatRegExpIdentifierPart(state)) {\n      state.lastStringValue += codePointToString(state.lastIntValue)\n    }\n    return true\n  }\n  return false\n}\n\n// RegExpIdentifierStart[U] ::\n//   UnicodeIDStart\n//   `$`\n//   `_`\n//   `\\` RegExpUnicodeEscapeSequence[?U]\npp.regexp_eatRegExpIdentifierStart = function(state) {\n  const start = state.pos\n  let ch = state.current()\n  state.advance()\n\n  if (ch === 0x5C /* \\ */ && this.regexp_eatRegExpUnicodeEscapeSequence(state)) {\n    ch = state.lastIntValue\n  }\n  if (isRegExpIdentifierStart(ch)) {\n    state.lastIntValue = ch\n    return true\n  }\n\n  state.pos = start\n  return false\n}\nfunction isRegExpIdentifierStart(ch) {\n  return isIdentifierStart(ch, true) || ch === 0x24 /* $ */ || ch === 0x5F /* _ */\n}\n\n// RegExpIdentifierPart[U] ::\n//   UnicodeIDContinue\n//   `$`\n//   `_`\n//   `\\` RegExpUnicodeEscapeSequence[?U]\n//   <ZWNJ>\n//   <ZWJ>\npp.regexp_eatRegExpIdentifierPart = function(state) {\n  const start = state.pos\n  let ch = state.current()\n  state.advance()\n\n  if (ch === 0x5C /* \\ */ && this.regexp_eatRegExpUnicodeEscapeSequence(state)) {\n    ch = state.lastIntValue\n  }\n  if (isRegExpIdentifierPart(ch)) {\n    state.lastIntValue = ch\n    return true\n  }\n\n  state.pos = start\n  return false\n}\nfunction isRegExpIdentifierPart(ch) {\n  return isIdentifierChar(ch, true) || ch === 0x24 /* $ */ || ch === 0x5F /* _ */ || ch === 0x200C /* <ZWNJ> */ || ch === 0x200D /* <ZWJ> */\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-AtomEscape\npp.regexp_eatAtomEscape = function(state) {\n  if (\n    this.regexp_eatBackReference(state) ||\n    this.regexp_eatCharacterClassEscape(state) ||\n    this.regexp_eatCharacterEscape(state) ||\n    (state.switchN && this.regexp_eatKGroupName(state))\n  ) {\n    return true\n  }\n  if (state.switchU) {\n    // Make the same message as V8.\n    if (state.current() === 0x63 /* c */) {\n      state.raise(\"Invalid unicode escape\")\n    }\n    state.raise(\"Invalid escape\")\n  }\n  return false\n}\npp.regexp_eatBackReference = function(state) {\n  const start = state.pos\n  if (this.regexp_eatDecimalEscape(state)) {\n    const n = state.lastIntValue\n    if (state.switchU) {\n      // For SyntaxError in https://www.ecma-international.org/ecma-262/8.0/#sec-atomescape\n      if (n > state.maxBackReference) {\n        state.maxBackReference = n\n      }\n      return true\n    }\n    if (n <= state.numCapturingParens) {\n      return true\n    }\n    state.pos = start\n  }\n  return false\n}\npp.regexp_eatKGroupName = function(state) {\n  if (state.eat(0x6B /* k */)) {\n    if (this.regexp_eatGroupName(state)) {\n      state.backReferenceNames.push(state.lastStringValue)\n      return true\n    }\n    state.raise(\"Invalid named reference\")\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-CharacterEscape\npp.regexp_eatCharacterEscape = function(state) {\n  return (\n    this.regexp_eatControlEscape(state) ||\n    this.regexp_eatCControlLetter(state) ||\n    this.regexp_eatZero(state) ||\n    this.regexp_eatHexEscapeSequence(state) ||\n    this.regexp_eatRegExpUnicodeEscapeSequence(state) ||\n    (!state.switchU && this.regexp_eatLegacyOctalEscapeSequence(state)) ||\n    this.regexp_eatIdentityEscape(state)\n  )\n}\npp.regexp_eatCControlLetter = function(state) {\n  const start = state.pos\n  if (state.eat(0x63 /* c */)) {\n    if (this.regexp_eatControlLetter(state)) {\n      return true\n    }\n    state.pos = start\n  }\n  return false\n}\npp.regexp_eatZero = function(state) {\n  if (state.current() === 0x30 /* 0 */ && !isDecimalDigit(state.lookahead())) {\n    state.lastIntValue = 0\n    state.advance()\n    return true\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ControlEscape\npp.regexp_eatControlEscape = function(state) {\n  const ch = state.current()\n  if (ch === 0x74 /* t */) {\n    state.lastIntValue = 0x09 /* \\t */\n    state.advance()\n    return true\n  }\n  if (ch === 0x6E /* n */) {\n    state.lastIntValue = 0x0A /* \\n */\n    state.advance()\n    return true\n  }\n  if (ch === 0x76 /* v */) {\n    state.lastIntValue = 0x0B /* \\v */\n    state.advance()\n    return true\n  }\n  if (ch === 0x66 /* f */) {\n    state.lastIntValue = 0x0C /* \\f */\n    state.advance()\n    return true\n  }\n  if (ch === 0x72 /* r */) {\n    state.lastIntValue = 0x0D /* \\r */\n    state.advance()\n    return true\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ControlLetter\npp.regexp_eatControlLetter = function(state) {\n  const ch = state.current()\n  if (isControlLetter(ch)) {\n    state.lastIntValue = ch % 0x20\n    state.advance()\n    return true\n  }\n  return false\n}\nfunction isControlLetter(ch) {\n  return (\n    (ch >= 0x41 /* A */ && ch <= 0x5A /* Z */) ||\n    (ch >= 0x61 /* a */ && ch <= 0x7A /* z */)\n  )\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-RegExpUnicodeEscapeSequence\npp.regexp_eatRegExpUnicodeEscapeSequence = function(state) {\n  const start = state.pos\n\n  if (state.eat(0x75 /* u */)) {\n    if (this.regexp_eatFixedHexDigits(state, 4)) {\n      const lead = state.lastIntValue\n      if (state.switchU && lead >= 0xD800 && lead <= 0xDBFF) {\n        const leadSurrogateEnd = state.pos\n        if (state.eat(0x5C /* \\ */) && state.eat(0x75 /* u */) && this.regexp_eatFixedHexDigits(state, 4)) {\n          const trail = state.lastIntValue\n          if (trail >= 0xDC00 && trail <= 0xDFFF) {\n            state.lastIntValue = (lead - 0xD800) * 0x400 + (trail - 0xDC00) + 0x10000\n            return true\n          }\n        }\n        state.pos = leadSurrogateEnd\n        state.lastIntValue = lead\n      }\n      return true\n    }\n    if (\n      state.switchU &&\n      state.eat(0x7B /* { */) &&\n      this.regexp_eatHexDigits(state) &&\n      state.eat(0x7D /* } */) &&\n      isValidUnicode(state.lastIntValue)\n    ) {\n      return true\n    }\n    if (state.switchU) {\n      state.raise(\"Invalid unicode escape\")\n    }\n    state.pos = start\n  }\n\n  return false\n}\nfunction isValidUnicode(ch) {\n  return ch >= 0 && ch <= 0x10FFFF\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-IdentityEscape\npp.regexp_eatIdentityEscape = function(state) {\n  if (state.switchU) {\n    if (this.regexp_eatSyntaxCharacter(state)) {\n      return true\n    }\n    if (state.eat(0x2F /* / */)) {\n      state.lastIntValue = 0x2F /* / */\n      return true\n    }\n    return false\n  }\n\n  const ch = state.current()\n  if (ch !== 0x63 /* c */ && (!state.switchN || ch !== 0x6B /* k */)) {\n    state.lastIntValue = ch\n    state.advance()\n    return true\n  }\n\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-DecimalEscape\npp.regexp_eatDecimalEscape = function(state) {\n  state.lastIntValue = 0\n  let ch = state.current()\n  if (ch >= 0x31 /* 1 */ && ch <= 0x39 /* 9 */) {\n    do {\n      state.lastIntValue = 10 * state.lastIntValue + (ch - 0x30 /* 0 */)\n      state.advance()\n    } while ((ch = state.current()) >= 0x30 /* 0 */ && ch <= 0x39 /* 9 */)\n    return true\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-CharacterClassEscape\npp.regexp_eatCharacterClassEscape = function(state) {\n  const ch = state.current()\n\n  if (isCharacterClassEscape(ch)) {\n    state.lastIntValue = -1\n    state.advance()\n    return true\n  }\n\n  if (\n    state.switchU &&\n    this.options.ecmaVersion >= 9 &&\n    (ch === 0x50 /* P */ || ch === 0x70 /* p */)\n  ) {\n    state.lastIntValue = -1\n    state.advance()\n    if (\n      state.eat(0x7B /* { */) &&\n      this.regexp_eatUnicodePropertyValueExpression(state) &&\n      state.eat(0x7D /* } */)\n    ) {\n      return true\n    }\n    state.raise(\"Invalid property name\")\n  }\n\n  return false\n}\nfunction isCharacterClassEscape(ch) {\n  return (\n    ch === 0x64 /* d */ ||\n    ch === 0x44 /* D */ ||\n    ch === 0x73 /* s */ ||\n    ch === 0x53 /* S */ ||\n    ch === 0x77 /* w */ ||\n    ch === 0x57 /* W */\n  )\n}\n\n// UnicodePropertyValueExpression ::\n//   UnicodePropertyName `=` UnicodePropertyValue\n//   LoneUnicodePropertyNameOrValue\npp.regexp_eatUnicodePropertyValueExpression = function(state) {\n  const start = state.pos\n\n  // UnicodePropertyName `=` UnicodePropertyValue\n  if (this.regexp_eatUnicodePropertyName(state) && state.eat(0x3D /* = */)) {\n    const name = state.lastStringValue\n    if (this.regexp_eatUnicodePropertyValue(state)) {\n      const value = state.lastStringValue\n      this.regexp_validateUnicodePropertyNameAndValue(state, name, value)\n      return true\n    }\n  }\n  state.pos = start\n\n  // LoneUnicodePropertyNameOrValue\n  if (this.regexp_eatLoneUnicodePropertyNameOrValue(state)) {\n    const nameOrValue = state.lastStringValue\n    this.regexp_validateUnicodePropertyNameOrValue(state, nameOrValue)\n    return true\n  }\n  return false\n}\npp.regexp_validateUnicodePropertyNameAndValue = function(state, name, value) {\n  if (!has(state.unicodeProperties.nonBinary, name))\n    state.raise(\"Invalid property name\")\n  if (!state.unicodeProperties.nonBinary[name].test(value))\n    state.raise(\"Invalid property value\")\n}\npp.regexp_validateUnicodePropertyNameOrValue = function(state, nameOrValue) {\n  if (!state.unicodeProperties.binary.test(nameOrValue))\n    state.raise(\"Invalid property name\")\n}\n\n// UnicodePropertyName ::\n//   UnicodePropertyNameCharacters\npp.regexp_eatUnicodePropertyName = function(state) {\n  let ch = 0\n  state.lastStringValue = \"\"\n  while (isUnicodePropertyNameCharacter(ch = state.current())) {\n    state.lastStringValue += codePointToString(ch)\n    state.advance()\n  }\n  return state.lastStringValue !== \"\"\n}\nfunction isUnicodePropertyNameCharacter(ch) {\n  return isControlLetter(ch) || ch === 0x5F /* _ */\n}\n\n// UnicodePropertyValue ::\n//   UnicodePropertyValueCharacters\npp.regexp_eatUnicodePropertyValue = function(state) {\n  let ch = 0\n  state.lastStringValue = \"\"\n  while (isUnicodePropertyValueCharacter(ch = state.current())) {\n    state.lastStringValue += codePointToString(ch)\n    state.advance()\n  }\n  return state.lastStringValue !== \"\"\n}\nfunction isUnicodePropertyValueCharacter(ch) {\n  return isUnicodePropertyNameCharacter(ch) || isDecimalDigit(ch)\n}\n\n// LoneUnicodePropertyNameOrValue ::\n//   UnicodePropertyValueCharacters\npp.regexp_eatLoneUnicodePropertyNameOrValue = function(state) {\n  return this.regexp_eatUnicodePropertyValue(state)\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-CharacterClass\npp.regexp_eatCharacterClass = function(state) {\n  if (state.eat(0x5B /* [ */)) {\n    state.eat(0x5E /* ^ */)\n    this.regexp_classRanges(state)\n    if (state.eat(0x5D /* [ */)) {\n      return true\n    }\n    // Unreachable since it threw \"unterminated regular expression\" error before.\n    state.raise(\"Unterminated character class\")\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ClassRanges\n// https://www.ecma-international.org/ecma-262/8.0/#prod-NonemptyClassRanges\n// https://www.ecma-international.org/ecma-262/8.0/#prod-NonemptyClassRangesNoDash\npp.regexp_classRanges = function(state) {\n  while (this.regexp_eatClassAtom(state)) {\n    const left = state.lastIntValue\n    if (state.eat(0x2D /* - */) && this.regexp_eatClassAtom(state)) {\n      const right = state.lastIntValue\n      if (state.switchU && (left === -1 || right === -1)) {\n        state.raise(\"Invalid character class\")\n      }\n      if (left !== -1 && right !== -1 && left > right) {\n        state.raise(\"Range out of order in character class\")\n      }\n    }\n  }\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ClassAtom\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ClassAtomNoDash\npp.regexp_eatClassAtom = function(state) {\n  const start = state.pos\n\n  if (state.eat(0x5C /* \\ */)) {\n    if (this.regexp_eatClassEscape(state)) {\n      return true\n    }\n    if (state.switchU) {\n      // Make the same message as V8.\n      const ch = state.current()\n      if (ch === 0x63 /* c */ || isOctalDigit(ch)) {\n        state.raise(\"Invalid class escape\")\n      }\n      state.raise(\"Invalid escape\")\n    }\n    state.pos = start\n  }\n\n  const ch = state.current()\n  if (ch !== 0x5D /* [ */) {\n    state.lastIntValue = ch\n    state.advance()\n    return true\n  }\n\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ClassEscape\npp.regexp_eatClassEscape = function(state) {\n  const start = state.pos\n\n  if (state.eat(0x62 /* b */)) {\n    state.lastIntValue = 0x08 /* <BS> */\n    return true\n  }\n\n  if (state.switchU && state.eat(0x2D /* - */)) {\n    state.lastIntValue = 0x2D /* - */\n    return true\n  }\n\n  if (!state.switchU && state.eat(0x63 /* c */)) {\n    if (this.regexp_eatClassControlLetter(state)) {\n      return true\n    }\n    state.pos = start\n  }\n\n  return (\n    this.regexp_eatCharacterClassEscape(state) ||\n    this.regexp_eatCharacterEscape(state)\n  )\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ClassControlLetter\npp.regexp_eatClassControlLetter = function(state) {\n  const ch = state.current()\n  if (isDecimalDigit(ch) || ch === 0x5F /* _ */) {\n    state.lastIntValue = ch % 0x20\n    state.advance()\n    return true\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-HexEscapeSequence\npp.regexp_eatHexEscapeSequence = function(state) {\n  const start = state.pos\n  if (state.eat(0x78 /* x */)) {\n    if (this.regexp_eatFixedHexDigits(state, 2)) {\n      return true\n    }\n    if (state.switchU) {\n      state.raise(\"Invalid escape\")\n    }\n    state.pos = start\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-DecimalDigits\npp.regexp_eatDecimalDigits = function(state) {\n  const start = state.pos\n  let ch = 0\n  state.lastIntValue = 0\n  while (isDecimalDigit(ch = state.current())) {\n    state.lastIntValue = 10 * state.lastIntValue + (ch - 0x30 /* 0 */)\n    state.advance()\n  }\n  return state.pos !== start\n}\nfunction isDecimalDigit(ch) {\n  return ch >= 0x30 /* 0 */ && ch <= 0x39 /* 9 */\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-HexDigits\npp.regexp_eatHexDigits = function(state) {\n  const start = state.pos\n  let ch = 0\n  state.lastIntValue = 0\n  while (isHexDigit(ch = state.current())) {\n    state.lastIntValue = 16 * state.lastIntValue + hexToInt(ch)\n    state.advance()\n  }\n  return state.pos !== start\n}\nfunction isHexDigit(ch) {\n  return (\n    (ch >= 0x30 /* 0 */ && ch <= 0x39 /* 9 */) ||\n    (ch >= 0x41 /* A */ && ch <= 0x46 /* F */) ||\n    (ch >= 0x61 /* a */ && ch <= 0x66 /* f */)\n  )\n}\nfunction hexToInt(ch) {\n  if (ch >= 0x41 /* A */ && ch <= 0x46 /* F */) {\n    return 10 + (ch - 0x41 /* A */)\n  }\n  if (ch >= 0x61 /* a */ && ch <= 0x66 /* f */) {\n    return 10 + (ch - 0x61 /* a */)\n  }\n  return ch - 0x30 /* 0 */\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-LegacyOctalEscapeSequence\n// Allows only 0-377(octal) i.e. 0-255(decimal).\npp.regexp_eatLegacyOctalEscapeSequence = function(state) {\n  if (this.regexp_eatOctalDigit(state)) {\n    const n1 = state.lastIntValue\n    if (this.regexp_eatOctalDigit(state)) {\n      const n2 = state.lastIntValue\n      if (n1 <= 3 && this.regexp_eatOctalDigit(state)) {\n        state.lastIntValue = n1 * 64 + n2 * 8 + state.lastIntValue\n      } else {\n        state.lastIntValue = n1 * 8 + n2\n      }\n    } else {\n      state.lastIntValue = n1\n    }\n    return true\n  }\n  return false\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-OctalDigit\npp.regexp_eatOctalDigit = function(state) {\n  const ch = state.current()\n  if (isOctalDigit(ch)) {\n    state.lastIntValue = ch - 0x30 /* 0 */\n    state.advance()\n    return true\n  }\n  state.lastIntValue = 0\n  return false\n}\nfunction isOctalDigit(ch) {\n  return ch >= 0x30 /* 0 */ && ch <= 0x37 /* 7 */\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Hex4Digits\n// https://www.ecma-international.org/ecma-262/8.0/#prod-HexDigit\n// And HexDigit HexDigit in https://www.ecma-international.org/ecma-262/8.0/#prod-HexEscapeSequence\npp.regexp_eatFixedHexDigits = function(state, length) {\n  const start = state.pos\n  state.lastIntValue = 0\n  for (let i = 0; i < length; ++i) {\n    const ch = state.current()\n    if (!isHexDigit(ch)) {\n      state.pos = start\n      return false\n    }\n    state.lastIntValue = 16 * state.lastIntValue + hexToInt(ch)\n    state.advance()\n  }\n  return true\n}\n", "import {isIdentifierStart, isIdentifierChar} from \"./identifier\"\nimport {types as tt, keywords as keywordTypes} from \"./tokentype\"\nimport {Parser} from \"./state\"\nimport {SourceLocation} from \"./locutil\"\nimport {RegExpValidationState} from \"./regexp\"\nimport {lineBreak, lineBreakG, isNewLine, nonASCIIwhitespace} from \"./whitespace\"\n\n// Object type used to represent tokens. Note that normally, tokens\n// simply exist as properties on the parser object. This is only\n// used for the onToken callback and the external tokenizer.\n\nexport class Token {\n  constructor(p) {\n    this.type = p.type\n    this.value = p.value\n    this.start = p.start\n    this.end = p.end\n    if (p.options.locations)\n      this.loc = new SourceLocation(p, p.startLoc, p.endLoc)\n    if (p.options.ranges)\n      this.range = [p.start, p.end]\n  }\n}\n\n// ## Tokenizer\n\nconst pp = Parser.prototype\n\n// Move to the next token\n\npp.next = function() {\n  if (this.options.onToken)\n    this.options.onToken(new Token(this))\n\n  this.lastTokEnd = this.end\n  this.lastTokStart = this.start\n  this.lastTokEndLoc = this.endLoc\n  this.lastTokStartLoc = this.startLoc\n  this.nextToken()\n}\n\npp.getToken = function() {\n  this.next()\n  return new Token(this)\n}\n\n// If we're in an ES6 environment, make parsers iterable\nif (typeof Symbol !== \"undefined\")\n  pp[Symbol.iterator] = function() {\n    return {\n      next: () => {\n        let token = this.getToken()\n        return {\n          done: token.type === tt.eof,\n          value: token\n        }\n      }\n    }\n  }\n\n// Toggle strict mode. Re-reads the next number or string to please\n// pedantic tests (`\"use strict\"; 010;` should fail).\n\npp.curContext = function() {\n  return this.context[this.context.length - 1]\n}\n\n// Read a single token, updating the parser object's token-related\n// properties.\n\npp.nextToken = function() {\n  let curContext = this.curContext()\n  if (!curContext || !curContext.preserveSpace) this.skipSpace()\n\n  this.start = this.pos\n  if (this.options.locations) this.startLoc = this.curPosition()\n  if (this.pos >= this.input.length) return this.finishToken(tt.eof)\n\n  if (curContext.override) return curContext.override(this)\n  else this.readToken(this.fullCharCodeAtPos())\n}\n\npp.readToken = function(code) {\n  // Identifier or keyword. '\\uXXXX' sequences are allowed in\n  // identifiers, so '\\' also dispatches to that.\n  if (isIdentifierStart(code, this.options.ecmaVersion >= 6) || code === 92 /* '\\' */)\n    return this.readWord()\n\n  return this.getTokenFromCode(code)\n}\n\npp.fullCharCodeAtPos = function() {\n  let code = this.input.charCodeAt(this.pos)\n  if (code <= 0xd7ff || code >= 0xe000) return code\n  let next = this.input.charCodeAt(this.pos + 1)\n  return (code << 10) + next - 0x35fdc00\n}\n\npp.skipBlockComment = function() {\n  let startLoc = this.options.onComment && this.curPosition()\n  let start = this.pos, end = this.input.indexOf(\"*/\", this.pos += 2)\n  if (end === -1) this.raise(this.pos - 2, \"Unterminated comment\")\n  this.pos = end + 2\n  if (this.options.locations) {\n    lineBreakG.lastIndex = start\n    let match\n    while ((match = lineBreakG.exec(this.input)) && match.index < this.pos) {\n      ++this.curLine\n      this.lineStart = match.index + match[0].length\n    }\n  }\n  if (this.options.onComment)\n    this.options.onComment(true, this.input.slice(start + 2, end), start, this.pos,\n                           startLoc, this.curPosition())\n}\n\npp.skipLineComment = function(startSkip) {\n  let start = this.pos\n  let startLoc = this.options.onComment && this.curPosition()\n  let ch = this.input.charCodeAt(this.pos += startSkip)\n  while (this.pos < this.input.length && !isNewLine(ch)) {\n    ch = this.input.charCodeAt(++this.pos)\n  }\n  if (this.options.onComment)\n    this.options.onComment(false, this.input.slice(start + startSkip, this.pos), start, this.pos,\n                           startLoc, this.curPosition())\n}\n\n// Called at the start of the parse and after every token. Skips\n// whitespace and comments, and.\n\npp.skipSpace = function() {\n  loop: while (this.pos < this.input.length) {\n    let ch = this.input.charCodeAt(this.pos)\n    switch (ch) {\n    case 32: case 160: // ' '\n      ++this.pos\n      break\n    case 13:\n      if (this.input.charCodeAt(this.pos + 1) === 10) {\n        ++this.pos\n      }\n    case 10: case 8232: case 8233:\n      ++this.pos\n      if (this.options.locations) {\n        ++this.curLine\n        this.lineStart = this.pos\n      }\n      break\n    case 47: // '/'\n      switch (this.input.charCodeAt(this.pos + 1)) {\n      case 42: // '*'\n        this.skipBlockComment()\n        break\n      case 47:\n        this.skipLineComment(2)\n        break\n      default:\n        break loop\n      }\n      break\n    default:\n      if (ch > 8 && ch < 14 || ch >= 5760 && nonASCIIwhitespace.test(String.fromCharCode(ch))) {\n        ++this.pos\n      } else {\n        break loop\n      }\n    }\n  }\n}\n\n// Called at the end of every token. Sets `end`, `val`, and\n// maintains `context` and `exprAllowed`, and skips the space after\n// the token, so that the next one's `start` will point at the\n// right position.\n\npp.finishToken = function(type, val) {\n  this.end = this.pos\n  if (this.options.locations) this.endLoc = this.curPosition()\n  let prevType = this.type\n  this.type = type\n  this.value = val\n\n  this.updateContext(prevType)\n}\n\n// ### Token reading\n\n// This is the function that is called to fetch the next token. It\n// is somewhat obscure, because it works in character codes rather\n// than characters, and because operator parsing has been inlined\n// into it.\n//\n// All in the name of speed.\n//\npp.readToken_dot = function() {\n  let next = this.input.charCodeAt(this.pos + 1)\n  if (next >= 48 && next <= 57) return this.readNumber(true)\n  let next2 = this.input.charCodeAt(this.pos + 2)\n  if (this.options.ecmaVersion >= 6 && next === 46 && next2 === 46) { // 46 = dot '.'\n    this.pos += 3\n    return this.finishToken(tt.ellipsis)\n  } else {\n    ++this.pos\n    return this.finishToken(tt.dot)\n  }\n}\n\npp.readToken_slash = function() { // '/'\n  let next = this.input.charCodeAt(this.pos + 1)\n  if (this.exprAllowed) { ++this.pos; return this.readRegexp() }\n  if (next === 61) return this.finishOp(tt.assign, 2)\n  return this.finishOp(tt.slash, 1)\n}\n\npp.readToken_mult_modulo_exp = function(code) { // '%*'\n  let next = this.input.charCodeAt(this.pos + 1)\n  let size = 1\n  let tokentype = code === 42 ? tt.star : tt.modulo\n\n  // exponentiation operator ** and **=\n  if (this.options.ecmaVersion >= 7 && code === 42 && next === 42) {\n    ++size\n    tokentype = tt.starstar\n    next = this.input.charCodeAt(this.pos + 2)\n  }\n\n  if (next === 61) return this.finishOp(tt.assign, size + 1)\n  return this.finishOp(tokentype, size)\n}\n\npp.readToken_pipe_amp = function(code) { // '|&'\n  let next = this.input.charCodeAt(this.pos + 1)\n  if (next === code) return this.finishOp(code === 124 ? tt.logicalOR : tt.logicalAND, 2)\n  if (next === 61) return this.finishOp(tt.assign, 2)\n  return this.finishOp(code === 124 ? tt.bitwiseOR : tt.bitwiseAND, 1)\n}\n\npp.readToken_caret = function() { // '^'\n  let next = this.input.charCodeAt(this.pos + 1)\n  if (next === 61) return this.finishOp(tt.assign, 2)\n  return this.finishOp(tt.bitwiseXOR, 1)\n}\n\npp.readToken_plus_min = function(code) { // '+-'\n  let next = this.input.charCodeAt(this.pos + 1)\n  if (next === code) {\n    if (next === 45 && !this.inModule && this.input.charCodeAt(this.pos + 2) === 62 &&\n        (this.lastTokEnd === 0 || lineBreak.test(this.input.slice(this.lastTokEnd, this.pos)))) {\n      // A `-->` line comment\n      this.skipLineComment(3)\n      this.skipSpace()\n      return this.nextToken()\n    }\n    return this.finishOp(tt.incDec, 2)\n  }\n  if (next === 61) return this.finishOp(tt.assign, 2)\n  return this.finishOp(tt.plusMin, 1)\n}\n\npp.readToken_lt_gt = function(code) { // '<>'\n  let next = this.input.charCodeAt(this.pos + 1)\n  let size = 1\n  if (next === code) {\n    size = code === 62 && this.input.charCodeAt(this.pos + 2) === 62 ? 3 : 2\n    if (this.input.charCodeAt(this.pos + size) === 61) return this.finishOp(tt.assign, size + 1)\n    return this.finishOp(tt.bitShift, size)\n  }\n  if (next === 33 && code === 60 && !this.inModule && this.input.charCodeAt(this.pos + 2) === 45 &&\n      this.input.charCodeAt(this.pos + 3) === 45) {\n    // `<!--`, an XML-style comment that should be interpreted as a line comment\n    this.skipLineComment(4)\n    this.skipSpace()\n    return this.nextToken()\n  }\n  if (next === 61) size = 2\n  return this.finishOp(tt.relational, size)\n}\n\npp.readToken_eq_excl = function(code) { // '=!'\n  let next = this.input.charCodeAt(this.pos + 1)\n  if (next === 61) return this.finishOp(tt.equality, this.input.charCodeAt(this.pos + 2) === 61 ? 3 : 2)\n  if (code === 61 && next === 62 && this.options.ecmaVersion >= 6) { // '=>'\n    this.pos += 2\n    return this.finishToken(tt.arrow)\n  }\n  return this.finishOp(code === 61 ? tt.eq : tt.prefix, 1)\n}\n\npp.getTokenFromCode = function(code) {\n  switch (code) {\n  // The interpretation of a dot depends on whether it is followed\n  // by a digit or another two dots.\n  case 46: // '.'\n    return this.readToken_dot()\n\n  // Punctuation tokens.\n  case 40: ++this.pos; return this.finishToken(tt.parenL)\n  case 41: ++this.pos; return this.finishToken(tt.parenR)\n  case 59: ++this.pos; return this.finishToken(tt.semi)\n  case 44: ++this.pos; return this.finishToken(tt.comma)\n  case 91: ++this.pos; return this.finishToken(tt.bracketL)\n  case 93: ++this.pos; return this.finishToken(tt.bracketR)\n  case 123: ++this.pos; return this.finishToken(tt.braceL)\n  case 125: ++this.pos; return this.finishToken(tt.braceR)\n  case 58: ++this.pos; return this.finishToken(tt.colon)\n  case 63: ++this.pos; return this.finishToken(tt.question)\n\n  case 96: // '`'\n    if (this.options.ecmaVersion < 6) break\n    ++this.pos\n    return this.finishToken(tt.backQuote)\n\n  case 48: // '0'\n    let next = this.input.charCodeAt(this.pos + 1)\n    if (next === 120 || next === 88) return this.readRadixNumber(16) // '0x', '0X' - hex number\n    if (this.options.ecmaVersion >= 6) {\n      if (next === 111 || next === 79) return this.readRadixNumber(8) // '0o', '0O' - octal number\n      if (next === 98 || next === 66) return this.readRadixNumber(2) // '0b', '0B' - binary number\n    }\n\n  // Anything else beginning with a digit is an integer, octal\n  // number, or float.\n  case 49: case 50: case 51: case 52: case 53: case 54: case 55: case 56: case 57: // 1-9\n    return this.readNumber(false)\n\n  // Quotes produce strings.\n  case 34: case 39: // '\"', \"'\"\n    return this.readString(code)\n\n  // Operators are parsed inline in tiny state machines. '=' (61) is\n  // often referred to. `finishOp` simply skips the amount of\n  // characters it is given as second argument, and returns a token\n  // of the type given by its first argument.\n\n  case 47: // '/'\n    return this.readToken_slash()\n\n  case 37: case 42: // '%*'\n    return this.readToken_mult_modulo_exp(code)\n\n  case 124: case 38: // '|&'\n    return this.readToken_pipe_amp(code)\n\n  case 94: // '^'\n    return this.readToken_caret()\n\n  case 43: case 45: // '+-'\n    return this.readToken_plus_min(code)\n\n  case 60: case 62: // '<>'\n    return this.readToken_lt_gt(code)\n\n  case 61: case 33: // '=!'\n    return this.readToken_eq_excl(code)\n\n  case 126: // '~'\n    return this.finishOp(tt.prefix, 1)\n  }\n\n  this.raise(this.pos, \"Unexpected character '\" + codePointToString(code) + \"'\")\n}\n\npp.finishOp = function(type, size) {\n  let str = this.input.slice(this.pos, this.pos + size)\n  this.pos += size\n  return this.finishToken(type, str)\n}\n\npp.readRegexp = function() {\n  let escaped, inClass, start = this.pos\n  for (;;) {\n    if (this.pos >= this.input.length) this.raise(start, \"Unterminated regular expression\")\n    let ch = this.input.charAt(this.pos)\n    if (lineBreak.test(ch)) this.raise(start, \"Unterminated regular expression\")\n    if (!escaped) {\n      if (ch === \"[\") inClass = true\n      else if (ch === \"]\" && inClass) inClass = false\n      else if (ch === \"/\" && !inClass) break\n      escaped = ch === \"\\\\\"\n    } else escaped = false\n    ++this.pos\n  }\n  let pattern = this.input.slice(start, this.pos)\n  ++this.pos\n  let flagsStart = this.pos\n  let flags = this.readWord1()\n  if (this.containsEsc) this.unexpected(flagsStart)\n\n  // Validate pattern\n  const state = this.regexpState || (this.regexpState = new RegExpValidationState(this))\n  state.reset(start, pattern, flags)\n  this.validateRegExpFlags(state)\n  this.validateRegExpPattern(state)\n\n  // Create Literal#value property value.\n  let value = null\n  try {\n    value = new RegExp(pattern, flags)\n  } catch (e) {\n    // ESTree requires null if it failed to instantiate RegExp object.\n    // https://github.com/estree/estree/blob/a27003adf4fd7bfad44de9cef372a2eacd527b1c/es5.md#regexpliteral\n  }\n\n  return this.finishToken(tt.regexp, {pattern, flags, value})\n}\n\n// Read an integer in the given radix. Return null if zero digits\n// were read, the integer value otherwise. When `len` is given, this\n// will return `null` unless the integer has exactly `len` digits.\n\npp.readInt = function(radix, len) {\n  let start = this.pos, total = 0\n  for (let i = 0, e = len == null ? Infinity : len; i < e; ++i) {\n    let code = this.input.charCodeAt(this.pos), val\n    if (code >= 97) val = code - 97 + 10 // a\n    else if (code >= 65) val = code - 65 + 10 // A\n    else if (code >= 48 && code <= 57) val = code - 48 // 0-9\n    else val = Infinity\n    if (val >= radix) break\n    ++this.pos\n    total = total * radix + val\n  }\n  if (this.pos === start || len != null && this.pos - start !== len) return null\n\n  return total\n}\n\npp.readRadixNumber = function(radix) {\n  this.pos += 2 // 0x\n  let val = this.readInt(radix)\n  if (val == null) this.raise(this.start + 2, \"Expected number in radix \" + radix)\n  if (isIdentifierStart(this.fullCharCodeAtPos())) this.raise(this.pos, \"Identifier directly after number\")\n  return this.finishToken(tt.num, val)\n}\n\n// Read an integer, octal integer, or floating-point number.\n\npp.readNumber = function(startsWithDot) {\n  let start = this.pos\n  if (!startsWithDot && this.readInt(10) === null) this.raise(start, \"Invalid number\")\n  let octal = this.pos - start >= 2 && this.input.charCodeAt(start) === 48\n  if (octal && this.strict) this.raise(start, \"Invalid number\")\n  if (octal && /[89]/.test(this.input.slice(start, this.pos))) octal = false\n  let next = this.input.charCodeAt(this.pos)\n  if (next === 46 && !octal) { // '.'\n    ++this.pos\n    this.readInt(10)\n    next = this.input.charCodeAt(this.pos)\n  }\n  if ((next === 69 || next === 101) && !octal) { // 'eE'\n    next = this.input.charCodeAt(++this.pos)\n    if (next === 43 || next === 45) ++this.pos // '+-'\n    if (this.readInt(10) === null) this.raise(start, \"Invalid number\")\n  }\n  if (isIdentifierStart(this.fullCharCodeAtPos())) this.raise(this.pos, \"Identifier directly after number\")\n\n  let str = this.input.slice(start, this.pos)\n  let val = octal ? parseInt(str, 8) : parseFloat(str)\n  return this.finishToken(tt.num, val)\n}\n\n// Read a string value, interpreting backslash-escapes.\n\npp.readCodePoint = function() {\n  let ch = this.input.charCodeAt(this.pos), code\n\n  if (ch === 123) { // '{'\n    if (this.options.ecmaVersion < 6) this.unexpected()\n    let codePos = ++this.pos\n    code = this.readHexChar(this.input.indexOf(\"}\", this.pos) - this.pos)\n    ++this.pos\n    if (code > 0x10FFFF) this.invalidStringToken(codePos, \"Code point out of bounds\")\n  } else {\n    code = this.readHexChar(4)\n  }\n  return code\n}\n\nfunction codePointToString(code) {\n  // UTF-16 Decoding\n  if (code <= 0xFFFF) return String.fromCharCode(code)\n  code -= 0x10000\n  return String.fromCharCode((code >> 10) + 0xD800, (code & 1023) + 0xDC00)\n}\n\npp.readString = function(quote) {\n  let out = \"\", chunkStart = ++this.pos\n  for (;;) {\n    if (this.pos >= this.input.length) this.raise(this.start, \"Unterminated string constant\")\n    let ch = this.input.charCodeAt(this.pos)\n    if (ch === quote) break\n    if (ch === 92) { // '\\'\n      out += this.input.slice(chunkStart, this.pos)\n      out += this.readEscapedChar(false)\n      chunkStart = this.pos\n    } else {\n      if (isNewLine(ch, this.options.ecmaVersion >= 10)) this.raise(this.start, \"Unterminated string constant\")\n      ++this.pos\n    }\n  }\n  out += this.input.slice(chunkStart, this.pos++)\n  return this.finishToken(tt.string, out)\n}\n\n// Reads template string tokens.\n\nconst INVALID_TEMPLATE_ESCAPE_ERROR = {}\n\npp.tryReadTemplateToken = function() {\n  this.inTemplateElement = true\n  try {\n    this.readTmplToken()\n  } catch (err) {\n    if (err === INVALID_TEMPLATE_ESCAPE_ERROR) {\n      this.readInvalidTemplateToken()\n    } else {\n      throw err\n    }\n  }\n\n  this.inTemplateElement = false\n}\n\npp.invalidStringToken = function(position, message) {\n  if (this.inTemplateElement && this.options.ecmaVersion >= 9) {\n    throw INVALID_TEMPLATE_ESCAPE_ERROR\n  } else {\n    this.raise(position, message)\n  }\n}\n\npp.readTmplToken = function() {\n  let out = \"\", chunkStart = this.pos\n  for (;;) {\n    if (this.pos >= this.input.length) this.raise(this.start, \"Unterminated template\")\n    let ch = this.input.charCodeAt(this.pos)\n    if (ch === 96 || ch === 36 && this.input.charCodeAt(this.pos + 1) === 123) { // '`', '${'\n      if (this.pos === this.start && (this.type === tt.template || this.type === tt.invalidTemplate)) {\n        if (ch === 36) {\n          this.pos += 2\n          return this.finishToken(tt.dollarBraceL)\n        } else {\n          ++this.pos\n          return this.finishToken(tt.backQuote)\n        }\n      }\n      out += this.input.slice(chunkStart, this.pos)\n      return this.finishToken(tt.template, out)\n    }\n    if (ch === 92) { // '\\'\n      out += this.input.slice(chunkStart, this.pos)\n      out += this.readEscapedChar(true)\n      chunkStart = this.pos\n    } else if (isNewLine(ch)) {\n      out += this.input.slice(chunkStart, this.pos)\n      ++this.pos\n      switch (ch) {\n      case 13:\n        if (this.input.charCodeAt(this.pos) === 10) ++this.pos\n      case 10:\n        out += \"\\n\"\n        break\n      default:\n        out += String.fromCharCode(ch)\n        break\n      }\n      if (this.options.locations) {\n        ++this.curLine\n        this.lineStart = this.pos\n      }\n      chunkStart = this.pos\n    } else {\n      ++this.pos\n    }\n  }\n}\n\n// Reads a template token to search for the end, without validating any escape sequences\npp.readInvalidTemplateToken = function() {\n  for (; this.pos < this.input.length; this.pos++) {\n    switch (this.input[this.pos]) {\n    case \"\\\\\":\n      ++this.pos\n      break\n\n    case \"$\":\n      if (this.input[this.pos + 1] !== \"{\") {\n        break\n      }\n    // falls through\n\n    case \"`\":\n      return this.finishToken(tt.invalidTemplate, this.input.slice(this.start, this.pos))\n\n    // no default\n    }\n  }\n  this.raise(this.start, \"Unterminated template\")\n}\n\n// Used to read escaped characters\n\npp.readEscapedChar = function(inTemplate) {\n  let ch = this.input.charCodeAt(++this.pos)\n  ++this.pos\n  switch (ch) {\n  case 110: return \"\\n\" // 'n' -> '\\n'\n  case 114: return \"\\r\" // 'r' -> '\\r'\n  case 120: return String.fromCharCode(this.readHexChar(2)) // 'x'\n  case 117: return codePointToString(this.readCodePoint()) // 'u'\n  case 116: return \"\\t\" // 't' -> '\\t'\n  case 98: return \"\\b\" // 'b' -> '\\b'\n  case 118: return \"\\u000b\" // 'v' -> '\\u000b'\n  case 102: return \"\\f\" // 'f' -> '\\f'\n  case 13: if (this.input.charCodeAt(this.pos) === 10) ++this.pos // '\\r\\n'\n  case 10: // ' \\n'\n    if (this.options.locations) { this.lineStart = this.pos; ++this.curLine }\n    return \"\"\n  default:\n    if (ch >= 48 && ch <= 55) {\n      let octalStr = this.input.substr(this.pos - 1, 3).match(/^[0-7]+/)[0]\n      let octal = parseInt(octalStr, 8)\n      if (octal > 255) {\n        octalStr = octalStr.slice(0, -1)\n        octal = parseInt(octalStr, 8)\n      }\n      this.pos += octalStr.length - 1\n      ch = this.input.charCodeAt(this.pos)\n      if ((octalStr !== \"0\" || ch === 56 || ch === 57) && (this.strict || inTemplate)) {\n        this.invalidStringToken(\n          this.pos - 1 - octalStr.length,\n          inTemplate\n            ? \"Octal literal in template string\"\n            : \"Octal literal in strict mode\"\n        )\n      }\n      return String.fromCharCode(octal)\n    }\n    if (isNewLine(ch)) {\n      // Unicode new line characters after \\ get removed from output in both\n      // template literals and strings\n      return \"\"\n    }\n    return String.fromCharCode(ch)\n  }\n}\n\n// Used to read character escape sequences ('\\x', '\\u', '\\U').\n\npp.readHexChar = function(len) {\n  let codePos = this.pos\n  let n = this.readInt(16, len)\n  if (n === null) this.invalidStringToken(codePos, \"Bad character escape sequence\")\n  return n\n}\n\n// Read an identifier, and return it as a string. Sets `this.containsEsc`\n// to whether the word contained a '\\u' escape.\n//\n// Incrementally adds only escaped chars, adding other chunks as-is\n// as a micro-optimization.\n\npp.readWord1 = function() {\n  this.containsEsc = false\n  let word = \"\", first = true, chunkStart = this.pos\n  let astral = this.options.ecmaVersion >= 6\n  while (this.pos < this.input.length) {\n    let ch = this.fullCharCodeAtPos()\n    if (isIdentifierChar(ch, astral)) {\n      this.pos += ch <= 0xffff ? 1 : 2\n    } else if (ch === 92) { // \"\\\"\n      this.containsEsc = true\n      word += this.input.slice(chunkStart, this.pos)\n      let escStart = this.pos\n      if (this.input.charCodeAt(++this.pos) !== 117) // \"u\"\n        this.invalidStringToken(this.pos, \"Expecting Unicode escape sequence \\\\uXXXX\")\n      ++this.pos\n      let esc = this.readCodePoint()\n      if (!(first ? isIdentifierStart : isIdentifierChar)(esc, astral))\n        this.invalidStringToken(escStart, \"Invalid Unicode escape\")\n      word += codePointToString(esc)\n      chunkStart = this.pos\n    } else {\n      break\n    }\n    first = false\n  }\n  return word + this.input.slice(chunkStart, this.pos)\n}\n\n// Read an identifier or keyword token. Will check for reserved\n// words when necessary.\n\npp.readWord = function() {\n  let word = this.readWord1()\n  let type = tt.name\n  if (this.keywords.test(word)) {\n    if (this.containsEsc) this.raiseRecoverable(this.start, \"Escape sequence in keyword \" + word)\n    type = keywordTypes[word]\n  }\n  return this.finishToken(type, word)\n}\n", "// Acorn is a tiny, fast JavaScript parser written in JavaScript.\n//\n// Acorn was written by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and\n// various contributors and released under an MIT license.\n//\n// Git repositories for Acorn are available at\n//\n//     http://marijnhaverbeke.nl/git/acorn\n//     https://github.com/acornjs/acorn.git\n//\n// Please use the [github bug tracker][ghbt] to report issues.\n//\n// [ghbt]: https://github.com/acornjs/acorn/issues\n//\n// [walk]: util/walk.js\n\nimport {Parser} from \"./state\"\nimport \"./parseutil\"\nimport \"./statement\"\nimport \"./lval\"\nimport \"./expression\"\nimport \"./location\"\nimport \"./scope\"\n\nexport {Parser} from \"./state\"\nexport {defaultOptions} from \"./options\"\nexport {Position, SourceLocation, getLineInfo} from \"./locutil\"\nexport {Node} from \"./node\"\nexport {TokenType, types as tokTypes, keywords as keywordTypes} from \"./tokentype\"\nexport {TokContext, types as tokContexts} from \"./tokencontext\"\nexport {isIdentifierChar, isIdentifierStart} from \"./identifier\"\nexport {Token} from \"./tokenize\"\nexport {isNewLine, lineBreak, lineBreakG, nonASCIIwhitespace} from \"./whitespace\"\n\nexport const version = \"6.1.1\"\n\n// The main exported interface (under `self.acorn` when in the\n// browser) is a `parse` function that takes a code string and\n// returns an abstract syntax tree as specified by [Mozilla parser\n// API][api].\n//\n// [api]: https://developer.mozilla.org/en-US/docs/SpiderMonkey/Parser_API\n\nexport function parse(input, options) {\n  return Parser.parse(input, options)\n}\n\n// This function tries to parse a single expression at a given\n// offset in a string. Useful for parsing mixed-language formats\n// that embed JavaScript expressions.\n\nexport function parseExpressionAt(input, pos, options) {\n  return Parser.parseExpressionAt(input, pos, options)\n}\n\n// Acorn is organized as a tokenizer and a recursive-descent parser.\n// The `tokenizer` export provides an interface to the tokenizer.\n\nexport function tokenizer(input, options) {\n  return Parser.tokenizer(input, options)\n}\n"], "names": ["const", "let", "keywords", "tt", "this", "pp", "init", "label", "node", "empty", "scope", "types", "UNICODE_PROPERTY_VALUES", "codePointToString", "ch", "keywordTypes"], "mappings": ";;;;;;AAAA;;AAEA,AAAOA,IAAM,aAAa,GAAG;EAC3B,CAAC,EAAE,qNAAqN;EACxN,CAAC,EAAE,8CAA8C;EACjD,CAAC,EAAE,MAAM;EACT,MAAM,EAAE,wEAAwE;EAChF,UAAU,EAAE,gBAAgB;EAC7B;;;;AAIDA,IAAM,oBAAoB,GAAG,8KAA6K;;AAE1M,AAAOA,IAAM,QAAQ,GAAG;EACtB,CAAC,EAAE,oBAAoB;EACvB,CAAC,EAAE,oBAAoB,GAAG,0CAA0C;EACrE;;AAED,AAAOA,IAAM,yBAAyB,GAAG,kBAAiB;;;;;;;;;;AAU1DC,IAAI,4BAA4B,GAAG,4tIAA2tI;AAC9vIA,IAAI,uBAAuB,GAAG,sjFAAqjF;;AAEnlFD,IAAM,uBAAuB,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,4BAA4B,GAAG,GAAG,EAAC;AACpFA,IAAM,kBAAkB,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,4BAA4B,GAAG,uBAAuB,GAAG,GAAG,EAAC;;AAEzG,4BAA4B,GAAG,uBAAu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vqCA,IAAM,qBAAqB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,EAAC;;;;;AAKnlB,SAAS,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE;EAChCC,IAAI,GAAG,GAAG,QAAO;EACjB,KAAKA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;IACtC,GAAG,IAAI,GAAG,CAAC,CAAC,EAAC;IACb,IAAI,GAAG,GAAG,IAAI,EAAE,EAAA,OAAO,KAAK,EAAA;IAC5B,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,EAAC;IACjB,IAAI,GAAG,IAAI,IAAI,EAAE,EAAA,OAAO,IAAI,EAAA;GAC7B;CACF;;;;AAID,AAAO,SAAS,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE;EAC9C,IAAI,IAAI,GAAG,EAAE,EAAE,EAAA,OAAO,IAAI,KAAK,EAAE,EAAA;EACjC,IAAI,IAAI,GAAG,EAAE,EAAE,EAAA,OAAO,IAAI,EAAA;EAC1B,IAAI,IAAI,GAAG,EAAE,EAAE,EAAA,OAAO,IAAI,KAAK,EAAE,EAAA;EACjC,IAAI,IAAI,GAAG,GAAG,EAAE,EAAA,OAAO,IAAI,EAAA;EAC3B,IAAI,IAAI,IAAI,MAAM,EAAE,EAAA,OAAO,IAAI,IAAI,IAAI,IAAI,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAA;EAClG,IAAI,MAAM,KAAK,KAAK,EAAE,EAAA,OAAO,KAAK,EAAA;EAClC,OAAO,aAAa,CAAC,IAAI,EAAE,0BAA0B,CAAC;CACvD;;;;AAID,AAAO,SAAS,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE;EAC7C,IAAI,IAAI,GAAG,EAAE,EAAE,EAAA,OAAO,IAAI,KAAK,EAAE,EAAA;EACjC,IAAI,IAAI,GAAG,EAAE,EAAE,EAAA,OAAO,IAAI,EAAA;EAC1B,IAAI,IAAI,GAAG,EAAE,EAAE,EAAA,OAAO,KAAK,EAAA;EAC3B,IAAI,IAAI,GAAG,EAAE,EAAE,EAAA,OAAO,IAAI,EAAA;EAC1B,IAAI,IAAI,GAAG,EAAE,EAAE,EAAA,OAAO,IAAI,KAAK,EAAE,EAAA;EACjC,IAAI,IAAI,GAAG,GAAG,EAAE,EAAA,OAAO,IAAI,EAAA;EAC3B,IAAI,IAAI,IAAI,MAAM,EAAE,EAAA,OAAO,IAAI,IAAI,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAA;EAC7F,IAAI,MAAM,KAAK,KAAK,EAAE,EAAA,OAAO,KAAK,EAAA;EAClC,OAAO,aAAa,CAAC,IAAI,EAAE,0BAA0B,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,qBAAqB,CAAC;CACrG;;ACtFD;;;;;;;;;;;;;;;;;;;;;;;AAuBA,AAAO,IAAM,SAAS,GAAC,kBACV,CAAC,KAAK,EAAE,IAAS,EAAE;6BAAP,GAAG,EAAE;;EAC5B,IAAM,CAAC,KAAK,GAAG,MAAK;EACpB,IAAM,CAAC,OAAO,GAAG,IAAI,CAAC,QAAO;EAC7B,IAAM,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,WAAU;EACrC,IAAM,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,WAAU;EACrC,IAAM,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,OAAM;EAC7B,IAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,SAAQ;EACjC,IAAM,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,OAAM;EAC7B,IAAM,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,QAAO;EAC/B,IAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,KAAI;EACjC,IAAM,CAAC,aAAa,GAAG,KAAI;CAC1B,CAAA;;AAGH,SAAS,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE;EACzB,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;CAC5D;AACDD,IAAM,UAAU,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;IAAE,UAAU,GAAG,CAAC,UAAU,EAAE,IAAI,EAAC;;;;AAItE,AAAOA,IAAME,UAAQ,GAAG,GAAE;;;AAG1B,SAAS,EAAE,CAAC,IAAI,EAAE,OAAY,EAAE;mCAAP,GAAG,EAAE;;EAC5B,OAAO,CAAC,OAAO,GAAG,KAAI;EACtB,OAAOA,UAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC;CACrD;;AAED,AAAOF,IAAM,KAAK,GAAG;EACnB,GAAG,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC;EACrC,MAAM,EAAE,IAAI,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC;EAC3C,MAAM,EAAE,IAAI,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC;EAC3C,IAAI,EAAE,IAAI,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC;EACvC,GAAG,EAAE,IAAI,SAAS,CAAC,KAAK,CAAC;;;EAGzB,QAAQ,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EAClE,QAAQ,EAAE,IAAI,SAAS,CAAC,GAAG,CAAC;EAC5B,MAAM,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EAChE,MAAM,EAAE,IAAI,SAAS,CAAC,GAAG,CAAC;EAC1B,MAAM,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EAChE,MAAM,EAAE,IAAI,SAAS,CAAC,GAAG,CAAC;EAC1B,KAAK,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE,UAAU,CAAC;EACrC,IAAI,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE,UAAU,CAAC;EACpC,KAAK,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE,UAAU,CAAC;EACrC,GAAG,EAAE,IAAI,SAAS,CAAC,GAAG,CAAC;EACvB,QAAQ,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE,UAAU,CAAC;EACxC,KAAK,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC;EACtC,QAAQ,EAAE,IAAI,SAAS,CAAC,UAAU,CAAC;EACnC,eAAe,EAAE,IAAI,SAAS,CAAC,iBAAiB,CAAC;EACjD,QAAQ,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC;EAC1C,SAAS,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE,UAAU,CAAC;EACzC,YAAY,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBvE,EAAE,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;EAC1D,MAAM,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;EAC/D,MAAM,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EAC/E,MAAM,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EAChF,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;EACzB,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;EAC1B,SAAS,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;EACxB,UAAU,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;EACzB,UAAU,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;EACzB,QAAQ,EAAE,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC;EACnC,UAAU,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;EACjC,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;EAC/B,OAAO,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EAC3F,MAAM,EAAE,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC;EACtB,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC;EACpB,KAAK,EAAE,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC;EACrB,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;;;EAGjD,MAAM,EAAE,EAAE,CAAC,OAAO,CAAC;EACnB,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;EAC7B,MAAM,EAAE,EAAE,CAAC,OAAO,CAAC;EACnB,SAAS,EAAE,EAAE,CAAC,UAAU,CAAC;EACzB,SAAS,EAAE,EAAE,CAAC,UAAU,CAAC;EACzB,QAAQ,EAAE,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;EACnC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EAC/C,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;EAC7B,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC;EACvB,IAAI,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EAC/B,SAAS,EAAE,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EACrC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC;EACb,OAAO,EAAE,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EACjC,OAAO,EAAE,EAAE,CAAC,QAAQ,CAAC;EACrB,MAAM,EAAE,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;EAC/B,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC;EACf,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC;EACf,MAAM,EAAE,EAAE,CAAC,OAAO,CAAC;EACnB,MAAM,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EACnC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC;EACjB,IAAI,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EACrD,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;EAC7B,MAAM,EAAE,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;EAC/B,MAAM,EAAE,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;EAC/B,QAAQ,EAAE,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;EACnC,OAAO,EAAE,EAAE,CAAC,QAAQ,CAAC;EACrB,OAAO,EAAE,EAAE,CAAC,QAAQ,CAAC;EACrB,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;EAC7B,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;EAC7B,MAAM,EAAE,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;EAC/B,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;EAC3C,WAAW,EAAE,EAAE,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;EAC3D,OAAO,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EACzE,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EACrE,OAAO,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;CAC1E;;ACnJD;;;AAGA,AAAOA,IAAM,SAAS,GAAG,yBAAwB;AACjD,AAAOA,IAAM,UAAU,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,GAAG,EAAC;;AAE3D,AAAO,SAAS,SAAS,CAAC,IAAI,EAAE,cAAc,EAAE;EAC9C,OAAO,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,KAAK,CAAC,cAAc,KAAK,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC;CAC/F;;AAED,AAAOA,IAAM,kBAAkB,GAAG,gDAA+C;;AAEjF,AAAOA,IAAM,cAAc,GAAG,+BAA+B;;ACZxD,OAA2B,GAAG,MAAM,CAAC,SAAS;AAA5C,IAAA,cAAc;AAAE,IAAA,QAAQ,gBAAzB;;;;AAIN,AAAO,SAAS,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE;EACjC,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC;CAC1C;;AAED,AAAOA,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,KAAK,UAAC,GAAG,EAAE;EAC7C,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,gBAAgB;IACxC,EAAC;;AAEF,AAAO,SAAS,WAAW,CAAC,KAAK,EAAE;EACjC,OAAO,IAAI,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;CAC5D;;;;;ACTD,AAAO,IAAM,QAAQ,GAAC,iBACT,CAAC,IAAI,EAAE,GAAG,EAAE;EACvB,IAAM,CAAC,IAAI,GAAG,KAAI;EAClB,IAAM,CAAC,MAAM,GAAG,IAAG;CAClB,CAAA;;AAEH,mBAAE,MAAM,oBAAC,CAAC,EAAE;EACV,OAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;CAChD,CAAA;;AAGH,AAAO,IAAM,cAAc,GAAC,uBACf,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;EAC3B,IAAM,CAAC,KAAK,GAAG,MAAK;EACpB,IAAM,CAAC,GAAG,GAAG,IAAG;EAChB,IAAM,CAAC,CAAC,UAAU,KAAK,IAAI,EAAE,EAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,WAAU,EAAA;CACtD,CAAA;;;;;;;;AASH,AAAO,SAAS,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE;EACzC,KAAKC,IAAI,IAAI,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI;IAC5B,UAAU,CAAC,SAAS,GAAG,IAAG;IAC1BA,IAAI,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,EAAC;IAClC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM,EAAE;MACjC,EAAE,KAAI;MACN,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;KACpC,MAAM;MACL,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG,CAAC;KACxC;GACF;CACF;;;;;ACnCD,AAAOD,IAAM,cAAc,GAAG;;;;;;EAM5B,WAAW,EAAE,CAAC;;;;EAId,UAAU,EAAE,QAAQ;;;;;;EAMpB,mBAAmB,EAAE,IAAI;;;EAGzB,eAAe,EAAE,IAAI;;;;;EAKrB,aAAa,EAAE,IAAI;;;EAGnB,0BAA0B,EAAE,KAAK;;;EAGjC,2BAA2B,EAAE,KAAK;;;EAGlC,yBAAyB,EAAE,KAAK;;;EAGhC,aAAa,EAAE,KAAK;;;;;EAKpB,SAAS,EAAE,KAAK;;;;;;EAMhB,OAAO,EAAE,IAAI;;;;;;;;;;;EAWb,SAAS,EAAE,IAAI;;;;;;;;;EASf,MAAM,EAAE,KAAK;;;;;;EAMb,OAAO,EAAE,IAAI;;;EAGb,UAAU,EAAE,IAAI;;;EAGhB,gBAAgB,EAAE,IAAI;;;EAGtB,cAAc,EAAE,KAAK;EACtB;;;;AAID,AAAO,SAAS,UAAU,CAAC,IAAI,EAAE;EAC/BC,IAAI,OAAO,GAAG,GAAE;;EAEhB,KAAKA,IAAI,GAAG,IAAI,cAAc;IAC5B,EAAA,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,EAAC,EAAA;;EAEzE,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI;IAC7B,EAAA,OAAO,CAAC,WAAW,IAAI,KAAI,EAAA;;EAE7B,IAAI,OAAO,CAAC,aAAa,IAAI,IAAI;IAC/B,EAAA,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,WAAW,GAAG,EAAC,EAAA;;EAEjD,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;IAC5BA,IAAI,MAAM,GAAG,OAAO,CAAC,QAAO;IAC5B,OAAO,CAAC,OAAO,GAAG,UAAC,KAAK,EAAE,SAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAA;GAChD;EACD,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;IAC5B,EAAA,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,EAAC,EAAA;;EAE7D,OAAO,OAAO;CACf;;AAED,SAAS,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE;EACnC,OAAO,SAAS,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE;IACzDA,IAAI,OAAO,GAAG;MACZ,IAAI,EAAE,KAAK,GAAG,OAAO,GAAG,MAAM;MAC9B,KAAK,EAAE,IAAI;MACX,KAAK,EAAE,KAAK;MACZ,GAAG,EAAE,GAAG;MACT;IACD,IAAI,OAAO,CAAC,SAAS;MACnB,EAAA,OAAO,CAAC,GAAG,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAC,EAAA;IAC1D,IAAI,OAAO,CAAC,MAAM;MAChB,EAAA,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,EAAC,EAAA;IAC9B,KAAK,CAAC,IAAI,CAAC,OAAO,EAAC;GACpB;CACF;;ACjID;AACA,AAAOD,IACH,SAAS,GAAG,CAAC;IACb,cAAc,GAAG,CAAC;IAClB,SAAS,GAAG,SAAS,GAAG,cAAc;IACtC,WAAW,GAAG,CAAC;IACf,eAAe,GAAG,CAAC;IACnB,WAAW,GAAG,EAAE;IAChB,kBAAkB,GAAG,EAAE;IACvB,WAAW,GAAG,EAAE;IAChB,kBAAkB,GAAG,IAAG;;AAE5B,AAAO,SAAS,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE;EAC9C,OAAO,cAAc,IAAI,KAAK,GAAG,WAAW,GAAG,CAAC,CAAC,IAAI,SAAS,GAAG,eAAe,GAAG,CAAC,CAAC;CACtF;;;AAGD,AAAOA,IACH,SAAS,GAAG,CAAC;IACb,QAAQ,GAAG,CAAC;IACZ,YAAY,GAAG,CAAC;IAChB,aAAa,GAAG,CAAC;IACjB,iBAAiB,GAAG,CAAC;IACrB,YAAY,GAAG,EAAC;;AChBb,IAAM,MAAM,GAAC,eACP,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;EACtC,IAAM,CAAC,OAAO,GAAG,OAAO,GAAG,UAAU,CAAC,OAAO,EAAC;EAC9C,IAAM,CAAC,UAAU,GAAG,OAAO,CAAC,WAAU;EACtC,IAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAC;EACzE,IAAM,QAAQ,GAAG,GAAE;EACnB,IAAM,CAAC,OAAO,CAAC,aAAa,EAAE;IAC5B,KAAOC,IAAI,CAAC,GAAG,OAAO,CAAC,WAAW,GAAG,CAAC,EAAE;MACtC,EAAE,IAAI,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,EAAA,KAAK,IAAA;IAC1C,IAAM,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE,EAAA,QAAQ,IAAI,SAAQ,EAAA;GAC1D;EACH,IAAM,CAAC,aAAa,GAAG,WAAW,CAAC,QAAQ,EAAC;EAC5C,IAAM,cAAc,GAAG,CAAC,QAAQ,GAAG,QAAQ,GAAG,GAAG,GAAG,EAAE,IAAI,aAAa,CAAC,OAAM;EAC9E,IAAM,CAAC,mBAAmB,GAAG,WAAW,CAAC,cAAc,EAAC;EACxD,IAAM,CAAC,uBAAuB,GAAG,WAAW,CAAC,cAAc,GAAG,GAAG,GAAG,aAAa,CAAC,UAAU,EAAC;EAC7F,IAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAC;;;;;EAK5B,IAAM,CAAC,WAAW,GAAG,MAAK;;;;;EAK1B,IAAM,QAAQ,EAAE;IACd,IAAM,CAAC,GAAG,GAAG,SAAQ;IACrB,IAAM,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAC;IACjE,IAAM,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAM;GAC3E,MAAM;IACP,IAAM,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,EAAC;IAC/B,IAAM,CAAC,OAAO,GAAG,EAAC;GACjB;;;;EAIH,IAAM,CAAC,IAAI,GAAGE,KAAE,CAAC,IAAG;;EAEpB,IAAM,CAAC,KAAK,GAAG,KAAI;;EAEnB,IAAM,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAG;;;EAGlC,IAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,GAAE;;;EAGlD,IAAM,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,GAAG,KAAI;EAClD,IAAM,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAG;;;;;EAKhD,IAAM,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAE;EACtC,IAAM,CAAC,WAAW,GAAG,KAAI;;;EAGzB,IAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,KAAK,SAAQ;EACjD,IAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAC;;;EAG/D,IAAM,CAAC,gBAAgB,GAAG,CAAC,EAAC;;;EAG5B,IAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG,EAAC;;EAExD,IAAM,CAAC,MAAM,GAAG,GAAE;;EAElB,IAAM,CAAC,gBAAgB,GAAG,GAAE;;;EAG5B,IAAM,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI;IAC9E,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAC,EAAA;;;EAG3B,IAAM,CAAC,UAAU,GAAG,GAAE;EACtB,IAAM,CAAC,UAAU,CAAC,SAAS,EAAC;;;EAG5B,IAAM,CAAC,WAAW,GAAG,KAAI;CACxB;;4PAAA;;AAEH,iBAAE,KAAK,qBAAG;EACR,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,GAAE;EACrD,IAAM,CAAC,SAAS,GAAE;EAClB,OAAS,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;CAChC,CAAA;;AAEH,mBAAE,UAAc,mBAAG,EAAE,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,GAAG,cAAc,IAAI,CAAC,EAAE,CAAA;AACjF,mBAAE,WAAe,mBAAG,EAAE,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,GAAG,eAAe,IAAI,CAAC,EAAE,CAAA;AACnF,mBAAE,OAAW,mBAAG,EAAE,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,GAAG,WAAW,IAAI,CAAC,EAAE,CAAA;AAC3E,mBAAE,UAAc,mBAAG,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,KAAK,GAAG,WAAW,IAAI,CAAC,EAAE,CAAA;AAC/E,mBAAE,gBAAoB,mBAAG,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,KAAK,GAAG,kBAAkB,IAAI,CAAC,EAAE,CAAA;AAC5F,mBAAE,mBAAuB,mBAAG,EAAE,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,CAAA;;;AAG3F,iBAAE,kBAAkB,kCAAG,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,KAAK,GAAG,cAAc,IAAI,CAAC,EAAE,CAAA;;AAEtF,OAAE,MAAa,sBAAa;;;;EAC1B,IAAM,GAAG,GAAG,KAAI;EAChB,KAAOF,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAC,EAAA;EAChE,OAAS,GAAG;CACX,CAAA;;AAEH,OAAE,KAAY,mBAAC,KAAK,EAAE,OAAO,EAAE;EAC7B,OAAS,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,KAAK,EAAE;CACxC,CAAA;;AAEH,OAAE,iBAAwB,+BAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;EAC9C,IAAM,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAC;EAC5C,MAAQ,CAAC,SAAS,GAAE;EACpB,OAAS,MAAM,CAAC,eAAe,EAAE;CAChC,CAAA;;AAEH,OAAE,SAAgB,uBAAC,KAAK,EAAE,OAAO,EAAE;EACjC,OAAS,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;CAChC,CAAA;;gEACF;;ACvHDD,IAAM,EAAE,GAAG,MAAM,CAAC,UAAS;;;;AAI3BA,IAAM,OAAO,GAAG,6CAA4C;AAC5D,EAAE,CAAC,eAAe,GAAG,SAAS,KAAK,EAAE;;;EACnC,SAAS;;IAEP,cAAc,CAAC,SAAS,GAAG,MAAK;IAChC,KAAK,IAAI,cAAc,CAAC,IAAI,CAACI,MAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM;IAClDH,IAAI,KAAK,GAAG,OAAO,CAAC,IAAI,CAACG,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAC;IACjD,IAAI,CAAC,KAAK,EAAE,EAAA,OAAO,KAAK,EAAA;IACxB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,YAAY,EAAE,EAAA,OAAO,IAAI,EAAA;IACxD,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;;;IAGxB,cAAc,CAAC,SAAS,GAAG,MAAK;IAChC,KAAK,IAAI,cAAc,CAAC,IAAI,CAACA,MAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM;IAClD,IAAIA,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG;MAC3B,EAAA,KAAK,GAAE,EAAA;GACV;EACF;;;;;AAKD,EAAE,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE;EACtB,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;IACtB,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI;GACZ,MAAM;IACL,OAAO,KAAK;GACb;EACF;;;;AAID,EAAE,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE;EAC/B,OAAO,IAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;EACzE;;;;AAID,EAAE,CAAC,aAAa,GAAG,SAAS,IAAI,EAAE;EAChC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAA,OAAO,KAAK,EAAA;EAC1C,IAAI,CAAC,IAAI,GAAE;EACX,OAAO,IAAI;EACZ;;;;AAID,EAAE,CAAC,gBAAgB,GAAG,SAAS,IAAI,EAAE;EACnC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;EACjD;;;;AAID,EAAE,CAAC,kBAAkB,GAAG,WAAW;EACjC,OAAO,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,GAAG;IACzB,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM;IACvB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;EAChE;;AAED,EAAE,CAAC,eAAe,GAAG,WAAW;EAC9B,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;IAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB;MAClC,EAAA,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,EAAC,EAAA;IACvE,OAAO,IAAI;GACZ;EACF;;;;;AAKD,EAAE,CAAC,SAAS,GAAG,WAAW;EACxB,IAAI,CAAC,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;EACrE;;AAED,EAAE,CAAC,kBAAkB,GAAG,SAAS,OAAO,EAAE,OAAO,EAAE;EACjD,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;IACzB,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe;MAC9B,EAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,EAAC,EAAA;IACvE,IAAI,CAAC,OAAO;MACV,EAAA,IAAI,CAAC,IAAI,GAAE,EAAA;IACb,OAAO,IAAI;GACZ;EACF;;;;;AAKD,EAAE,CAAC,MAAM,GAAG,SAAS,IAAI,EAAE;EACzB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,GAAE;EACpC;;;;AAID,EAAE,CAAC,UAAU,GAAG,SAAS,GAAG,EAAE;EAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,kBAAkB,EAAC;EAC/D;;AAED,AAAO,SAAS,mBAAmB,GAAG;EACpC,IAAI,CAAC,eAAe;EACpB,IAAI,CAAC,aAAa;EAClB,IAAI,CAAC,mBAAmB;EACxB,IAAI,CAAC,iBAAiB;EACtB,IAAI,CAAC,WAAW;IACd,CAAC,EAAC;CACL;;AAED,EAAE,CAAC,kBAAkB,GAAG,SAAS,sBAAsB,EAAE,QAAQ,EAAE;EACjE,IAAI,CAAC,sBAAsB,EAAE,EAAA,MAAM,EAAA;EACnC,IAAI,sBAAsB,CAAC,aAAa,GAAG,CAAC,CAAC;IAC3C,EAAA,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,aAAa,EAAE,+CAA+C,EAAC,EAAA;EAC9GF,IAAI,MAAM,GAAG,QAAQ,GAAG,sBAAsB,CAAC,mBAAmB,GAAG,sBAAsB,CAAC,kBAAiB;EAC7G,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE,EAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,uBAAuB,EAAC,EAAA;EACxE;;AAED,EAAE,CAAC,qBAAqB,GAAG,SAAS,sBAAsB,EAAE,QAAQ,EAAE;EACpE,IAAI,CAAC,sBAAsB,EAAE,EAAA,OAAO,KAAK,EAAA;EACzC,IAAK,eAAe;EAAE,IAAA,WAAW,sCAA7B;EACJ,IAAI,CAAC,QAAQ,EAAE,EAAA,OAAO,eAAe,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAA;EAC9D,IAAI,eAAe,IAAI,CAAC;IACtB,EAAA,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,yEAAyE,EAAC,EAAA;EACxG,IAAI,WAAW,IAAI,CAAC;IAClB,EAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,oCAAoC,EAAC,EAAA;EAC3E;;AAED,EAAE,CAAC,8BAA8B,GAAG,WAAW;EAC7C,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IACpE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,4CAA4C,EAAC,EAAA;EACzE,IAAI,IAAI,CAAC,QAAQ;IACf,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,4CAA4C,EAAC,EAAA;EAC1E;;AAED,EAAE,CAAC,oBAAoB,GAAG,SAAS,IAAI,EAAE;EACvC,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB;IACzC,EAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAA;EACnD,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB;CACtE;;ACvIDD,IAAMK,IAAE,GAAG,MAAM,CAAC,UAAS;;;;;;;;;AAS3BA,IAAE,CAAC,aAAa,GAAG,SAAS,IAAI,EAAE;;;EAChCJ,IAAI,OAAO,GAAG,GAAE;EAChB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAA,IAAI,CAAC,IAAI,GAAG,GAAE,EAAA;EAC9B,OAAO,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,GAAG,EAAE;IAC3BF,IAAI,IAAI,GAAGG,MAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAC;IACnD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;GACrB;EACD,IAAI,IAAI,CAAC,QAAQ;IACf,EAAA,KAAa,kBAAI,MAAM,CAAC,IAAI,CAACA,MAAI,CAAC,gBAAgB,CAAC,yBAAA;MAA9C;QAAAH,IAAI,IAAI;;QACXG,MAAI,CAAC,gBAAgB,CAACA,MAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,GAAE,UAAS,GAAE,IAAI,qBAAiB,GAAE;OAAA,EAAA;EAC/F,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAC;EACtC,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAU;GAC1C;EACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC;EACxC;;AAEDJ,IAAM,SAAS,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC;IAAE,WAAW,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAC;;AAEhEK,IAAE,CAAC,KAAK,GAAG,SAAS,OAAO,EAAE;EAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,EAAA,OAAO,KAAK,EAAA;EAC3E,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,IAAG;EACnCJ,IAAI,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAC;EAC1CA,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAC;;;;;EAK1E,IAAI,MAAM,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,EAAA;EAC9B,IAAI,OAAO,EAAE,EAAA,OAAO,KAAK,EAAA;;EAEzB,IAAI,MAAM,KAAK,GAAG,EAAE,EAAA,OAAO,IAAI,EAAA;EAC/B,IAAI,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;IACnCA,IAAI,GAAG,GAAG,IAAI,GAAG,EAAC;IAClB,OAAO,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,EAAA,EAAE,IAAG,EAAA;IAChEA,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAC;IACvC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAA,OAAO,IAAI,EAAA;GACxD;EACD,OAAO,KAAK;EACb;;;;;AAKDI,IAAE,CAAC,eAAe,GAAG,WAAW;EAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;IAC7D,EAAA,OAAO,KAAK,EAAA;;EAEd,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,IAAG;EACnCJ,IAAI,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAC;EAC1CA,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM;EACpC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,KAAK,UAAU;KAC9C,IAAI,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;EACrF;;;;;;;;;AASDI,IAAE,CAAC,cAAc,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE;EACvDJ,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,KAAI;;EAExD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;IACvB,SAAS,GAAGE,KAAE,CAAC,KAAI;IACnB,IAAI,GAAG,MAAK;GACb;;;;;;EAMD,QAAQ,SAAS;EACjB,KAAKA,KAAE,CAAC,MAAM,CAAC,CAAC,KAAKA,KAAE,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC;EACnG,KAAKA,KAAE,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;EAC3D,KAAKA,KAAE,CAAC,GAAG,EAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;EAC/C,KAAKA,KAAE,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;EACjD,KAAKA,KAAE,CAAC,SAAS;;;;IAIf,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,MAAM,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IAC7H,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;EAC3D,KAAKA,KAAE,CAAC,MAAM;IACZ,IAAI,OAAO,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IAC9B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;EACpC,KAAKA,KAAE,CAAC,GAAG,EAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;EAC/C,KAAKA,KAAE,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;EACvD,KAAKA,KAAE,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;EACvD,KAAKA,KAAE,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;EACrD,KAAKA,KAAE,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;EACjD,KAAKA,KAAE,CAAC,MAAM,CAAC,CAAC,KAAKA,KAAE,CAAC,IAAI;IAC1B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAK;IACzB,IAAI,OAAO,IAAI,IAAI,KAAK,KAAK,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IAChD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;EAC3C,KAAKA,KAAE,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;EACrD,KAAKA,KAAE,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;EACnD,KAAKA,KAAE,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;EAClD,KAAKA,KAAE,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;EACnD,KAAKA,KAAE,CAAC,OAAO,CAAC;EAChB,KAAKA,KAAE,CAAC,OAAO;IACb,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE;MAC7C,IAAI,CAAC,QAAQ;QACX,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,wDAAwD,EAAC,EAAA;MAClF,IAAI,CAAC,IAAI,CAAC,QAAQ;QAChB,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,iEAAiE,EAAC,EAAA;KAC5F;IACD,OAAO,SAAS,KAAKA,KAAE,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC;;;;;;;EAO5F;IACE,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;MAC1B,IAAI,OAAO,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;MAC9B,IAAI,CAAC,IAAI,GAAE;MACX,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC;KACzD;;IAEDF,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI,CAAC,eAAe,GAAE;IACzD,IAAI,SAAS,KAAKE,KAAE,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,KAAK,CAAC;MAC3E,EAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,EAAA;SAC9D,EAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,EAAA;GACtD;EACF;;AAEDE,IAAE,CAAC,2BAA2B,GAAG,SAAS,IAAI,EAAE,OAAO,EAAE;;;EACvDJ,IAAI,OAAO,GAAG,OAAO,KAAK,QAAO;EACjC,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,EAAA,IAAI,CAAC,KAAK,GAAG,KAAI,EAAA;OAC7D,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,IAAI,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;OAC5C;IACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAE;IAC9B,IAAI,CAAC,SAAS,GAAE;GACjB;;;;EAIDF,IAAI,CAAC,GAAG,EAAC;EACT,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;IAClCA,IAAI,GAAG,GAAGG,MAAI,CAAC,MAAM,CAAC,CAAC,EAAC;IACxB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;MACtD,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,KAAK,OAAO,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,EAAA,KAAK,EAAA;MAC/D,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,EAAE,EAAA,KAAK,EAAA;KACjC;GACF;EACD,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,GAAG,OAAO,EAAC,EAAA;EAC9E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,GAAG,gBAAgB,GAAG,mBAAmB,CAAC;EAC/E;;AAEDC,IAAE,CAAC,sBAAsB,GAAG,SAAS,IAAI,EAAE;EACzC,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,SAAS,GAAE;EAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,mBAAmB,CAAC;EAClD;;AAEDA,IAAE,CAAC,gBAAgB,GAAG,SAAS,IAAI,EAAE;EACnC,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAC;EAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAC;EACrC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAE;EACjB,IAAI,CAAC,MAAM,CAACF,KAAE,CAAC,MAAM,EAAC;EACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,oBAAoB,GAAE;EACvC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;IAC/B,EAAA,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,IAAI,EAAC,EAAA;;IAEjB,EAAA,IAAI,CAAC,SAAS,GAAE,EAAA;EAClB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,kBAAkB,CAAC;EACjD;;;;;;;;;;AAUDE,IAAE,CAAC,iBAAiB,GAAG,SAAS,IAAI,EAAE;EACpC,IAAI,CAAC,IAAI,GAAE;EACXJ,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAC;EACvL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAC;EAC3B,IAAI,CAAC,UAAU,CAAC,CAAC,EAAC;EAClB,IAAI,CAAC,MAAM,CAACE,KAAE,CAAC,MAAM,EAAC;EACtB,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,IAAI,EAAE;IACzB,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAC,EAAA;IAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;GACjC;EACDF,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,GAAE;EACxB,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,IAAI,KAAK,EAAE;IAC7DF,IAAIK,MAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAK;IAC9D,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,QAAQ,CAACA,MAAI,EAAE,IAAI,EAAE,IAAI,EAAC;IAC/B,IAAI,CAAC,UAAU,CAACA,MAAI,EAAE,qBAAqB,EAAC;IAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,KAAKH,KAAE,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,KAAKG,MAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC;QACtH,EAAE,IAAI,KAAK,KAAK,IAAIA,MAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MAClD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;QACjC,IAAI,IAAI,CAAC,IAAI,KAAKH,KAAE,CAAC,GAAG,EAAE;UACxB,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAC,EAAA;SAC3C,MAAM,EAAA,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,CAAC,EAAC,EAAA;OACjC;MACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAEG,MAAI,CAAC;KACnC;IACD,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAC,EAAA;IAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAEA,MAAI,CAAC;GACjC;EACDL,IAAI,sBAAsB,GAAG,IAAI,oBAAmB;EACpDA,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAC;EAC7D,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE;IACtF,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;MACjC,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,GAAG,EAAE;QACxB,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAC,EAAA;OAC3C,MAAM,EAAA,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,CAAC,EAAC,EAAA;KACjC;IACD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,sBAAsB,EAAC;IACtD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAC;IACpB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;GACnC,MAAM;IACL,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,EAAE,IAAI,EAAC;GACzD;EACD,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAC,EAAA;EAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;EACjC;;AAEDE,IAAE,CAAC,sBAAsB,GAAG,SAAS,IAAI,EAAE,OAAO,EAAE,mBAAmB,EAAE;EACvE,IAAI,CAAC,IAAI,GAAE;EACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,cAAc,IAAI,mBAAmB,GAAG,CAAC,GAAG,sBAAsB,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC;EACrH;;AAEDA,IAAE,CAAC,gBAAgB,GAAG,SAAS,IAAI,EAAE;EACnC,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,oBAAoB,GAAE;;EAEvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAC;EAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAACF,KAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,KAAI;EACtE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC;EAC5C;;AAEDE,IAAE,CAAC,oBAAoB,GAAG,SAAS,IAAI,EAAE;EACvC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,0BAA0B;IAC9D,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,8BAA8B,EAAC,EAAA;EACxD,IAAI,CAAC,IAAI,GAAE;;;;;;EAMX,IAAI,IAAI,CAAC,GAAG,CAACF,KAAE,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,EAAA,IAAI,CAAC,QAAQ,GAAG,KAAI,EAAA;OAChE,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,GAAE,EAAE;EACjE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAChD;;AAEDE,IAAE,CAAC,oBAAoB,GAAG,SAAS,IAAI,EAAE;;;EACvC,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,GAAE;EAC/C,IAAI,CAAC,KAAK,GAAG,GAAE;EACf,IAAI,CAAC,MAAM,CAACF,KAAE,CAAC,MAAM,EAAC;EACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAC;EAC7B,IAAI,CAAC,UAAU,CAAC,CAAC,EAAC;;;;;;EAMlBF,IAAI,IAAG;EACP,KAAKA,IAAI,UAAU,GAAG,KAAK,EAAE,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,MAAM,GAAG;IACrD,IAAIC,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,KAAK,IAAIC,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,QAAQ,EAAE;MACvDF,IAAI,MAAM,GAAGG,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,MAAK;MACnC,IAAI,GAAG,EAAE,EAAAC,MAAI,CAAC,UAAU,CAAC,GAAG,EAAE,YAAY,EAAC,EAAA;MAC3C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAGA,MAAI,CAAC,SAAS,EAAE,EAAC;MACvC,GAAG,CAAC,UAAU,GAAG,GAAE;MACnBA,MAAI,CAAC,IAAI,GAAE;MACX,IAAI,MAAM,EAAE;QACV,GAAG,CAAC,IAAI,GAAGA,MAAI,CAAC,eAAe,GAAE;OAClC,MAAM;QACL,IAAI,UAAU,EAAE,EAAAA,MAAI,CAAC,gBAAgB,CAACA,MAAI,CAAC,YAAY,EAAE,0BAA0B,EAAC,EAAA;QACpF,UAAU,GAAG,KAAI;QACjB,GAAG,CAAC,IAAI,GAAG,KAAI;OAChB;MACDA,MAAI,CAAC,MAAM,CAACD,KAAE,CAAC,KAAK,EAAC;KACtB,MAAM;MACL,IAAI,CAAC,GAAG,EAAE,EAAAC,MAAI,CAAC,UAAU,GAAE,EAAA;MAC3B,GAAG,CAAC,UAAU,CAAC,IAAI,CAACA,MAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAC;KAC/C;GACF;EACD,IAAI,CAAC,SAAS,GAAE;EAChB,IAAI,GAAG,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,YAAY,EAAC,EAAA;EAC3C,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,MAAM,CAAC,GAAG,GAAE;EACjB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAChD;;AAEDC,IAAE,CAAC,mBAAmB,GAAG,SAAS,IAAI,EAAE;EACtC,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/D,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,6BAA6B,EAAC,EAAA;EAC5D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAE;EACtC,IAAI,CAAC,SAAS,GAAE;EAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC;EAC/C;;;;AAIDL,IAAM,KAAK,GAAG,GAAE;;AAEhBK,IAAE,CAAC,iBAAiB,GAAG,SAAS,IAAI,EAAE;EACpC,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAE;EAC9B,IAAI,CAAC,OAAO,GAAG,KAAI;EACnB,IAAI,IAAI,CAAC,IAAI,KAAKF,KAAE,CAAC,MAAM,EAAE;IAC3BF,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,GAAE;IAC7B,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,MAAM,CAAC,EAAE;MACvB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,GAAE;MACtCF,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,aAAY;MAC/C,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,kBAAkB,GAAG,CAAC,EAAC;MAChD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,iBAAiB,GAAG,YAAY,EAAC;MACvE,IAAI,CAAC,MAAM,CAACE,KAAE,CAAC,MAAM,EAAC;KACvB,MAAM;MACL,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,EAAE,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;MACpD,MAAM,CAAC,KAAK,GAAG,KAAI;MACnB,IAAI,CAAC,UAAU,CAAC,CAAC,EAAC;KACnB;IACD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAC;IACpC,IAAI,CAAC,SAAS,GAAE;IAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,EAAC;GACtD;EACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,KAAI;EACjE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;IAClC,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,iCAAiC,EAAC,EAAA;EAC3D,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC;EAC7C;;AAEDE,IAAE,CAAC,iBAAiB,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;EAC1C,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC;EAChC,IAAI,CAAC,SAAS,GAAE;EAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,qBAAqB,CAAC;EACpD;;AAEDA,IAAE,CAAC,mBAAmB,GAAG,SAAS,IAAI,EAAE;EACtC,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,oBAAoB,GAAE;EACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAC;EAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAC;EACxC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAE;EACjB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC;EAC/C;;AAEDA,IAAE,CAAC,kBAAkB,GAAG,SAAS,IAAI,EAAE;EACrC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,uBAAuB,EAAC,EAAA;EAChE,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,GAAE;EACzC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAC;EACvC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC;EAC9C;;AAEDA,IAAE,CAAC,mBAAmB,GAAG,SAAS,IAAI,EAAE;EACtC,IAAI,CAAC,IAAI,GAAE;EACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC;EAC/C;;AAEDA,IAAE,CAAC,qBAAqB,GAAG,SAAS,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE;;;EAClE,KAAc,oBAAID,MAAI,CAAC,MAAM,6BAAA;IAAxB;IAAAH,IAAI,KAAK;;IACZ,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS;MAC1B,EAAAG,MAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,GAAG,SAAS,GAAG,uBAAuB,EAAC;GAAA,EAAA;EAC3EH,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,OAAO,GAAG,QAAQ,GAAG,KAAI;EACjF,KAAKF,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;IAChDA,IAAIM,OAAK,GAAGH,MAAI,CAAC,MAAM,CAAC,CAAC,EAAC;IAC1B,IAAIG,OAAK,CAAC,cAAc,KAAK,IAAI,CAAC,KAAK,EAAE;;MAEvCA,OAAK,CAAC,cAAc,GAAGH,MAAI,CAAC,MAAK;MACjCG,OAAK,CAAC,IAAI,GAAG,KAAI;KAClB,MAAM,EAAA,KAAK,EAAA;GACb;EACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,MAAA,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,EAAC;EACrE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,EAAC;EAClH,IAAI,CAAC,MAAM,CAAC,GAAG,GAAE;EACjB,IAAI,CAAC,KAAK,GAAG,KAAI;EACjB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,kBAAkB,CAAC;EACjD;;AAEDF,IAAE,CAAC,wBAAwB,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;EACjD,IAAI,CAAC,UAAU,GAAG,KAAI;EACtB,IAAI,CAAC,SAAS,GAAE;EAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,qBAAqB,CAAC;EACpD;;;;;;AAMDA,IAAE,CAAC,UAAU,GAAG,SAAS,qBAA4B,EAAE,IAAuB,EAAE;oBAAlC;+DAAA,GAAG,IAAI,CAAM;6BAAA,GAAG,IAAI,CAAC,SAAS,EAAE;;EAC5E,IAAI,CAAC,IAAI,GAAG,GAAE;EACd,IAAI,CAAC,MAAM,CAACF,KAAE,CAAC,MAAM,EAAC;EACtB,IAAI,qBAAqB,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,CAAC,EAAC,EAAA;EAC7C,OAAO,CAAC,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,MAAM,CAAC,EAAE;IAC3BF,IAAI,IAAI,GAAGG,MAAI,CAAC,cAAc,CAAC,IAAI,EAAC;IACpC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;GACrB;EACD,IAAI,qBAAqB,EAAE,EAAA,IAAI,CAAC,SAAS,GAAE,EAAA;EAC3C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC;EAC/C;;;;;;AAMDC,IAAE,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;EACjC,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,IAAI,CAAC,MAAM,CAACF,KAAE,CAAC,IAAI,EAAC;EACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,eAAe,GAAE;EACjE,IAAI,CAAC,MAAM,CAACA,KAAE,CAAC,IAAI,EAAC;EACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,eAAe,GAAE;EACrE,IAAI,CAAC,MAAM,CAACA,KAAE,CAAC,MAAM,EAAC;EACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAC;EACtC,IAAI,CAAC,SAAS,GAAE;EAChB,IAAI,CAAC,MAAM,CAAC,GAAG,GAAE;EACjB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC;EAC7C;;;;;AAKDE,IAAE,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;EACnCJ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,GAAG,GAAG,gBAAgB,GAAG,iBAAgB;EACrE,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,IAAI,KAAK,gBAAgB,EAAE;IAC7B,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAmB;OAClC,IAAI,CAAC,IAAI,KAAK,qBAAqB,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI;QACvE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;MAChE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,wCAAwC,EAAC,EAAA;GACnE;EACD,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,gBAAgB,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,gBAAgB,GAAE;EACzF,IAAI,CAAC,MAAM,CAACA,KAAE,CAAC,MAAM,EAAC;EACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAC;EACtC,IAAI,CAAC,SAAS,GAAE;EAChB,IAAI,CAAC,MAAM,CAAC,GAAG,GAAE;EACjB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;EACnC;;;;AAIDE,IAAE,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;;;EACxC,IAAI,CAAC,YAAY,GAAG,GAAE;EACtB,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,SAAS;IACPJ,IAAI,IAAI,GAAGG,MAAI,CAAC,SAAS,GAAE;IAC3BA,MAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAC;IAC3B,IAAIA,MAAI,CAAC,GAAG,CAACD,KAAE,CAAC,EAAE,CAAC,EAAE;MACnB,IAAI,CAAC,IAAI,GAAGC,MAAI,CAAC,gBAAgB,CAAC,KAAK,EAAC;KACzC,MAAM,IAAI,IAAI,KAAK,OAAO,IAAI,EAAEA,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,GAAG,KAAKC,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAIA,MAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;MACpHA,MAAI,CAAC,UAAU,GAAE;KAClB,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,IAAI,EAAE,KAAK,KAAKA,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,GAAG,IAAIC,MAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;MACzGA,MAAI,CAAC,KAAK,CAACA,MAAI,CAAC,UAAU,EAAE,0DAA0D,EAAC;KACxF,MAAM;MACL,IAAI,CAAC,IAAI,GAAG,KAAI;KACjB;IACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAACA,MAAI,CAAC,UAAU,CAAC,IAAI,EAAE,oBAAoB,CAAC,EAAC;IACnE,IAAI,CAACA,MAAI,CAAC,GAAG,CAACD,KAAE,CAAC,KAAK,CAAC,EAAE,EAAA,KAAK,EAAA;GAC/B;EACD,OAAO,IAAI;EACZ;;AAEDE,IAAE,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;EACnC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;IACpE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,6CAA6C,EAAC;GACjF;EACD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,gBAAgB,GAAE;EACjC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,KAAK,GAAG,QAAQ,GAAG,YAAY,EAAE,KAAK,EAAC;EACzE;;AAEDL,IAAM,cAAc,GAAG,CAAC;IAAE,sBAAsB,GAAG,CAAC;IAAE,gBAAgB,GAAG,EAAC;;;;;;AAM1EK,IAAE,CAAC,aAAa,GAAG,SAAS,IAAI,EAAE,SAAS,EAAE,mBAAmB,EAAE,OAAO,EAAE;EACzE,IAAI,CAAC,YAAY,CAAC,IAAI,EAAC;EACvB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;IAC9E,IAAI,IAAI,CAAC,IAAI,KAAKF,KAAE,CAAC,IAAI,KAAK,SAAS,GAAG,sBAAsB,CAAC;MAC/D,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,IAAI,EAAC;GACnC;EACD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;IAC/B,EAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,QAAO,EAAA;;EAExB,IAAI,SAAS,GAAG,cAAc,EAAE;IAC9B,IAAI,CAAC,EAAE,GAAG,CAAC,SAAS,GAAG,gBAAgB,KAAK,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,GAAE;IAC5F,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,GAAG,sBAAsB,CAAC;;;;;MAKlD,EAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,mBAAmB,GAAG,QAAQ,GAAG,YAAY,GAAG,aAAa,EAAC,EAAA;GAC9I;;EAEDF,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,gBAAgB,GAAG,IAAI,CAAC,cAAa;EACnG,IAAI,CAAC,QAAQ,GAAG,EAAC;EACjB,IAAI,CAAC,QAAQ,GAAG,EAAC;EACjB,IAAI,CAAC,aAAa,GAAG,EAAC;EACtB,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,EAAC;;EAE1D,IAAI,EAAE,SAAS,GAAG,cAAc,CAAC;IAC/B,EAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,KAAI,EAAA;;EAE5D,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAC;EAC9B,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,mBAAmB,EAAE,KAAK,EAAC;;EAExD,IAAI,CAAC,QAAQ,GAAG,YAAW;EAC3B,IAAI,CAAC,QAAQ,GAAG,YAAW;EAC3B,IAAI,CAAC,aAAa,GAAG,iBAAgB;EACrC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,cAAc,IAAI,qBAAqB,GAAG,oBAAoB,CAAC;EAC1G;;AAEDE,IAAE,CAAC,mBAAmB,GAAG,SAAS,IAAI,EAAE;EACtC,IAAI,CAAC,MAAM,CAACF,KAAE,CAAC,MAAM,EAAC;EACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAACA,KAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAC;EACpF,IAAI,CAAC,8BAA8B,GAAE;EACtC;;;;;AAKDE,IAAE,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,WAAW,EAAE;;;EAC1C,IAAI,CAAC,IAAI,GAAE;;;;EAIXL,IAAM,SAAS,GAAG,IAAI,CAAC,OAAM;EAC7B,IAAI,CAAC,MAAM,GAAG,KAAI;;EAElB,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,EAAC;EACpC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAC;EAC1BC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,GAAE;EAChCA,IAAI,cAAc,GAAG,MAAK;EAC1B,SAAS,CAAC,IAAI,GAAG,GAAE;EACnB,IAAI,CAAC,MAAM,CAACE,KAAE,CAAC,MAAM,EAAC;EACtB,OAAO,CAAC,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,MAAM,CAAC,EAAE;IAC3BH,IAAM,OAAO,GAAGI,MAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,EAAC;IAChE,IAAI,OAAO,EAAE;MACX,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAC;MAC5B,IAAI,OAAO,CAAC,IAAI,KAAK,kBAAkB,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE;QACzE,IAAI,cAAc,EAAE,EAAAA,MAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,yCAAyC,EAAC,EAAA;QACxF,cAAc,GAAG,KAAI;OACtB;KACF;GACF;EACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAC;EACnD,IAAI,CAAC,MAAM,GAAG,UAAS;EACvB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,GAAG,kBAAkB,GAAG,iBAAiB,CAAC;EACnF;;AAEDC,IAAE,CAAC,iBAAiB,GAAG,SAAS,sBAAsB,EAAE;;;EACtD,IAAI,IAAI,CAAC,GAAG,CAACF,KAAE,CAAC,IAAI,CAAC,EAAE,EAAA,OAAO,IAAI,EAAA;;EAElCF,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,GAAE;EAC7BD,IAAM,aAAa,GAAG,UAAC,CAAC,EAAE,WAAmB,EAAE;6CAAV,GAAG,KAAK;;IAC3CA,IAAM,KAAK,GAAGI,MAAI,CAAC,KAAK,EAAE,QAAQ,GAAGA,MAAI,CAAC,SAAQ;IAClD,IAAI,CAACA,MAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAA,OAAO,KAAK,EAAA;IACxC,IAAIA,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI,CAACC,MAAI,CAAC,kBAAkB,EAAE,CAAC,EAAE,EAAA,OAAO,IAAI,EAAA;IACxF,IAAI,MAAM,CAAC,GAAG,EAAE,EAAAA,MAAI,CAAC,UAAU,GAAE,EAAA;IACjC,MAAM,CAAC,QAAQ,GAAG,MAAK;IACvB,MAAM,CAAC,GAAG,GAAGA,MAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,EAAC;IAC9C,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,EAAC;IACnBA,MAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,EAAC;IACzC,OAAO,KAAK;IACb;;EAED,MAAM,CAAC,IAAI,GAAG,SAAQ;EACtB,MAAM,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAC;EACvCH,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,IAAI,EAAC;EACnCF,IAAI,OAAO,GAAG,MAAK;EACnB,IAAI,CAAC,WAAW,EAAE;IAChB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;MACjE,OAAO,GAAG,KAAI;MACd,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,IAAI,EAAC;KACjE,MAAM,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;MAC/B,MAAM,CAAC,IAAI,GAAG,MAAK;KACpB,MAAM,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;MAC/B,MAAM,CAAC,IAAI,GAAG,MAAK;KACpB;GACF;EACD,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,EAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAC,EAAA;EAC/C,IAAK,GAAG,cAAJ;EACJF,IAAI,iBAAiB,GAAG,MAAK;EAC7B,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa;MAC9F,GAAG,CAAC,IAAI,KAAK,SAAS,IAAI,GAAG,CAAC,KAAK,KAAK,aAAa,CAAC,EAAE;IAC1D,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,yCAAyC,EAAC,EAAA;IAC9F,IAAI,WAAW,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,kCAAkC,EAAC,EAAA;IAC1E,IAAI,OAAO,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,sCAAsC,EAAC,EAAA;IAC1E,MAAM,CAAC,IAAI,GAAG,cAAa;IAC3B,iBAAiB,GAAG,uBAAsB;GAC3C,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE;IACjF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,wDAAwD,EAAC;GAChF;EACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAC;EACtE,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;IAC3D,EAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,8BAA8B,EAAC,EAAA;EAC3E,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;IAC3D,EAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,sCAAsC,EAAC,EAAA;EACnF,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,aAAa;IACxE,EAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,+BAA+B,EAAC,EAAA;EACtF,OAAO,MAAM;EACd;;AAEDI,IAAE,CAAC,gBAAgB,GAAG,SAAS,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE;EAC9E,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAC;EACxE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,kBAAkB,CAAC;EACnD;;AAEDA,IAAE,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE,WAAW,EAAE;EAC5C,IAAI,IAAI,CAAC,IAAI,KAAKF,KAAE,CAAC,IAAI,EAAE;IACzB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE;IAC3B,IAAI,WAAW;MACb,EAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,EAAE,KAAK,EAAC,EAAA;GAC/C,MAAM;IACL,IAAI,WAAW,KAAK,IAAI;MACtB,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IACnB,IAAI,CAAC,EAAE,GAAG,KAAI;GACf;EACF;;AAEDE,IAAE,CAAC,eAAe,GAAG,SAAS,IAAI,EAAE;EAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAACF,KAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,KAAI;EAC5E;;;;AAIDE,IAAE,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,OAAO,EAAE;;;EACvC,IAAI,CAAC,IAAI,GAAE;;EAEX,IAAI,IAAI,CAAC,GAAG,CAACF,KAAE,CAAC,IAAI,CAAC,EAAE;IACrB,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAC;IAC7B,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,GAAE;IAClC,IAAI,CAAC,SAAS,GAAE;IAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,sBAAsB,CAAC;GACrD;EACD,IAAI,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,QAAQ,CAAC,EAAE;IACzB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,EAAC;IACvDF,IAAI,QAAO;IACX,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;MACpEF,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,GAAE;MAC5B,IAAI,CAAC,IAAI,GAAE;MACX,IAAI,OAAO,EAAE,EAAA,IAAI,CAAC,IAAI,GAAE,EAAA;MACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,cAAc,GAAG,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAC;KAChG,MAAM,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,MAAM,EAAE;MAClCF,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,GAAE;MAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,YAAY,EAAC;KACxD,MAAM;MACL,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,GAAE;MAC1C,IAAI,CAAC,SAAS,GAAE;KACjB;IACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,0BAA0B,CAAC;GACzD;;EAED,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE;IACrC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAC;IAC5C,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,qBAAqB;MACjD,EAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,EAAC,EAAA;;MAEhE,EAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,EAAC,EAAA;IAChF,IAAI,CAAC,UAAU,GAAG,GAAE;IACpB,IAAI,CAAC,MAAM,GAAG,KAAI;GACnB,MAAM;IACL,IAAI,CAAC,WAAW,GAAG,KAAI;IACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAC;IACrD,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;MAC9B,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,MAAM,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;MAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,GAAE;KACnC,MAAM;MACL,KAAa,kBAAI,IAAI,CAAC,UAAU,yBAAA,EAAE;;QAA7BF,IAAI,IAAI;;QAEXG,MAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAC;;QAEhCA,MAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAC;OAClC;;MAED,IAAI,CAAC,MAAM,GAAG,KAAI;KACnB;IACD,IAAI,CAAC,SAAS,GAAE;GACjB;EACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACvD;;AAEDC,IAAE,CAAC,WAAW,GAAG,SAAS,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE;EAC5C,IAAI,CAAC,OAAO,EAAE,EAAA,MAAM,EAAA;EACpB,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;IACpB,EAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,oBAAoB,GAAG,IAAI,GAAG,GAAG,EAAC,EAAA;EAC/D,OAAO,CAAC,IAAI,CAAC,GAAG,KAAI;EACrB;;AAEDA,IAAE,CAAC,kBAAkB,GAAG,SAAS,OAAO,EAAE,GAAG,EAAE;;;EAC7CJ,IAAI,IAAI,GAAG,GAAG,CAAC,KAAI;EACnB,IAAI,IAAI,KAAK,YAAY;IACvB,EAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,EAAC,EAAA;OAC3C,IAAI,IAAI,KAAK,eAAe;IAC/B,EAAA,KAAa,kBAAI,GAAG,CAAC,UAAU,yBAAA;MAA1B;QAAAA,IAAI,IAAI;;QACXG,MAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAC;OAAA,EAAA;OACrC,IAAI,IAAI,KAAK,cAAc;IAC9B,EAAA,KAAY,sBAAI,GAAG,CAAC,QAAQ,+BAAA,EAAE;MAAzBH,IAAI,GAAG;;QACV,IAAI,GAAG,EAAE,EAAAG,MAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,EAAC,EAAA;KAC/C,EAAA;OACE,IAAI,IAAI,KAAK,UAAU;IAC1B,EAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,EAAC,EAAA;OACxC,IAAI,IAAI,KAAK,mBAAmB;IACnC,EAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,EAAC,EAAA;OACvC,IAAI,IAAI,KAAK,aAAa;IAC7B,EAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,QAAQ,EAAC,EAAA;OAC3C,IAAI,IAAI,KAAK,yBAAyB;IACzC,EAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,UAAU,EAAC,EAAA;EACnD;;AAEDC,IAAE,CAAC,mBAAmB,GAAG,SAAS,OAAO,EAAE,KAAK,EAAE;;;EAChD,IAAI,CAAC,OAAO,EAAE,EAAA,MAAM,EAAA;EACpB,KAAa,kBAAI,KAAK,yBAAA;IAAjB;IAAAJ,IAAI,IAAI;;IACXG,MAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAC;GAAA;EAC5C;;AAEDC,IAAE,CAAC,0BAA0B,GAAG,WAAW;EACzC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK;IAChC,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO;IAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO;IAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,UAAU;IAChC,IAAI,CAAC,KAAK,EAAE;IACZ,IAAI,CAAC,eAAe,EAAE;EACzB;;;;AAIDA,IAAE,CAAC,qBAAqB,GAAG,SAAS,OAAO,EAAE;;;EAC3CJ,IAAI,KAAK,GAAG,EAAE,EAAE,KAAK,GAAG,KAAI;;EAE5B,IAAI,CAAC,MAAM,CAACE,KAAE,CAAC,MAAM,EAAC;EACtB,OAAO,CAAC,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,MAAM,CAAC,EAAE;IAC3B,IAAI,CAAC,KAAK,EAAE;MACVC,MAAI,CAAC,MAAM,CAACD,KAAE,CAAC,KAAK,EAAC;MACrB,IAAIC,MAAI,CAAC,kBAAkB,CAACD,KAAE,CAAC,MAAM,CAAC,EAAE,EAAA,KAAK,EAAA;KAC9C,MAAM,EAAA,KAAK,GAAG,MAAK,EAAA;;IAEpBF,IAAI,IAAI,GAAGG,MAAI,CAAC,SAAS,GAAE;IAC3B,IAAI,CAAC,KAAK,GAAGA,MAAI,CAAC,UAAU,CAAC,IAAI,EAAC;IAClC,IAAI,CAAC,QAAQ,GAAGA,MAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAGA,MAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAK;IAC7EA,MAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAC;IAClE,KAAK,CAAC,IAAI,CAACA,MAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAC;GACrD;EACD,OAAO,KAAK;EACb;;;;AAIDC,IAAE,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE;EAC9B,IAAI,CAAC,IAAI,GAAE;;EAEX,IAAI,IAAI,CAAC,IAAI,KAAKF,KAAE,CAAC,MAAM,EAAE;IAC3B,IAAI,CAAC,UAAU,GAAG,MAAK;IACvB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,GAAE;GACnC,MAAM;IACL,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,GAAE;IAC9C,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAC;IAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE;GACjF;EACD,IAAI,CAAC,SAAS,GAAE;EAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,mBAAmB,CAAC;EAClD;;;;AAIDE,IAAE,CAAC,qBAAqB,GAAG,WAAW;;;EACpCJ,IAAI,KAAK,GAAG,EAAE,EAAE,KAAK,GAAG,KAAI;EAC5B,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,IAAI,EAAE;;IAEzBF,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAE;IAC9B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAC;IACxC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAAC;IAC3D,IAAI,CAAC,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,KAAK,CAAC,EAAE,EAAA,OAAO,KAAK,EAAA;GACtC;EACD,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,IAAI,EAAE;IACzBF,IAAIO,MAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IAC3B,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAC;IAC3BA,MAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAE;IAC9B,IAAI,CAAC,SAAS,CAACA,MAAI,CAAC,KAAK,EAAE,YAAY,EAAC;IACxC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAACA,MAAI,EAAE,0BAA0B,CAAC,EAAC;IAC7D,OAAO,KAAK;GACb;EACD,IAAI,CAAC,MAAM,CAACL,KAAE,CAAC,MAAM,EAAC;EACtB,OAAO,CAAC,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,MAAM,CAAC,EAAE;IAC3B,IAAI,CAAC,KAAK,EAAE;MACVC,MAAI,CAAC,MAAM,CAACD,KAAE,CAAC,KAAK,EAAC;MACrB,IAAIC,MAAI,CAAC,kBAAkB,CAACD,KAAE,CAAC,MAAM,CAAC,EAAE,EAAA,KAAK,EAAA;KAC9C,MAAM,EAAA,KAAK,GAAG,MAAK,EAAA;;IAEpBF,IAAIO,MAAI,GAAGJ,MAAI,CAAC,SAAS,GAAE;IAC3BI,MAAI,CAAC,QAAQ,GAAGJ,MAAI,CAAC,UAAU,CAAC,IAAI,EAAC;IACrC,IAAIA,MAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;MAC5BI,MAAI,CAAC,KAAK,GAAGJ,MAAI,CAAC,UAAU,GAAE;KAC/B,MAAM;MACLA,MAAI,CAAC,eAAe,CAACI,MAAI,CAAC,QAAQ,EAAC;MACnCA,MAAI,CAAC,KAAK,GAAGA,MAAI,CAAC,SAAQ;KAC3B;IACDJ,MAAI,CAAC,SAAS,CAACI,MAAI,CAAC,KAAK,EAAE,YAAY,EAAC;IACxC,KAAK,CAAC,IAAI,CAACJ,MAAI,CAAC,UAAU,CAACI,MAAI,EAAE,iBAAiB,CAAC,EAAC;GACrD;EACD,OAAO,KAAK;EACb;;;AAGDH,IAAE,CAAC,sBAAsB,GAAG,SAAS,UAAU,EAAE;EAC/C,KAAKJ,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IACtF,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC;GACpE;EACF;AACDI,IAAE,CAAC,oBAAoB,GAAG,SAAS,SAAS,EAAE;EAC5C;IACE,SAAS,CAAC,IAAI,KAAK,qBAAqB;IACxC,SAAS,CAAC,UAAU,CAAC,IAAI,KAAK,SAAS;IACvC,OAAO,SAAS,CAAC,UAAU,CAAC,KAAK,KAAK,QAAQ;;KAE7C,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;GAC9E;CACF;;AC30BDL,IAAMK,IAAE,GAAG,MAAM,CAAC,UAAS;;;;;AAK3BA,IAAE,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE,SAAS,EAAE,sBAAsB,EAAE;;;EAClE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,EAAE;IACzC,QAAQ,IAAI,CAAC,IAAI;IACjB,KAAK,YAAY;MACf,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO;QACvC,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,2DAA2D,EAAC,EAAA;MACrF,KAAK;;IAEP,KAAK,eAAe,CAAC;IACrB,KAAK,cAAc,CAAC;IACpB,KAAK,aAAa;MAChB,KAAK;;IAEP,KAAK,kBAAkB;MACrB,IAAI,CAAC,IAAI,GAAG,gBAAe;MAC3B,IAAI,sBAAsB,EAAE,EAAA,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,IAAI,EAAC,EAAA;MACjF,KAAa,kBAAI,IAAI,CAAC,UAAU,yBAAA,EAAE;QAA7BJ,IAAI,IAAI;;MACXG,MAAI,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,EAAC;;;;;;QAMlC;UACE,IAAI,CAAC,IAAI,KAAK,aAAa;WAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC;UACjF;UACAA,MAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,kBAAkB,EAAC;SACpD;OACF;MACD,KAAK;;IAEP,KAAK,UAAU;;MAEb,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,+CAA+C,EAAC,EAAA;MACrG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,EAAC;MACxC,KAAK;;IAEP,KAAK,iBAAiB;MACpB,IAAI,CAAC,IAAI,GAAG,eAAc;MAC1B,IAAI,sBAAsB,EAAE,EAAA,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,IAAI,EAAC,EAAA;MACjF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAC;MAC/C,KAAK;;IAEP,KAAK,eAAe;MAClB,IAAI,CAAC,IAAI,GAAG,cAAa;MACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAC;MAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mBAAmB;QAC5C,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,2CAA2C,EAAC,EAAA;MAC9E,KAAK;;IAEP,KAAK,sBAAsB;MACzB,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,6DAA6D,EAAC,EAAA;MACnH,IAAI,CAAC,IAAI,GAAG,oBAAmB;MAC/B,OAAO,IAAI,CAAC,SAAQ;MACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAC;;;IAGzC,KAAK,mBAAmB;MACtB,KAAK;;IAEP,KAAK,yBAAyB;MAC5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,sBAAsB,EAAC;MACrE,KAAK;;IAEP,KAAK,kBAAkB;MACrB,IAAI,CAAC,SAAS,EAAE,EAAA,KAAK,EAAA;;IAEvB;MACE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAqB,EAAC;KAC9C;GACF,MAAM,IAAI,sBAAsB,EAAE,EAAA,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,IAAI,EAAC,EAAA;EACxF,OAAO,IAAI;EACZ;;;;AAIDC,IAAE,CAAC,gBAAgB,GAAG,SAAS,QAAQ,EAAE,SAAS,EAAE;;;EAClDJ,IAAI,GAAG,GAAG,QAAQ,CAAC,OAAM;EACzB,KAAKA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC5BA,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAC;IACrB,IAAI,GAAG,EAAE,EAAAG,MAAI,CAAC,YAAY,CAAC,GAAG,EAAE,SAAS,EAAC,EAAA;GAC3C;EACD,IAAI,GAAG,EAAE;IACPH,IAAI,IAAI,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAC;IAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,CAAC,IAAI,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;MAC3H,EAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAC,EAAA;GACvC;EACD,OAAO,QAAQ;EAChB;;;;AAIDI,IAAE,CAAC,WAAW,GAAG,SAAS,sBAAsB,EAAE;EAChDJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,sBAAsB,EAAC;EACpE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC;EAC9C;;AAEDI,IAAE,CAAC,gBAAgB,GAAG,WAAW;EAC/BJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;;;EAGX,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,IAAI;IACzD,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;;EAEnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,GAAE;;EAEvC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC;EAC5C;;;;AAIDE,IAAE,CAAC,gBAAgB,GAAG,WAAW;EAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,QAAQ,IAAI,CAAC,IAAI;IACjB,KAAKF,KAAE,CAAC,QAAQ;MACdF,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;MAC3B,IAAI,CAAC,IAAI,GAAE;MACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAACE,KAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAC;MAC9D,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC;;IAE9C,KAAKA,KAAE,CAAC,MAAM;MACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;KAC3B;GACF;EACD,OAAO,IAAI,CAAC,UAAU,EAAE;EACzB;;AAEDE,IAAE,CAAC,gBAAgB,GAAG,SAAS,KAAK,EAAE,UAAU,EAAE,kBAAkB,EAAE;;;EACpEJ,IAAI,IAAI,GAAG,EAAE,EAAE,KAAK,GAAG,KAAI;EAC3B,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IACvB,IAAI,KAAK,EAAE,EAAA,KAAK,GAAG,MAAK,EAAA;SACnB,EAAAG,MAAI,CAAC,MAAM,CAACD,KAAE,CAAC,KAAK,EAAC,EAAA;IAC1B,IAAI,UAAU,IAAIC,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,KAAK,EAAE;MACxC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;KAChB,MAAM,IAAI,kBAAkB,IAAIC,MAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;MAC/D,KAAK;KACN,MAAM,IAAIA,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,QAAQ,EAAE;MACpCF,IAAI,IAAI,GAAGG,MAAI,CAAC,gBAAgB,GAAE;MAClCA,MAAI,CAAC,oBAAoB,CAAC,IAAI,EAAC;MAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;MACf,IAAIA,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,KAAK,EAAE,EAAAC,MAAI,CAAC,KAAK,CAACA,MAAI,CAAC,KAAK,EAAE,+CAA+C,EAAC,EAAA;MACnGA,MAAI,CAAC,MAAM,CAAC,KAAK,EAAC;MAClB,KAAK;KACN,MAAM;MACLH,IAAI,IAAI,GAAGG,MAAI,CAAC,iBAAiB,CAACA,MAAI,CAAC,KAAK,EAAEA,MAAI,CAAC,QAAQ,EAAC;MAC5DA,MAAI,CAAC,oBAAoB,CAAC,IAAI,EAAC;MAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;KAChB;GACF;EACD,OAAO,IAAI;EACZ;;AAEDC,IAAE,CAAC,oBAAoB,GAAG,SAAS,KAAK,EAAE;EACxC,OAAO,KAAK;EACb;;;;AAIDA,IAAE,CAAC,iBAAiB,GAAG,SAAS,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE;EACxD,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,gBAAgB,GAAE;EACtC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAACF,KAAE,CAAC,EAAE,CAAC,EAAE,EAAA,OAAO,IAAI,EAAA;EACjEF,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;EAC/C,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,GAAE;EACpC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,mBAAmB,CAAC;EAClD;;;;;;;;;AASDI,IAAE,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,WAAuB,EAAE,YAAY,EAAE;oBAA5B;2CAAA,GAAG,SAAS;;EACnD,QAAQ,IAAI,CAAC,IAAI;EACjB,KAAK,YAAY;IACf,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;MAC7D,EAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,WAAW,GAAG,UAAU,GAAG,eAAe,IAAI,IAAI,CAAC,IAAI,GAAG,iBAAiB,EAAC,EAAA;IACjH,IAAI,YAAY,EAAE;MAChB,IAAI,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC;QAC9B,EAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAqB,EAAC,EAAA;MAC1D,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAI;KAC/B;IACD,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,YAAY,EAAE,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,KAAK,EAAC,EAAA;IACnH,KAAK;;EAEP,KAAK,kBAAkB;IACrB,IAAI,WAAW,EAAE,EAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,2BAA2B,EAAC,EAAA;IAC/E,KAAK;;EAEP,KAAK,eAAe;IAClB,KAAa,kBAAI,IAAI,CAAC,UAAU,yBAAA;MAA3B;IAAAJ,IAAI,IAAI;;IACXG,MAAI,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAC;GAAA;IACjD,KAAK;;EAEP,KAAK,UAAU;;IAEb,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,YAAY,EAAC;IACrD,KAAK;;EAEP,KAAK,cAAc;IACjB,KAAa,sBAAI,IAAI,CAAC,QAAQ,+BAAA,EAAE;MAA3BH,IAAI,IAAI;;IACX,IAAI,IAAI,EAAE,EAAAG,MAAI,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAC,EAAA;KAC1D;IACD,KAAK;;EAEP,KAAK,mBAAmB;IACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAC;IACpD,KAAK;;EAEP,KAAK,aAAa;IAChB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAC;IACxD,KAAK;;EAEP,KAAK,yBAAyB;IAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,EAAC;IAC1D,KAAK;;EAEP;IACE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,WAAW,GAAG,SAAS,GAAG,cAAc,IAAI,SAAS,EAAC;GAC/E;CACF;;AC5OD;;;;;;;;;;;;;;;;;;AAkBA,AAMAJ,IAAMK,IAAE,GAAG,MAAM,CAAC,UAAS;;;;;;;AAO3BA,IAAE,CAAC,cAAc,GAAG,SAAS,IAAI,EAAE,QAAQ,EAAE,sBAAsB,EAAE;EACnE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;IAChE,EAAA,MAAM,EAAA;EACR,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC;IACnF,EAAA,MAAM,EAAA;EACR,IAAK,GAAG;EAAJ,IAAc,KAAI;EACtB,QAAQ,GAAG,CAAC,IAAI;EAChB,KAAK,YAAY,EAAE,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK;EACzC,KAAK,SAAS,EAAE,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK;EAC/C,SAAS,MAAM;GACd;EACD,IAAK,IAAI,aAAL;EACJ,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,MAAM,EAAE;MAC3C,IAAI,QAAQ,CAAC,KAAK,EAAE;QAClB,IAAI,sBAAsB,IAAI,sBAAsB,CAAC,WAAW,GAAG,CAAC,EAAE,EAAA,sBAAsB,CAAC,WAAW,GAAG,GAAG,CAAC,MAAK,EAAA;;aAE/G,EAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,oCAAoC,EAAC,EAAA;OAC5E;MACD,QAAQ,CAAC,KAAK,GAAG,KAAI;KACtB;IACD,MAAM;GACP;EACD,IAAI,GAAG,GAAG,GAAG,KAAI;EACjBJ,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAC;EAC1B,IAAI,KAAK,EAAE;IACTA,IAAI,aAAY;IAChB,IAAI,IAAI,KAAK,MAAM,EAAE;MACnB,YAAY,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,IAAG;KACnE,MAAM;MACL,YAAY,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,EAAC;KACzC;IACD,IAAI,YAAY;MACd,EAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,0BAA0B,EAAC,EAAA;GAC/D,MAAM;IACL,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG;MACvB,IAAI,EAAE,KAAK;MACX,GAAG,EAAE,KAAK;MACV,GAAG,EAAE,KAAK;MACX;GACF;EACD,KAAK,CAAC,IAAI,CAAC,GAAG,KAAI;EACnB;;;;;;;;;;;;;;;;;AAiBDI,IAAE,CAAC,eAAe,GAAG,SAAS,IAAI,EAAE,sBAAsB,EAAE;;;EAC1DJ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAQ;EACnDA,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,sBAAsB,EAAC;EAC9D,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,KAAK,EAAE;IAC1BF,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;IAC/C,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,EAAC;IACzB,OAAO,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,KAAK,CAAC,EAAE,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAACC,MAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,sBAAsB,CAAC,EAAC,EAAA;IACrG,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,oBAAoB,CAAC;GACnD;EACD,OAAO,IAAI;EACZ;;;;;AAKDC,IAAE,CAAC,gBAAgB,GAAG,SAAS,IAAI,EAAE,sBAAsB,EAAE,cAAc,EAAE;EAC3E,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;IAC9B,IAAI,IAAI,CAAC,WAAW,EAAE,EAAA,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAA;;;SAG7C,EAAA,IAAI,CAAC,WAAW,GAAG,MAAK,EAAA;GAC9B;;EAEDJ,IAAI,sBAAsB,GAAG,KAAK,EAAE,cAAc,GAAG,CAAC,CAAC,EAAE,gBAAgB,GAAG,CAAC,CAAC,EAAE,kBAAkB,GAAG,CAAC,EAAC;EACvG,IAAI,sBAAsB,EAAE;IAC1B,cAAc,GAAG,sBAAsB,CAAC,oBAAmB;IAC3D,gBAAgB,GAAG,sBAAsB,CAAC,cAAa;IACvD,kBAAkB,GAAG,sBAAsB,CAAC,gBAAe;IAC3D,sBAAsB,CAAC,mBAAmB,GAAG,sBAAsB,CAAC,aAAa,GAAG,sBAAsB,CAAC,eAAe,GAAG,CAAC,EAAC;GAChI,MAAM;IACL,sBAAsB,GAAG,IAAI,oBAAmB;IAChD,sBAAsB,GAAG,KAAI;GAC9B;;EAEDA,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAQ;EACnD,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,IAAI;IAClD,EAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAK,EAAA;EACpCF,IAAI,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,sBAAsB,EAAC;EACnE,IAAI,cAAc,EAAE,EAAA,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAC,EAAA;EAC9E,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;IACtBA,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;IAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAK;IAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,sBAAsB,CAAC,GAAG,KAAI;IAC/F,IAAI,CAAC,sBAAsB,EAAE,EAAA,mBAAmB,CAAC,IAAI,CAAC,sBAAsB,EAAC,EAAA;IAC7E,sBAAsB,CAAC,eAAe,GAAG,CAAC,EAAC;IAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,EAAC;IACpB,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAC;IACxC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,sBAAsB,CAAC;GACrD,MAAM;IACL,IAAI,sBAAsB,EAAE,EAAA,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,EAAE,IAAI,EAAC,EAAA;GACrF;EACD,IAAI,cAAc,GAAG,CAAC,CAAC,EAAE,EAAA,sBAAsB,CAAC,mBAAmB,GAAG,eAAc,EAAA;EACpF,IAAI,gBAAgB,GAAG,CAAC,CAAC,EAAE,EAAA,sBAAsB,CAAC,aAAa,GAAG,iBAAgB,EAAA;EAClF,IAAI,kBAAkB,GAAG,CAAC,CAAC,EAAE,EAAA,sBAAsB,CAAC,eAAe,GAAG,mBAAkB,EAAA;EACxF,OAAO,IAAI;EACZ;;;;AAIDE,IAAE,CAAC,qBAAqB,GAAG,SAAS,IAAI,EAAE,sBAAsB,EAAE;EAChEJ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAQ;EACnDA,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,sBAAsB,EAAC;EAC1D,IAAI,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,EAAE,EAAA,OAAO,IAAI,EAAA;EACnE,IAAI,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,QAAQ,CAAC,EAAE;IACzBF,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;IAC/C,IAAI,CAAC,IAAI,GAAG,KAAI;IAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,GAAE;IACzC,IAAI,CAAC,MAAM,CAACE,KAAE,CAAC,KAAK,EAAC;IACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAC;IAC5C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,uBAAuB,CAAC;GACtD;EACD,OAAO,IAAI;EACZ;;;;AAIDE,IAAE,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE,sBAAsB,EAAE;EACvDJ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAQ;EACnDA,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE,KAAK,EAAC;EAC9D,IAAI,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,EAAE,EAAA,OAAO,IAAI,EAAA;EACnE,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB,GAAG,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;EACxI;;;;;;;;AAQDI,IAAE,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE;EACzEJ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAK;EAC1B,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,GAAG,CAAC,EAAE;IACnD,IAAI,IAAI,GAAG,OAAO,EAAE;MAClBF,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,WAAU;MACvEF,IAAI,EAAE,GAAG,IAAI,CAAC,MAAK;MACnB,IAAI,CAAC,IAAI,GAAE;MACXA,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAQ;MACnDA,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAC;MAC/FA,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAC;MACjF,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC;KACzE;GACF;EACD,OAAO,IAAI;EACZ;;AAEDI,IAAE,CAAC,WAAW,GAAG,SAAS,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE;EACtEJ,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;EAC/C,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,IAAI,CAAC,QAAQ,GAAG,GAAE;EAClB,IAAI,CAAC,KAAK,GAAG,MAAK;EAClB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,GAAG,mBAAmB,GAAG,kBAAkB,CAAC;EACjF;;;;AAIDI,IAAE,CAAC,eAAe,GAAG,SAAS,sBAAsB,EAAE,QAAQ,EAAE;;;EAC9DJ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,KAAI;EACzD,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,EAAE;IAChH,IAAI,GAAG,IAAI,CAAC,UAAU,GAAE;IACxB,QAAQ,GAAG,KAAI;GAChB,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;IAC3BA,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,OAAM;IAC7D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAK;IAC1B,IAAI,CAAC,MAAM,GAAG,KAAI;IAClB,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAC;IAChD,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,EAAE,IAAI,EAAC;IACxD,IAAI,MAAM,EAAE,EAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAC,EAAA;SACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ;aACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;MAC1C,EAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,wCAAwC,EAAC,EAAA;SACxE,EAAA,QAAQ,GAAG,KAAI,EAAA;IACpB,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,kBAAkB,GAAG,iBAAiB,EAAC;GAC9E,MAAM;IACL,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,EAAC;IACvD,IAAI,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,EAAE,EAAA,OAAO,IAAI,EAAA;IACnE,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;MACtDF,IAAIO,MAAI,GAAGJ,MAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;MAC/CI,MAAI,CAAC,QAAQ,GAAGJ,MAAI,CAAC,MAAK;MAC1BI,MAAI,CAAC,MAAM,GAAG,MAAK;MACnBA,MAAI,CAAC,QAAQ,GAAG,KAAI;MACpBJ,MAAI,CAAC,SAAS,CAAC,IAAI,EAAC;MACpBA,MAAI,CAAC,IAAI,GAAE;MACX,IAAI,GAAGA,MAAI,CAAC,UAAU,CAACI,MAAI,EAAE,kBAAkB,EAAC;KACjD;GACF;;EAED,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAACL,KAAE,CAAC,QAAQ,CAAC;IACpC,EAAA,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,EAAA;;IAEjG,EAAA,OAAO,IAAI,EAAA;EACd;;;;AAIDE,IAAE,CAAC,mBAAmB,GAAG,SAAS,sBAAsB,EAAE;EACxDJ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAQ;EACnDA,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAC;EACrDA,IAAI,mBAAmB,GAAG,IAAI,CAAC,IAAI,KAAK,yBAAyB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,IAAG;EACjI,IAAI,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,IAAI,mBAAmB,EAAE,EAAA,OAAO,IAAI,EAAA;EAC1FA,IAAI,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAC;EAC3D,IAAI,sBAAsB,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;IAChE,IAAI,sBAAsB,CAAC,mBAAmB,IAAI,MAAM,CAAC,KAAK,EAAE,EAAA,sBAAsB,CAAC,mBAAmB,GAAG,CAAC,EAAC,EAAA;IAC/G,IAAI,sBAAsB,CAAC,iBAAiB,IAAI,MAAM,CAAC,KAAK,EAAE,EAAA,sBAAsB,CAAC,iBAAiB,GAAG,CAAC,EAAC,EAAA;GAC5G;EACD,OAAO,MAAM;EACd;;AAEDI,IAAE,CAAC,eAAe,GAAG,SAAS,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE;;;EAC/DJ,IAAI,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO;MACtG,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,QAAO;EACpH,OAAO,IAAI,EAAE;IACXA,IAAI,OAAO,GAAGG,MAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAC;IACrF,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,yBAAyB,EAAE,EAAA,OAAO,OAAO,EAAA;IAClF,IAAI,GAAG,QAAO;GACf;EACF;;AAEDC,IAAE,CAAC,cAAc,GAAG,SAAS,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE;EAC/EJ,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,QAAQ,EAAC;EACpC,IAAI,QAAQ,IAAI,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,GAAG,CAAC,EAAE;IAChCF,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;IAC/C,IAAI,CAAC,MAAM,GAAG,KAAI;IAClB,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAC;IACzE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,SAAQ;IAC1B,IAAI,QAAQ,EAAE,EAAA,IAAI,CAAC,MAAM,CAACE,KAAE,CAAC,QAAQ,EAAC,EAAA;IACtC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,kBAAkB,EAAC;GACjD,MAAM,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,MAAM,CAAC,EAAE;IAC1CF,IAAI,sBAAsB,GAAG,IAAI,mBAAmB,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,gBAAgB,GAAG,IAAI,CAAC,cAAa;IACrJ,IAAI,CAAC,QAAQ,GAAG,EAAC;IACjB,IAAI,CAAC,QAAQ,GAAG,EAAC;IACjB,IAAI,CAAC,aAAa,GAAG,EAAC;IACtBA,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,CAACE,KAAE,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAC;IAC1G,IAAI,eAAe,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,KAAK,CAAC,EAAE;MACvE,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,KAAK,EAAC;MACtD,IAAI,CAAC,8BAA8B,GAAE;MACrC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC;QACxB,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,2DAA2D,EAAC,EAAA;MAC7F,IAAI,CAAC,QAAQ,GAAG,YAAW;MAC3B,IAAI,CAAC,QAAQ,GAAG,YAAW;MAC3B,IAAI,CAAC,aAAa,GAAG,iBAAgB;MACrC,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;KACvF;IACD,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,EAAE,IAAI,EAAC;IACxD,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,IAAI,CAAC,SAAQ;IAC5C,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,IAAI,CAAC,SAAQ;IAC5C,IAAI,CAAC,aAAa,GAAG,gBAAgB,IAAI,IAAI,CAAC,cAAa;IAC3DF,IAAIO,MAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;IAC/CA,MAAI,CAAC,MAAM,GAAG,KAAI;IAClBA,MAAI,CAAC,SAAS,GAAG,SAAQ;IACzB,IAAI,GAAG,IAAI,CAAC,UAAU,CAACA,MAAI,EAAE,gBAAgB,EAAC;GAC/C,MAAM,IAAI,IAAI,CAAC,IAAI,KAAKL,KAAE,CAAC,SAAS,EAAE;IACrCF,IAAIO,MAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;IAC/CA,MAAI,CAAC,GAAG,GAAG,KAAI;IACfA,MAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAC;IACjD,IAAI,GAAG,IAAI,CAAC,UAAU,CAACA,MAAI,EAAE,0BAA0B,EAAC;GACzD;EACD,OAAO,IAAI;EACZ;;;;;;;AAODH,IAAE,CAAC,aAAa,GAAG,SAAS,sBAAsB,EAAE;;;EAGlD,IAAI,IAAI,CAAC,IAAI,KAAKF,KAAE,CAAC,KAAK,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;;EAE7CF,IAAI,IAAI,EAAE,UAAU,GAAG,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,MAAK;EAC3D,QAAQ,IAAI,CAAC,IAAI;EACjB,KAAKE,KAAE,CAAC,MAAM;IACZ,IAAI,CAAC,IAAI,CAAC,UAAU;MAClB,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,kCAAkC,EAAC,EAAA;IAC5D,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;MACnD,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,gDAAgD,EAAC,EAAA;;;;;;;IAO1E,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM;MAC9E,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IACnB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;;EAEvC,KAAKA,KAAE,CAAC,KAAK;IACX,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC;;EAEhD,KAAKA,KAAE,CAAC,IAAI;IACVF,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,GAAG,IAAI,CAAC,YAAW;IACnFA,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAC;IAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,SAAS,CAAC;MAC9H,EAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,EAAA;IACjF,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;MAC5C,IAAI,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,KAAK,CAAC;QACpB,EAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAA;MACrF,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;QACjG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAC;QAC3B,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,KAAK,CAAC;UAClD,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;QACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;OACnF;KACF;IACD,OAAO,EAAE;;EAEX,KAAKA,KAAE,CAAC,MAAM;IACZF,IAAI,KAAK,GAAG,IAAI,CAAC,MAAK;IACtB,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,EAAC;IACrC,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAC;IACzD,OAAO,IAAI;;EAEb,KAAKE,KAAE,CAAC,GAAG,CAAC,CAAC,KAAKA,KAAE,CAAC,MAAM;IACzB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;EAEtC,KAAKA,KAAE,CAAC,KAAK,CAAC,CAAC,KAAKA,KAAE,CAAC,KAAK,CAAC,CAAC,KAAKA,KAAE,CAAC,MAAM;IAC1C,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAK;IACnE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAO;IAC5B,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC;;EAEzC,KAAKA,KAAE,CAAC,MAAM;IACZF,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI,CAAC,kCAAkC,CAAC,UAAU,EAAC;IAClF,IAAI,sBAAsB,EAAE;MAC1B,IAAI,sBAAsB,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;QACpF,EAAA,sBAAsB,CAAC,mBAAmB,GAAG,MAAK,EAAA;MACpD,IAAI,sBAAsB,CAAC,iBAAiB,GAAG,CAAC;QAC9C,EAAA,sBAAsB,CAAC,iBAAiB,GAAG,MAAK,EAAA;KACnD;IACD,OAAO,IAAI;;EAEb,KAAKE,KAAE,CAAC,QAAQ;IACd,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAACA,KAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAC;IACnF,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;;EAEjD,KAAKA,KAAE,CAAC,MAAM;IACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,sBAAsB,CAAC;;EAErD,KAAKA,KAAE,CAAC,SAAS;IACf,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;;EAEpC,KAAKA,KAAE,CAAC,MAAM;IACZ,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC;;EAEjD,KAAKA,KAAE,CAAC,IAAI;IACV,OAAO,IAAI,CAAC,QAAQ,EAAE;;EAExB,KAAKA,KAAE,CAAC,SAAS;IACf,OAAO,IAAI,CAAC,aAAa,EAAE;;EAE7B;IACE,IAAI,CAAC,UAAU,GAAE;GAClB;EACF;;AAEDE,IAAE,CAAC,YAAY,GAAG,SAAS,KAAK,EAAE;EAChCJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,KAAK,GAAG,MAAK;EAClB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAC;EACjD,IAAI,CAAC,IAAI,GAAE;EACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC;EACxC;;AAEDI,IAAE,CAAC,oBAAoB,GAAG,WAAW;EACnC,IAAI,CAAC,MAAM,CAACF,KAAE,CAAC,MAAM,EAAC;EACtBF,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,GAAE;EAChC,IAAI,CAAC,MAAM,CAACE,KAAE,CAAC,MAAM,EAAC;EACtB,OAAO,GAAG;EACX;;AAEDE,IAAE,CAAC,kCAAkC,GAAG,SAAS,UAAU,EAAE;;;EAC3DJ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,EAAC;EAC5G,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,CAAC,IAAI,GAAE;;IAEXA,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,GAAG,IAAI,CAAC,SAAQ;IAC7DA,IAAI,QAAQ,GAAG,EAAE,EAAE,KAAK,GAAG,IAAI,EAAE,WAAW,GAAG,MAAK;IACpDA,IAAI,sBAAsB,GAAG,IAAI,mBAAmB,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,YAAW;IAC3H,IAAI,CAAC,QAAQ,GAAG,EAAC;IACjB,IAAI,CAAC,QAAQ,GAAG,EAAC;;IAEjB,OAAO,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,MAAM,EAAE;MAC9B,KAAK,GAAG,KAAK,GAAG,KAAK,GAAGC,MAAI,CAAC,MAAM,CAACD,KAAE,CAAC,KAAK,EAAC;MAC7C,IAAI,kBAAkB,IAAIC,MAAI,CAAC,kBAAkB,CAACD,KAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;QAClE,WAAW,GAAG,KAAI;QAClB,KAAK;OACN,MAAM,IAAIC,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,QAAQ,EAAE;QACpC,WAAW,GAAGC,MAAI,CAAC,MAAK;QACxB,QAAQ,CAAC,IAAI,CAACA,MAAI,CAAC,cAAc,CAACA,MAAI,CAAC,gBAAgB,EAAE,CAAC,EAAC;QAC3D,IAAIA,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,KAAK,EAAE,EAAAC,MAAI,CAAC,KAAK,CAACA,MAAI,CAAC,KAAK,EAAE,+CAA+C,EAAC,EAAA;QACnG,KAAK;OACN,MAAM;QACL,QAAQ,CAAC,IAAI,CAACA,MAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,sBAAsB,EAAEA,MAAI,CAAC,cAAc,CAAC,EAAC;OACzF;KACF;IACDH,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,CAAC,SAAQ;IACzD,IAAI,CAAC,MAAM,CAACE,KAAE,CAAC,MAAM,EAAC;;IAEtB,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,KAAK,EAAC;MACtD,IAAI,CAAC,8BAA8B,GAAE;MACrC,IAAI,CAAC,QAAQ,GAAG,YAAW;MAC3B,IAAI,CAAC,QAAQ,GAAG,YAAW;MAC3B,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;KAC9D;;IAED,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,WAAW,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAC,EAAA;IACvE,IAAI,WAAW,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,WAAW,EAAC,EAAA;IAC7C,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,EAAE,IAAI,EAAC;IACxD,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,IAAI,CAAC,SAAQ;IAC5C,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,IAAI,CAAC,SAAQ;;IAE5C,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;MACvB,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,aAAa,EAAC;MACpD,GAAG,CAAC,WAAW,GAAG,SAAQ;MAC1B,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,oBAAoB,EAAE,WAAW,EAAE,WAAW,EAAC;KACvE,MAAM;MACL,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAC;KAClB;GACF,MAAM;IACL,GAAG,GAAG,IAAI,CAAC,oBAAoB,GAAE;GAClC;;EAED,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;IAC/BF,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAC;IAC9C,GAAG,CAAC,UAAU,GAAG,IAAG;IACpB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,yBAAyB,CAAC;GACvD,MAAM;IACL,OAAO,GAAG;GACX;EACF;;AAEDI,IAAE,CAAC,cAAc,GAAG,SAAS,IAAI,EAAE;EACjC,OAAO,IAAI;EACZ;;AAEDA,IAAE,CAAC,mBAAmB,GAAG,SAAS,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;EAC9D,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC;EACjF;;;;;;;;AAQDL,IAAMS,OAAK,GAAG,GAAE;;AAEhBJ,IAAE,CAAC,QAAQ,GAAG,WAAW;EACvBJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3BA,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAC;EAChC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,GAAG,CAAC,EAAE;IACrD,IAAI,CAAC,IAAI,GAAG,KAAI;IAChBF,IAAI,WAAW,GAAG,IAAI,CAAC,YAAW;IAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAC;IACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,IAAI,WAAW;MAChD,EAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,oDAAoD,EAAC,EAAA;IAClG,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;MAC5B,EAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,0CAA0C,EAAC,EAAA;IAC/E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC;GAC7C;EACDA,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAQ;EACnD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAC;EAClF,IAAI,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,MAAM,CAAC,EAAE,EAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAACA,KAAE,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE,KAAK,EAAC,EAAA;OACxG,EAAA,IAAI,CAAC,SAAS,GAAGM,QAAK,EAAA;EAC3B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC;EAC9C;;;;AAIDJ,IAAE,CAAC,oBAAoB,GAAG,SAAS,GAAA,EAAY;MAAX,QAAQ;;EAC1CJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,eAAe,EAAE;IACpC,IAAI,CAAC,QAAQ,EAAE;MACb,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,kDAAkD,EAAC;KACtF;IACD,IAAI,CAAC,KAAK,GAAG;MACX,GAAG,EAAE,IAAI,CAAC,KAAK;MACf,MAAM,EAAE,IAAI;MACb;GACF,MAAM;IACL,IAAI,CAAC,KAAK,GAAG;MACX,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;MACnE,MAAM,EAAE,IAAI,CAAC,KAAK;MACnB;GACF;EACD,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,UAAS;EACtC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAChD;;AAEDE,IAAE,CAAC,aAAa,GAAG,SAAS,GAAA,EAAyB;oBAAP;2BAAA,GAAG,EAAE,CAAX;qEAAA,KAAK;;EAC3CJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,WAAW,GAAG,GAAE;EACrBA,IAAI,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,UAAA,QAAQ,CAAC,EAAC;EAClD,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,EAAC;EACtB,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;IACnB,IAAIG,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,GAAG,EAAE,EAAAC,MAAI,CAAC,KAAK,CAACA,MAAI,CAAC,GAAG,EAAE,+BAA+B,EAAC,EAAA;IAC/EA,MAAI,CAAC,MAAM,CAACD,KAAE,CAAC,YAAY,EAAC;IAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAACC,MAAI,CAAC,eAAe,EAAE,EAAC;IAC7CA,MAAI,CAAC,MAAM,CAACD,KAAE,CAAC,MAAM,EAAC;IACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAGC,MAAI,CAAC,oBAAoB,CAAC,CAAC,UAAA,QAAQ,CAAC,CAAC,EAAC;GACjE;EACD,IAAI,CAAC,IAAI,GAAE;EACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAChD;;AAEDC,IAAE,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE;EAC9B,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO;KACjF,IAAI,CAAC,IAAI,KAAKF,KAAE,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,IAAI,CAAC,CAAC;IACxL,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;EACjE;;;;AAIDE,IAAE,CAAC,QAAQ,GAAG,SAAS,SAAS,EAAE,sBAAsB,EAAE;;;EACxDJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,KAAK,GAAG,IAAI,EAAE,QAAQ,GAAG,GAAE;EACxD,IAAI,CAAC,UAAU,GAAG,GAAE;EACpB,IAAI,CAAC,IAAI,GAAE;EACX,OAAO,CAAC,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,MAAM,CAAC,EAAE;IAC3B,IAAI,CAAC,KAAK,EAAE;MACVC,MAAI,CAAC,MAAM,CAACD,KAAE,CAAC,KAAK,EAAC;MACrB,IAAIC,MAAI,CAAC,kBAAkB,CAACD,KAAE,CAAC,MAAM,CAAC,EAAE,EAAA,KAAK,EAAA;KAC9C,MAAM,EAAA,KAAK,GAAG,MAAK,EAAA;;IAEpBH,IAAM,IAAI,GAAGI,MAAI,CAAC,aAAa,CAAC,SAAS,EAAE,sBAAsB,EAAC;IAClE,IAAI,CAAC,SAAS,EAAE,EAAAA,MAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,sBAAsB,EAAC,EAAA;IAC3E,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAC;GAC3B;EACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,GAAG,eAAe,GAAG,kBAAkB,CAAC;EAC/E;;AAEDC,IAAE,CAAC,aAAa,GAAG,SAAS,SAAS,EAAE,sBAAsB,EAAE;EAC7DJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAQ;EACrE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,QAAQ,CAAC,EAAE;IAC1D,IAAI,SAAS,EAAE;MACb,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAC;MACtC,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,KAAK,EAAE;QAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,+CAA+C,EAAC;OACxE;MACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC;KAC5C;;IAED,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,IAAI,sBAAsB,EAAE;MACrD,IAAI,sBAAsB,CAAC,mBAAmB,GAAG,CAAC,EAAE;QAClD,sBAAsB,CAAC,mBAAmB,GAAG,IAAI,CAAC,MAAK;OACxD;MACD,IAAI,sBAAsB,CAAC,iBAAiB,GAAG,CAAC,EAAE;QAChD,sBAAsB,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAK;OACtD;KACF;;IAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,sBAAsB,EAAC;;IAEpE,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,KAAK,IAAI,sBAAsB,IAAI,sBAAsB,CAAC,aAAa,GAAG,CAAC,EAAE;MAChG,sBAAsB,CAAC,aAAa,GAAG,IAAI,CAAC,MAAK;KAClD;;IAED,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC;GAC9C;EACD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,CAAC,MAAM,GAAG,MAAK;IACnB,IAAI,CAAC,SAAS,GAAG,MAAK;IACtB,IAAI,SAAS,IAAI,sBAAsB,EAAE;MACvC,QAAQ,GAAG,IAAI,CAAC,MAAK;MACrB,QAAQ,GAAG,IAAI,CAAC,SAAQ;KACzB;IACD,IAAI,CAAC,SAAS;MACZ,EAAA,WAAW,GAAG,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,IAAI,EAAC,EAAA;GAClC;EACDF,IAAI,WAAW,GAAG,IAAI,CAAC,YAAW;EAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAC;EAC5B,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;IACzG,OAAO,GAAG,KAAI;IACd,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAACE,KAAE,CAAC,IAAI,EAAC;IAChE,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,sBAAsB,EAAC;GACrD,MAAM;IACL,OAAO,GAAG,MAAK;GAChB;EACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,sBAAsB,EAAE,WAAW,EAAC;EACvH,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC;EACzC;;AAEDE,IAAE,CAAC,kBAAkB,GAAG,SAAS,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,sBAAsB,EAAE,WAAW,EAAE;EAC/H,IAAI,CAAC,WAAW,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,KAAKF,KAAE,CAAC,KAAK;IACpD,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;;EAEnB,IAAI,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,KAAK,CAAC,EAAE;IACtB,IAAI,CAAC,KAAK,GAAG,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,sBAAsB,EAAC;IACjI,IAAI,CAAC,IAAI,GAAG,OAAM;GACnB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,EAAE;IACnE,IAAI,SAAS,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IAChC,IAAI,CAAC,IAAI,GAAG,OAAM;IAClB,IAAI,CAAC,MAAM,GAAG,KAAI;IAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,OAAO,EAAC;GACpD,MAAM,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW;aAC1B,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY;cAChF,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC;cACnD,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,CAAC,EAAE;IAC9D,IAAI,WAAW,IAAI,OAAO,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAI;IACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAC;IAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAC;IACpCF,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG,EAAC;IAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;MAC3CA,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAK;MAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK;QACrB,EAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,8BAA8B,EAAC,EAAA;;QAE5D,EAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,sCAAsC,EAAC,EAAA;KACvE,MAAM;MACL,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,aAAa;QACpE,EAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,+BAA+B,EAAC,EAAA;KACrF;GACF,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE;IAC5F,IAAI,WAAW,IAAI,OAAO,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IAC7C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAC;IAC9B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa;MAClD,EAAA,IAAI,CAAC,aAAa,GAAG,SAAQ,EAAA;IAC/B,IAAI,CAAC,IAAI,GAAG,OAAM;IAClB,IAAI,SAAS,EAAE;MACb,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAC;KAClE,MAAM,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,EAAE,IAAI,sBAAsB,EAAE;MACxD,IAAI,sBAAsB,CAAC,eAAe,GAAG,CAAC;QAC5C,EAAA,sBAAsB,CAAC,eAAe,GAAG,IAAI,CAAC,MAAK,EAAA;MACrD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAC;KAClE,MAAM;MACL,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAG;KACtB;IACD,IAAI,CAAC,SAAS,GAAG,KAAI;GACtB,MAAM,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;EACzB;;AAEDE,IAAE,CAAC,iBAAiB,GAAG,SAAS,IAAI,EAAE;EACpC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,GAAG,CAACF,KAAE,CAAC,QAAQ,CAAC,EAAE;MACzB,IAAI,CAAC,QAAQ,GAAG,KAAI;MACpB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,gBAAgB,GAAE;MAClC,IAAI,CAAC,MAAM,CAACA,KAAE,CAAC,QAAQ,EAAC;MACxB,OAAO,IAAI,CAAC,GAAG;KAChB,MAAM;MACL,IAAI,CAAC,QAAQ,GAAG,MAAK;KACtB;GACF;EACD,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;EACjH;;;;AAIDE,IAAE,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE;EAC/B,IAAI,CAAC,EAAE,GAAG,KAAI;EACd,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE,EAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,MAAK,EAAA;EAC3E,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE,EAAA,IAAI,CAAC,KAAK,GAAG,MAAK,EAAA;EACtD;;;;AAIDA,IAAE,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,OAAO,EAAE,gBAAgB,EAAE;EAChEJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,gBAAgB,GAAG,IAAI,CAAC,cAAa;;EAE5H,IAAI,CAAC,YAAY,CAAC,IAAI,EAAC;EACvB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;IAC/B,EAAA,IAAI,CAAC,SAAS,GAAG,YAAW,EAAA;EAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;IAC/B,EAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,QAAO,EAAA;;EAExB,IAAI,CAAC,QAAQ,GAAG,EAAC;EACjB,IAAI,CAAC,QAAQ,GAAG,EAAC;EACjB,IAAI,CAAC,aAAa,GAAG,EAAC;EACtB,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,WAAW,IAAI,gBAAgB,GAAG,kBAAkB,GAAG,CAAC,CAAC,EAAC;;EAEnH,IAAI,CAAC,MAAM,CAACE,KAAE,CAAC,MAAM,EAAC;EACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAACA,KAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAC;EACpF,IAAI,CAAC,8BAA8B,GAAE;EACrC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC;;EAEzC,IAAI,CAAC,QAAQ,GAAG,YAAW;EAC3B,IAAI,CAAC,QAAQ,GAAG,YAAW;EAC3B,IAAI,CAAC,aAAa,GAAG,iBAAgB;EACrC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,oBAAoB,CAAC;EACnD;;;;AAIDE,IAAE,CAAC,oBAAoB,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;EACxDJ,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,gBAAgB,GAAG,IAAI,CAAC,cAAa;;EAEnG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,WAAW,EAAC;EAC5D,IAAI,CAAC,YAAY,CAAC,IAAI,EAAC;EACvB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE,EAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,QAAO,EAAA;;EAEzD,IAAI,CAAC,QAAQ,GAAG,EAAC;EACjB,IAAI,CAAC,QAAQ,GAAG,EAAC;EACjB,IAAI,CAAC,aAAa,GAAG,EAAC;;EAEtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAC;EACjD,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC;;EAEzC,IAAI,CAAC,QAAQ,GAAG,YAAW;EAC3B,IAAI,CAAC,QAAQ,GAAG,YAAW;EAC3B,IAAI,CAAC,aAAa,GAAG,iBAAgB;EACrC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,yBAAyB,CAAC;EACxD;;;;AAIDI,IAAE,CAAC,iBAAiB,GAAG,SAAS,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE;EAC/DJ,IAAI,YAAY,GAAG,eAAe,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,OAAM;EAC7DF,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,GAAG,MAAK;;EAE9C,IAAI,YAAY,EAAE;IAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,GAAE;IACnC,IAAI,CAAC,UAAU,GAAG,KAAI;IACtB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAC;GAC9B,MAAM;IACLA,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAC;IACrF,IAAI,CAAC,SAAS,IAAI,SAAS,EAAE;MAC3B,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAC;;;;MAI1C,IAAI,SAAS,IAAI,SAAS;QACxB,EAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,2EAA2E,EAAC,EAAA;KACjH;;;IAGDA,IAAI,SAAS,GAAG,IAAI,CAAC,OAAM;IAC3B,IAAI,CAAC,MAAM,GAAG,GAAE;IAChB,IAAI,SAAS,EAAE,EAAA,IAAI,CAAC,MAAM,GAAG,KAAI,EAAA;;;;IAIjC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,eAAe,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAC;IACxH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAC;IAClC,IAAI,CAAC,UAAU,GAAG,MAAK;IACvB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;IAC3C,IAAI,CAAC,MAAM,GAAG,UAAS;GACxB;EACD,IAAI,CAAC,SAAS,GAAE;;;EAGhB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,EAAE,EAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,EAAC,EAAA;EACjE,IAAI,CAAC,MAAM,GAAG,UAAS;EACxB;;AAEDI,IAAE,CAAC,iBAAiB,GAAG,SAAS,MAAM,EAAE;EACtC,KAAc,kBAAI,MAAM,yBAAA;IAAnB;IAAAJ,IAAI,KAAK;;IACZ,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,EAAA,OAAO,KAAK;GAAA,EAAA;EAC/C,OAAO,IAAI;EACZ;;;;;AAKDI,IAAE,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,eAAe,EAAE;;;EAC/CJ,IAAI,QAAQ,GAAG,GAAE;EACjB,KAAc,kBAAI,IAAI,CAAC,MAAM,yBAAA;IAAxB;IAAAA,IAAI,KAAK;;IACZG,MAAI,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,eAAe,GAAG,IAAI,GAAG,QAAQ,EAAC;GAAA;EACrE;;;;;;;;AAQDC,IAAE,CAAC,aAAa,GAAG,SAAS,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,sBAAsB,EAAE;;;EACzFJ,IAAI,IAAI,GAAG,EAAE,EAAE,KAAK,GAAG,KAAI;EAC3B,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IACvB,IAAI,CAAC,KAAK,EAAE;MACVG,MAAI,CAAC,MAAM,CAACD,KAAE,CAAC,KAAK,EAAC;MACrB,IAAI,kBAAkB,IAAIC,MAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,EAAA,KAAK,EAAA;KAChE,MAAM,EAAA,KAAK,GAAG,MAAK,EAAA;;IAEpBH,IAAI,GAAG,YAAA;IACP,IAAI,UAAU,IAAIG,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,KAAK;MACtC,EAAA,GAAG,GAAG,KAAI,EAAA;SACP,IAAIC,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,QAAQ,EAAE;MAClC,GAAG,GAAGC,MAAI,CAAC,WAAW,CAAC,sBAAsB,EAAC;MAC9C,IAAI,sBAAsB,IAAIA,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,KAAK,IAAI,sBAAsB,CAAC,aAAa,GAAG,CAAC;QAC9F,EAAA,sBAAsB,CAAC,aAAa,GAAGC,MAAI,CAAC,MAAK,EAAA;KACpD,MAAM;MACL,GAAG,GAAGA,MAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,sBAAsB,EAAC;KAC3D;IACD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAC;GACf;EACD,OAAO,IAAI;EACZ;;AAEDC,IAAE,CAAC,eAAe,GAAG,SAAS,GAAA,EAAoB;MAAnB,KAAK,aAAE;MAAA,GAAG,WAAE;MAAA,IAAI;;EAC7C,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,KAAK,OAAO;IACtC,EAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,qDAAqD,EAAC,EAAA;EACrF,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,OAAO;IAClC,EAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,2DAA2D,EAAC,EAAA;EAC3F,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1B,EAAA,IAAI,CAAC,KAAK,CAAC,KAAK,GAAE,sBAAqB,GAAE,IAAI,MAAE,GAAE,EAAA;EACnD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC;IAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAA,MAAM,EAAA;EAC3DL,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAa;EACtE,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,OAAO;MACnC,EAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,sDAAsD,EAAC,EAAA;IACtF,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAE,eAAc,GAAE,IAAI,kBAAc,GAAE;GAClE;EACF;;;;;;AAMDK,IAAE,CAAC,UAAU,GAAG,SAAS,OAAO,EAAE,SAAS,EAAE;EAC3CJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,OAAO,EAAE,EAAA,OAAO,GAAG,MAAK,EAAA;EACtE,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,IAAI,EAAE;IACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAK;GACvB,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;IAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAO;;;;;;IAM7B,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU;SACjD,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE;MAClG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAE;KACnB;GACF,MAAM;IACL,IAAI,CAAC,UAAU,GAAE;GAClB;EACD,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,YAAY,EAAC;EACnC,IAAI,CAAC,OAAO,EAAE;IACZ,IAAI,CAAC,eAAe,CAAC,IAAI,EAAC;IAC1B,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa;MAC9C,EAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAK,EAAA;GAClC;EACD,OAAO,IAAI;EACZ;;;;AAIDE,IAAE,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE;EAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAK,EAAA;;EAE9CJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,IAAI,CAAC,IAAI,KAAKE,KAAE,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,IAAI,CAAC,IAAI,KAAKA,KAAE,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;IAC1G,IAAI,CAAC,QAAQ,GAAG,MAAK;IACrB,IAAI,CAAC,QAAQ,GAAG,KAAI;GACrB,MAAM;IACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAACA,KAAE,CAAC,IAAI,EAAC;IACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAC;GAC5C;EACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAChD;;AAEDE,IAAE,CAAC,UAAU,GAAG,WAAW;EACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAK,EAAA;;EAE9CJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAC;EAChD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;CAChD;;AC15BDD,IAAMK,IAAE,GAAG,MAAM,CAAC,UAAS;;;;;;;;AAQ3BA,IAAE,CAAC,KAAK,GAAG,SAAS,GAAG,EAAE,OAAO,EAAE;EAChCJ,IAAI,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAC;EACtC,OAAO,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,IAAG;EACnDA,IAAI,GAAG,GAAG,IAAI,WAAW,CAAC,OAAO,EAAC;EAClC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAG;EACrD,MAAM,GAAG;EACV;;AAEDI,IAAE,CAAC,gBAAgB,GAAGA,IAAE,CAAC,MAAK;;AAE9BA,IAAE,CAAC,WAAW,GAAG,WAAW;EAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;IAC1B,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;GAC7D;CACF;;ACtBDL,IAAMK,IAAE,GAAG,MAAM,CAAC,UAAS;;AAE3B,IAAM,KAAK,GAAC,cACC,CAAC,KAAK,EAAE;EACnB,IAAM,CAAC,KAAK,GAAG,MAAK;;EAEpB,IAAM,CAAC,GAAG,GAAG,GAAE;;EAEf,IAAM,CAAC,OAAO,GAAG,GAAE;;EAEnB,IAAM,CAAC,SAAS,GAAG,GAAE;CACpB,CAAA;;;;AAKHA,IAAE,CAAC,UAAU,GAAG,SAAS,KAAK,EAAE;EAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAC;EACvC;;AAEDA,IAAE,CAAC,SAAS,GAAG,WAAW;EACxB,IAAI,CAAC,UAAU,CAAC,GAAG,GAAE;EACtB;;;;;AAKDA,IAAE,CAAC,0BAA0B,GAAG,SAAS,KAAK,EAAE;EAC9C,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,cAAc,KAAK,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;EACrF;;AAEDA,IAAE,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE;;;EAChDJ,IAAI,UAAU,GAAG,MAAK;EACtB,IAAI,WAAW,KAAK,YAAY,EAAE;IAChCD,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,GAAE;IACjC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC;IACnH,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAC;IACxB,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;MAC5C,EAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAC,EAAA;GACrC,MAAM,IAAI,WAAW,KAAK,iBAAiB,EAAE;IAC5CA,IAAMU,OAAK,GAAG,IAAI,CAAC,YAAY,GAAE;IACjCA,OAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAC;GACzB,MAAM,IAAI,WAAW,KAAK,aAAa,EAAE;IACxCV,IAAMU,OAAK,GAAG,IAAI,CAAC,YAAY,GAAE;IACjC,IAAI,IAAI,CAAC,mBAAmB;MAC1B,EAAA,UAAU,GAAGA,OAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC,EAAA;;MAE7C,EAAA,UAAU,GAAGA,OAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAIA,OAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC,EAAA;IAC/EA,OAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAC;GAC3B,MAAM;IACL,KAAKT,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;MACpDD,IAAMU,OAAK,GAAGN,MAAI,CAAC,UAAU,CAAC,CAAC,EAAC;MAChC,IAAIM,OAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAACA,OAAK,CAAC,KAAK,GAAG,kBAAkB,KAAKA,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;UACtG,CAACN,MAAI,CAAC,0BAA0B,CAACM,OAAK,CAAC,IAAIA,OAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACjF,UAAU,GAAG,KAAI;QACjB,KAAK;OACN;MACDA,OAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAC;MACpB,IAAIN,MAAI,CAAC,QAAQ,KAAKM,OAAK,CAAC,KAAK,GAAG,SAAS,CAAC;QAC5C,EAAA,OAAON,MAAI,CAAC,gBAAgB,CAAC,IAAI,EAAC,EAAA;MACpC,IAAIM,OAAK,CAAC,KAAK,GAAG,SAAS,EAAE,EAAA,KAAK,EAAA;KACnC;GACF;EACD,IAAI,UAAU,EAAE,EAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,GAAE,cAAa,GAAE,IAAI,gCAA4B,GAAE,EAAA;EAC7F;;AAEDL,IAAE,CAAC,gBAAgB,GAAG,SAAS,EAAE,EAAE;;EAEjC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;MAClD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IAClD,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,GAAE;GACpC;EACF;;AAEDA,IAAE,CAAC,YAAY,GAAG,WAAW;EAC3B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;EACnD;;AAEDA,IAAE,CAAC,eAAe,GAAG,WAAW;;;EAC9B,KAAKJ,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE;IAC7CA,IAAI,KAAK,GAAGG,MAAI,CAAC,UAAU,CAAC,CAAC,EAAC;IAC9B,IAAI,KAAK,CAAC,KAAK,GAAG,SAAS,EAAE,EAAA,OAAO,KAAK,EAAA;GAC1C;EACF;;;AAGDC,IAAE,CAAC,gBAAgB,GAAG,WAAW;;;EAC/B,KAAKJ,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE;IAC7CA,IAAI,KAAK,GAAGG,MAAI,CAAC,UAAU,CAAC,CAAC,EAAC;IAC9B,IAAI,KAAK,CAAC,KAAK,GAAG,SAAS,IAAI,EAAE,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,EAAE,EAAA,OAAO,KAAK,EAAA;GAC1E;CACF;;AC3FM,IAAM,IAAI,GAAC,aACL,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EAC9B,IAAM,CAAC,IAAI,GAAG,GAAE;EAChB,IAAM,CAAC,KAAK,GAAG,IAAG;EAClB,IAAM,CAAC,GAAG,GAAG,EAAC;EACd,IAAM,MAAM,CAAC,OAAO,CAAC,SAAS;IAC5B,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,EAAC,EAAA;EAC9C,IAAM,MAAM,CAAC,OAAO,CAAC,gBAAgB;IACnC,EAAE,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAgB,EAAA;EACrD,IAAM,MAAM,CAAC,OAAO,CAAC,MAAM;IACzB,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC,EAAC,EAAA;CACxB,CAAA;;;;AAKHJ,IAAMK,IAAE,GAAG,MAAM,CAAC,UAAS;;AAE3BA,IAAE,CAAC,SAAS,GAAG,WAAW;EACxB,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC;EACjD;;AAEDA,IAAE,CAAC,WAAW,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE;EAClC,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;EAChC;;;;AAID,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAC1C,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,IAAI,CAAC,GAAG,GAAG,IAAG;EACd,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS;IACxB,EAAA,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAG,EAAA;EACpB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM;IACrB,EAAA,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAG,EAAA;EACrB,OAAO,IAAI;CACZ;;AAEDA,IAAE,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;EACnC,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC;EAChF;;;;AAIDA,IAAE,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAC/C,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;CACrD;;ACjDD;;;;AAIA,AAIO,IAAM,UAAU,GAAC,mBACX,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE;EAC/D,IAAM,CAAC,KAAK,GAAG,MAAK;EACpB,IAAM,CAAC,MAAM,GAAG,CAAC,CAAC,OAAM;EACxB,IAAM,CAAC,aAAa,GAAG,CAAC,CAAC,cAAa;EACtC,IAAM,CAAC,QAAQ,GAAG,SAAQ;EAC1B,IAAM,CAAC,SAAS,GAAG,CAAC,CAAC,UAAS;CAC7B,CAAA;;AAGH,AAAOL,IAAMW,OAAK,GAAG;EACnB,MAAM,EAAE,IAAI,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;EAClC,MAAM,EAAE,IAAI,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC;EACjC,MAAM,EAAE,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC;EACnC,MAAM,EAAE,IAAI,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;EAClC,MAAM,EAAE,IAAI,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC;EACjC,MAAM,EAAE,IAAI,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,UAAA,CAAC,EAAC,SAAG,CAAC,CAAC,oBAAoB,EAAE,GAAA,CAAC;EACtE,MAAM,EAAE,IAAI,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC;EACzC,MAAM,EAAE,IAAI,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;EACxC,UAAU,EAAE,IAAI,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EAC/D,KAAK,EAAE,IAAI,UAAU,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EAC5D;;AAEDX,IAAMK,IAAE,GAAG,MAAM,CAAC,UAAS;;AAE3BA,IAAE,CAAC,cAAc,GAAG,WAAW;EAC7B,OAAO,CAACM,OAAK,CAAC,MAAM,CAAC;EACtB;;AAEDN,IAAE,CAAC,YAAY,GAAG,SAAS,QAAQ,EAAE;EACnCJ,IAAI,MAAM,GAAG,IAAI,CAAC,UAAU,GAAE;EAC9B,IAAI,MAAM,KAAKU,OAAK,CAAC,MAAM,IAAI,MAAM,KAAKA,OAAK,CAAC,MAAM;IACpD,EAAA,OAAO,IAAI,EAAA;EACb,IAAI,QAAQ,KAAKR,KAAE,CAAC,KAAK,KAAK,MAAM,KAAKQ,OAAK,CAAC,MAAM,IAAI,MAAM,KAAKA,OAAK,CAAC,MAAM,CAAC;IAC/E,EAAA,OAAO,CAAC,MAAM,CAAC,MAAM,EAAA;;;;;EAKvB,IAAI,QAAQ,KAAKR,KAAE,CAAC,OAAO,IAAI,QAAQ,KAAKA,KAAE,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW;IACrE,EAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAA;EACtE,IAAI,QAAQ,KAAKA,KAAE,CAAC,KAAK,IAAI,QAAQ,KAAKA,KAAE,CAAC,IAAI,IAAI,QAAQ,KAAKA,KAAE,CAAC,GAAG,IAAI,QAAQ,KAAKA,KAAE,CAAC,MAAM,IAAI,QAAQ,KAAKA,KAAE,CAAC,KAAK;IACzH,EAAA,OAAO,IAAI,EAAA;EACb,IAAI,QAAQ,KAAKA,KAAE,CAAC,MAAM;IACxB,EAAA,OAAO,MAAM,KAAKQ,OAAK,CAAC,MAAM,EAAA;EAChC,IAAI,QAAQ,KAAKR,KAAE,CAAC,IAAI,IAAI,QAAQ,KAAKA,KAAE,CAAC,MAAM,IAAI,QAAQ,KAAKA,KAAE,CAAC,IAAI;IACxE,EAAA,OAAO,KAAK,EAAA;EACd,OAAO,CAAC,IAAI,CAAC,WAAW;EACzB;;AAEDE,IAAE,CAAC,kBAAkB,GAAG,WAAW;;;EACjC,KAAKJ,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;IACjDA,IAAI,OAAO,GAAGG,MAAI,CAAC,OAAO,CAAC,CAAC,EAAC;IAC7B,IAAI,OAAO,CAAC,KAAK,KAAK,UAAU;MAC9B,EAAA,OAAO,OAAO,CAAC,SAAS,EAAA;GAC3B;EACD,OAAO,KAAK;EACb;;AAEDC,IAAE,CAAC,aAAa,GAAG,SAAS,QAAQ,EAAE;EACpCJ,IAAI,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC,KAAI;EAC5B,IAAI,IAAI,CAAC,OAAO,IAAI,QAAQ,KAAKE,KAAE,CAAC,GAAG;IACrC,EAAA,IAAI,CAAC,WAAW,GAAG,MAAK,EAAA;OACrB,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa;IAClC,EAAA,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAC,EAAA;;IAE3B,EAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAU,EAAA;EACrC;;;;AAIDA,KAAE,CAAC,MAAM,CAAC,aAAa,GAAGA,KAAE,CAAC,MAAM,CAAC,aAAa,GAAG,WAAW;EAC7D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;IAC7B,IAAI,CAAC,WAAW,GAAG,KAAI;IACvB,MAAM;GACP;EACDF,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAE;EAC5B,IAAI,GAAG,KAAKU,OAAK,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,KAAK,UAAU,EAAE;IAClE,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAE;GACzB;EACD,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,OAAM;EAC/B;;AAEDR,KAAE,CAAC,MAAM,CAAC,aAAa,GAAG,SAAS,QAAQ,EAAE;EAC3C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAGQ,OAAK,CAAC,MAAM,GAAGA,OAAK,CAAC,MAAM,EAAC;EAC5E,IAAI,CAAC,WAAW,GAAG,KAAI;EACxB;;AAEDR,KAAE,CAAC,YAAY,CAAC,aAAa,GAAG,WAAW;EACzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACQ,OAAK,CAAC,MAAM,EAAC;EAC/B,IAAI,CAAC,WAAW,GAAG,KAAI;EACxB;;AAEDR,KAAE,CAAC,MAAM,CAAC,aAAa,GAAG,SAAS,QAAQ,EAAE;EAC3CF,IAAI,eAAe,GAAG,QAAQ,KAAKE,KAAE,CAAC,GAAG,IAAI,QAAQ,KAAKA,KAAE,CAAC,IAAI,IAAI,QAAQ,KAAKA,KAAE,CAAC,KAAK,IAAI,QAAQ,KAAKA,KAAE,CAAC,OAAM;EACpH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,GAAGQ,OAAK,CAAC,MAAM,GAAGA,OAAK,CAAC,MAAM,EAAC;EAChE,IAAI,CAAC,WAAW,GAAG,KAAI;EACxB;;AAEDR,KAAE,CAAC,MAAM,CAAC,aAAa,GAAG,WAAW;;EAEpC;;AAEDA,KAAE,CAAC,SAAS,CAAC,aAAa,GAAGA,KAAE,CAAC,MAAM,CAAC,aAAa,GAAG,SAAS,QAAQ,EAAE;EACxE,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,KAAKA,KAAE,CAAC,IAAI,IAAI,QAAQ,KAAKA,KAAE,CAAC,KAAK;MACpE,EAAE,QAAQ,KAAKA,KAAE,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;MAC3F,EAAE,CAAC,QAAQ,KAAKA,KAAE,CAAC,KAAK,IAAI,QAAQ,KAAKA,KAAE,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE,KAAKQ,OAAK,CAAC,MAAM,CAAC;IAC5F,EAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,OAAK,CAAC,MAAM,EAAC,EAAA;;IAE/B,EAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,OAAK,CAAC,MAAM,EAAC,EAAA;EACjC,IAAI,CAAC,WAAW,GAAG,MAAK;EACzB;;AAEDR,KAAE,CAAC,SAAS,CAAC,aAAa,GAAG,WAAW;EACtC,IAAI,IAAI,CAAC,UAAU,EAAE,KAAKQ,OAAK,CAAC,MAAM;IACpC,EAAA,IAAI,CAAC,OAAO,CAAC,GAAG,GAAE,EAAA;;IAElB,EAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,OAAK,CAAC,MAAM,EAAC,EAAA;EACjC,IAAI,CAAC,WAAW,GAAG,MAAK;EACzB;;AAEDR,KAAE,CAAC,IAAI,CAAC,aAAa,GAAG,SAAS,QAAQ,EAAE;EACzC,IAAI,QAAQ,KAAKA,KAAE,CAAC,SAAS,EAAE;IAC7BF,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAC;IACnC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAKU,OAAK,CAAC,MAAM;MACtC,EAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAGA,OAAK,CAAC,WAAU,EAAA;;MAEtC,EAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAGA,OAAK,CAAC,MAAK,EAAA;GACpC;EACD,IAAI,CAAC,WAAW,GAAG,KAAI;EACxB;;AAEDR,KAAE,CAAC,IAAI,CAAC,aAAa,GAAG,SAAS,QAAQ,EAAE;EACzCF,IAAI,OAAO,GAAG,MAAK;EACnB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,QAAQ,KAAKE,KAAE,CAAC,GAAG,EAAE;IACxD,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;QACxC,IAAI,CAAC,KAAK,KAAK,OAAO,IAAI,IAAI,CAAC,kBAAkB,EAAE;MACrD,EAAA,OAAO,GAAG,KAAI,EAAA;GACjB;EACD,IAAI,CAAC,WAAW,GAAG,QAAO;CAC3B;;;;;;;AC7IDH,IAAM,qBAAqB,GAAG,89BAA69B;AAC3/BA,IAAM,uBAAuB,GAAG;EAC9B,CAAC,EAAE,qBAAqB;EACxB,EAAE,EAAE,qBAAqB,GAAG,wBAAwB;EACrD;;;AAGDA,IAAM,4BAA4B,GAAG,qpBAAopB;;;AAGzrBA,IAAM,iBAAiB,GAAG,2+DAA0+D;AACpgEA,IAAM,mBAAmB,GAAG;EAC1B,CAAC,EAAE,iBAAiB;EACpB,EAAE,EAAE,iBAAiB,GAAG,iHAAiH;EAC1I;;AAEDA,IAAM,IAAI,GAAG,GAAE;AACf,SAAS,gBAAgB,CAAC,WAAW,EAAE;EACrCC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG;IAC1B,MAAM,EAAE,WAAW,CAAC,uBAAuB,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,4BAA4B,CAAC;IAC9F,SAAS,EAAE;MACT,gBAAgB,EAAE,WAAW,CAAC,4BAA4B,CAAC;MAC3D,MAAM,EAAE,WAAW,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;KACtD;IACF;EACD,CAAC,CAAC,SAAS,CAAC,iBAAiB,GAAG,CAAC,CAAC,SAAS,CAAC,OAAM;;EAElD,CAAC,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,iBAAgB;EAC7C,CAAC,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAM;EACnC,CAAC,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,CAAC,kBAAiB;CAChD;AACD,gBAAgB,CAAC,CAAC,EAAC;AACnB,gBAAgB,CAAC,EAAE,CAAC;;AClCpBD,IAAMK,IAAE,GAAG,MAAM,CAAC,UAAS;;AAE3B,AAAO,IAAM,qBAAqB,GAAC,8BACtB,CAAC,MAAM,EAAE;EACpB,IAAM,CAAC,MAAM,GAAG,OAAM;EACtB,IAAM,CAAC,UAAU,GAAG,KAAI,IAAE,MAAM,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,CAAA,IAAG,MAAM,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA;EAClH,IAAM,CAAC,iBAAiB,GAAGO,IAAuB,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,EAAC;EACtH,IAAM,CAAC,MAAM,GAAG,GAAE;EAClB,IAAM,CAAC,KAAK,GAAG,GAAE;EACjB,IAAM,CAAC,KAAK,GAAG,EAAC;EAChB,IAAM,CAAC,OAAO,GAAG,MAAK;EACtB,IAAM,CAAC,OAAO,GAAG,MAAK;EACtB,IAAM,CAAC,GAAG,GAAG,EAAC;EACd,IAAM,CAAC,YAAY,GAAG,EAAC;EACvB,IAAM,CAAC,eAAe,GAAG,GAAE;EAC3B,IAAM,CAAC,2BAA2B,GAAG,MAAK;EAC1C,IAAM,CAAC,kBAAkB,GAAG,EAAC;EAC7B,IAAM,CAAC,gBAAgB,GAAG,EAAC;EAC3B,IAAM,CAAC,UAAU,GAAG,GAAE;EACtB,IAAM,CAAC,kBAAkB,GAAG,GAAE;CAC7B,CAAA;;AAEH,gCAAE,KAAK,mBAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE;EAC7B,IAAQ,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAC;EAC3C,IAAM,CAAC,KAAK,GAAG,KAAK,GAAG,EAAC;EACxB,IAAM,CAAC,MAAM,GAAG,OAAO,GAAG,GAAE;EAC5B,IAAM,CAAC,KAAK,GAAG,MAAK;EACpB,IAAM,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,IAAI,EAAC;EAChE,IAAM,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,IAAI,EAAC;CAC/D,CAAA;;AAEH,gCAAE,KAAK,mBAAC,OAAO,EAAE;EACf,IAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,GAAE,+BAA8B,IAAE,IAAI,CAAC,MAAM,CAAA,QAAI,GAAE,OAAO,GAAG;CACrG,CAAA;;;;AAIH,gCAAE,EAAE,gBAAC,CAAC,EAAE;EACN,IAAQ,CAAC,GAAG,IAAI,CAAC,OAAM;EACvB,IAAQ,CAAC,GAAG,CAAC,CAAC,OAAM;EACpB,IAAM,CAAC,IAAI,CAAC,EAAE;IACZ,OAAS,CAAC,CAAC;GACV;EACH,IAAQ,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,EAAC;EAC3B,IAAM,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IAC/D,OAAS,CAAC;GACT;EACH,OAAS,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;CACnD,CAAA;;AAEH,gCAAE,SAAS,uBAAC,CAAC,EAAE;EACb,IAAQ,CAAC,GAAG,IAAI,CAAC,OAAM;EACvB,IAAQ,CAAC,GAAG,CAAC,CAAC,OAAM;EACpB,IAAM,CAAC,IAAI,CAAC,EAAE;IACZ,OAAS,CAAC;GACT;EACH,IAAQ,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,EAAC;EAC3B,IAAM,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IAC/D,OAAS,CAAC,GAAG,CAAC;GACb;EACH,OAAS,CAAC,GAAG,CAAC;CACb,CAAA;;AAEH,gCAAE,OAAO,uBAAG;EACV,OAAS,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;CACzB,CAAA;;AAEH,gCAAE,SAAS,yBAAG;EACZ,OAAS,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACzC,CAAA;;AAEH,gCAAE,OAAO,uBAAG;EACV,IAAM,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAC;CACpC,CAAA;;AAEH,gCAAE,GAAG,iBAAC,EAAE,EAAE;EACR,IAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;IAC3B,IAAM,CAAC,OAAO,GAAE;IAChB,OAAS,IAAI;GACZ;EACH,OAAS,KAAK;CACb,CAAA;;AAGH,SAASC,mBAAiB,CAAC,EAAE,EAAE;EAC7B,IAAI,EAAE,IAAI,MAAM,EAAE,EAAA,OAAO,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,EAAA;EAChD,EAAE,IAAI,QAAO;EACb,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,IAAI,MAAM,CAAC;CACxE;;;;;;;;AAQDR,IAAE,CAAC,mBAAmB,GAAG,SAAS,KAAK,EAAE;;;EACvCL,IAAM,UAAU,GAAG,KAAK,CAAC,WAAU;EACnCA,IAAM,KAAK,GAAG,KAAK,CAAC,MAAK;;EAEzB,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACrCD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAC;IAC5B,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MACnCI,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,iCAAiC,EAAC;KAC3D;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;MACnCA,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,mCAAmC,EAAC;KAC7D;GACF;EACF;;;;;;;;AAQDC,IAAE,CAAC,qBAAqB,GAAG,SAAS,KAAK,EAAE;EACzC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAC;;;;;;;EAO1B,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;IAClF,KAAK,CAAC,OAAO,GAAG,KAAI;IACpB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAC;GAC3B;EACF;;;AAGDA,IAAE,CAAC,cAAc,GAAG,SAAS,KAAK,EAAE;EAClC,KAAK,CAAC,GAAG,GAAG,EAAC;EACb,KAAK,CAAC,YAAY,GAAG,EAAC;EACtB,KAAK,CAAC,eAAe,GAAG,GAAE;EAC1B,KAAK,CAAC,2BAA2B,GAAG,MAAK;EACzC,KAAK,CAAC,kBAAkB,GAAG,EAAC;EAC5B,KAAK,CAAC,gBAAgB,GAAG,EAAC;EAC1B,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,EAAC;EAC3B,KAAK,CAAC,kBAAkB,CAAC,MAAM,GAAG,EAAC;;EAEnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAC;;EAE9B,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE;;IAErC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;MAC3B,KAAK,CAAC,KAAK,CAAC,eAAe,EAAC;KAC7B;IACD,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;MACtD,KAAK,CAAC,KAAK,CAAC,0BAA0B,EAAC;KACxC;GACF;EACD,IAAI,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,EAAE;IACrD,KAAK,CAAC,KAAK,CAAC,gBAAgB,EAAC;GAC9B;EACD,KAAe,kBAAI,KAAK,CAAC,kBAAkB,yBAAA,EAAE;IAAxCL,IAAM,IAAI;;IACb,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MACzC,KAAK,CAAC,KAAK,CAAC,kCAAkC,EAAC;KAChD;GACF;EACF;;;AAGDK,IAAE,CAAC,kBAAkB,GAAG,SAAS,KAAK,EAAE;;;EACtC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAC;EAC9B,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC9BD,MAAI,CAAC,kBAAkB,CAAC,KAAK,EAAC;GAC/B;;;EAGD,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;IAC1C,KAAK,CAAC,KAAK,CAAC,mBAAmB,EAAC;GACjC;EACD,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,KAAK,CAAC,KAAK,CAAC,0BAA0B,EAAC;GACxC;EACF;;;AAGDC,IAAE,CAAC,kBAAkB,GAAG,SAAS,KAAK,EAAE;EACtC,OAAO,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;IAClE,EAAA,AAAC,EAAA;EACJ;;;AAGDA,IAAE,CAAC,cAAc,GAAG,SAAS,KAAK,EAAE;EAClC,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;;;;IAInC,IAAI,KAAK,CAAC,2BAA2B,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;;MAEzE,IAAI,KAAK,CAAC,OAAO,EAAE;QACjB,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAC;OAClC;KACF;IACD,OAAO,IAAI;GACZ;;EAED,IAAI,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE;IACnF,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAC;IAChC,OAAO,IAAI;GACZ;;EAED,OAAO,KAAK;EACb;;;AAGDA,IAAE,CAAC,mBAAmB,GAAG,SAAS,KAAK,EAAE;EACvCL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvB,KAAK,CAAC,2BAA2B,GAAG,MAAK;;;EAGzC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IACtD,OAAO,IAAI;GACZ;;;EAGD,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;MACtD,OAAO,IAAI;KACZ;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;;;EAGD,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IACtDC,IAAI,UAAU,GAAG,MAAK;IACtB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;MACjC,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,UAAS;KACrC;IACD,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;MACtD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAC;MAC9B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;QAC5B,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAC;OAClC;MACD,KAAK,CAAC,2BAA2B,GAAG,CAAC,WAAU;MAC/C,OAAO,IAAI;KACZ;GACF;;EAED,KAAK,CAAC,GAAG,GAAG,MAAK;EACjB,OAAO,KAAK;EACb;;;AAGDI,IAAE,CAAC,oBAAoB,GAAG,SAAS,KAAK,EAAE,OAAe,EAAE;mCAAV,GAAG,KAAK;;EACvD,IAAI,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;IACnD,KAAK,CAAC,GAAG,CAAC,IAAI,UAAS;IACvB,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;;;AAGDA,IAAE,CAAC,0BAA0B,GAAG,SAAS,KAAK,EAAE,OAAO,EAAE;EACvD;IACE,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS;IACvB,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS;IACvB,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS;IACvB,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC;GAChD;EACF;AACDA,IAAE,CAAC,0BAA0B,GAAG,SAAS,KAAK,EAAE,OAAO,EAAE;EACvDL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3BC,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAC;IACrB,IAAI,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;MACvC,GAAG,GAAG,KAAK,CAAC,aAAY;MACxB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;QAClE,GAAG,GAAG,KAAK,CAAC,aAAY;OACzB;MACD,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;;QAE3B,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE;UACvC,KAAK,CAAC,KAAK,CAAC,uCAAuC,EAAC;SACrD;QACD,OAAO,IAAI;OACZ;KACF;IACD,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE;MAC7B,KAAK,CAAC,KAAK,CAAC,uBAAuB,EAAC;KACrC;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;EACD,OAAO,KAAK;EACb;;;AAGDI,IAAE,CAAC,cAAc,GAAG,SAAS,KAAK,EAAE;EAClC;IACE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC;IACvC,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS;IACvB,IAAI,CAAC,kCAAkC,CAAC,KAAK,CAAC;IAC9C,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;IACpC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC;IACtC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;GACrC;EACF;AACDA,IAAE,CAAC,kCAAkC,GAAG,SAAS,KAAK,EAAE;EACtDL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;MACpC,OAAO,IAAI;KACZ;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;EACD,OAAO,KAAK;EACb;AACDK,IAAE,CAAC,0BAA0B,GAAG,SAAS,KAAK,EAAE;EAC9CL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;MACtD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAC;MAC9B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;QAC3B,OAAO,IAAI;OACZ;MACD,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAC;KAClC;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;EACD,OAAO,KAAK;EACb;AACDK,IAAE,CAAC,wBAAwB,GAAG,SAAS,KAAK,EAAE;EAC5C,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;MACjC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAC;KAClC,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,IAAI,UAAU;MAC3C,KAAK,CAAC,KAAK,CAAC,eAAe,EAAC;KAC7B;IACD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAC;IAC9B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;MAC3B,KAAK,CAAC,kBAAkB,IAAI,EAAC;MAC7B,OAAO,IAAI;KACZ;IACD,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAC;GAClC;EACD,OAAO,KAAK;EACb;;;AAGDA,IAAE,CAAC,sBAAsB,GAAG,SAAS,KAAK,EAAE;EAC1C;IACE,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS;IACvB,IAAI,CAAC,kCAAkC,CAAC,KAAK,CAAC;IAC9C,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;IACpC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC;IACtC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;IACpC,IAAI,CAAC,iCAAiC,CAAC,KAAK,CAAC;IAC7C,IAAI,CAAC,kCAAkC,CAAC,KAAK,CAAC;GAC/C;EACF;;;AAGDA,IAAE,CAAC,iCAAiC,GAAG,SAAS,KAAK,EAAE;EACrD,IAAI,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;IAChD,KAAK,CAAC,KAAK,CAAC,mBAAmB,EAAC;GACjC;EACD,OAAO,KAAK;EACb;;;AAGDA,IAAE,CAAC,yBAAyB,GAAG,SAAS,KAAK,EAAE;EAC7CL,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EAC1B,IAAI,iBAAiB,CAAC,EAAE,CAAC,EAAE;IACzB,KAAK,CAAC,YAAY,GAAG,GAAE;IACvB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;AACD,SAAS,iBAAiB,CAAC,EAAE,EAAE;EAC7B;IACE,EAAE,KAAK,IAAI;IACX,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI;IAChC,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;IACX,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI;IAChC,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI;GACjC;CACF;;;;AAIDK,IAAE,CAAC,2BAA2B,GAAG,SAAS,KAAK,EAAE;EAC/CL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvBC,IAAI,EAAE,GAAG,EAAC;EACV,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE;IAC9D,KAAK,CAAC,OAAO,GAAE;GAChB;EACD,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK;EAC3B;;;AAGDI,IAAE,CAAC,kCAAkC,GAAG,SAAS,KAAK,EAAE;EACtDL,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EAC1B;IACE,EAAE,KAAK,CAAC,CAAC;IACT,EAAE,KAAK,IAAI;IACX,EAAE,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI,SAAS;IAC3C,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;IACX;IACA,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;;;;;AAKDK,IAAE,CAAC,qBAAqB,GAAG,SAAS,KAAK,EAAE;EACzC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;MACnC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;QAC1D,KAAK,CAAC,KAAK,CAAC,8BAA8B,EAAC;OAC5C;MACD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAC;MAC5C,MAAM;KACP;IACD,KAAK,CAAC,KAAK,CAAC,eAAe,EAAC;GAC7B;EACF;;;;;AAKDA,IAAE,CAAC,mBAAmB,GAAG,SAAS,KAAK,EAAE;EACvC,KAAK,CAAC,eAAe,GAAG,GAAE;EAC1B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;MACzE,OAAO,IAAI;KACZ;IACD,KAAK,CAAC,KAAK,CAAC,4BAA4B,EAAC;GAC1C;EACD,OAAO,KAAK;EACb;;;;;;AAMDA,IAAE,CAAC,8BAA8B,GAAG,SAAS,KAAK,EAAE;EAClD,KAAK,CAAC,eAAe,GAAG,GAAE;EAC1B,IAAI,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,EAAE;IAC/C,KAAK,CAAC,eAAe,IAAIQ,mBAAiB,CAAC,KAAK,CAAC,YAAY,EAAC;IAC9D,OAAO,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,EAAE;MACjD,KAAK,CAAC,eAAe,IAAIA,mBAAiB,CAAC,KAAK,CAAC,YAAY,EAAC;KAC/D;IACD,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;;;;;;;AAODR,IAAE,CAAC,+BAA+B,GAAG,SAAS,KAAK,EAAE;EACnDL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvBC,IAAI,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EACxB,KAAK,CAAC,OAAO,GAAE;;EAEf,IAAI,EAAE,KAAK,IAAI,YAAY,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,EAAE;IAC5E,EAAE,GAAG,KAAK,CAAC,aAAY;GACxB;EACD,IAAI,uBAAuB,CAAC,EAAE,CAAC,EAAE;IAC/B,KAAK,CAAC,YAAY,GAAG,GAAE;IACvB,OAAO,IAAI;GACZ;;EAED,KAAK,CAAC,GAAG,GAAG,MAAK;EACjB,OAAO,KAAK;EACb;AACD,SAAS,uBAAuB,CAAC,EAAE,EAAE;EACnC,OAAO,iBAAiB,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,YAAY,EAAE,KAAK,IAAI;CACzE;;;;;;;;;AASDI,IAAE,CAAC,8BAA8B,GAAG,SAAS,KAAK,EAAE;EAClDL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvBC,IAAI,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EACxB,KAAK,CAAC,OAAO,GAAE;;EAEf,IAAI,EAAE,KAAK,IAAI,YAAY,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,EAAE;IAC5E,EAAE,GAAG,KAAK,CAAC,aAAY;GACxB;EACD,IAAI,sBAAsB,CAAC,EAAE,CAAC,EAAE;IAC9B,KAAK,CAAC,YAAY,GAAG,GAAE;IACvB,OAAO,IAAI;GACZ;;EAED,KAAK,CAAC,GAAG,GAAG,MAAK;EACjB,OAAO,KAAK;EACb;AACD,SAAS,sBAAsB,CAAC,EAAE,EAAE;EAClC,OAAO,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,YAAY,EAAE,KAAK,IAAI,YAAY,EAAE,KAAK,MAAM,iBAAiB,EAAE,KAAK,MAAM;CAC/H;;;AAGDI,IAAE,CAAC,oBAAoB,GAAG,SAAS,KAAK,EAAE;EACxC;IACE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;IACnC,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC;IAC1C,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC;KACpC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACnD;IACA,OAAO,IAAI;GACZ;EACD,IAAI,KAAK,CAAC,OAAO,EAAE;;IAEjB,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,IAAI,UAAU;MACpC,KAAK,CAAC,KAAK,CAAC,wBAAwB,EAAC;KACtC;IACD,KAAK,CAAC,KAAK,CAAC,gBAAgB,EAAC;GAC9B;EACD,OAAO,KAAK;EACb;AACDA,IAAE,CAAC,uBAAuB,GAAG,SAAS,KAAK,EAAE;EAC3CL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvB,IAAI,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;IACvCA,IAAM,CAAC,GAAG,KAAK,CAAC,aAAY;IAC5B,IAAI,KAAK,CAAC,OAAO,EAAE;;MAEjB,IAAI,CAAC,GAAG,KAAK,CAAC,gBAAgB,EAAE;QAC9B,KAAK,CAAC,gBAAgB,GAAG,EAAC;OAC3B;MACD,OAAO,IAAI;KACZ;IACD,IAAI,CAAC,IAAI,KAAK,CAAC,kBAAkB,EAAE;MACjC,OAAO,IAAI;KACZ;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;EACD,OAAO,KAAK;EACb;AACDK,IAAE,CAAC,oBAAoB,GAAG,SAAS,KAAK,EAAE;EACxC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;MACnC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAC;MACpD,OAAO,IAAI;KACZ;IACD,KAAK,CAAC,KAAK,CAAC,yBAAyB,EAAC;GACvC;EACD,OAAO,KAAK;EACb;;;AAGDA,IAAE,CAAC,yBAAyB,GAAG,SAAS,KAAK,EAAE;EAC7C;IACE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;IACnC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;IACpC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;IAC1B,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC;IACvC,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC;KAChD,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC;IACnE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;GACrC;EACF;AACDA,IAAE,CAAC,wBAAwB,GAAG,SAAS,KAAK,EAAE;EAC5CL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;MACvC,OAAO,IAAI;KACZ;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;EACD,OAAO,KAAK;EACb;AACDK,IAAE,CAAC,cAAc,GAAG,SAAS,KAAK,EAAE;EAClC,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,IAAI,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE;IAC1E,KAAK,CAAC,YAAY,GAAG,EAAC;IACtB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;;;AAGDA,IAAE,CAAC,uBAAuB,GAAG,SAAS,KAAK,EAAE;EAC3CL,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EAC1B,IAAI,EAAE,KAAK,IAAI,UAAU;IACvB,KAAK,CAAC,YAAY,GAAG,KAAI;IACzB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,IAAI,EAAE,KAAK,IAAI,UAAU;IACvB,KAAK,CAAC,YAAY,GAAG,KAAI;IACzB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,IAAI,EAAE,KAAK,IAAI,UAAU;IACvB,KAAK,CAAC,YAAY,GAAG,KAAI;IACzB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,IAAI,EAAE,KAAK,IAAI,UAAU;IACvB,KAAK,CAAC,YAAY,GAAG,KAAI;IACzB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,IAAI,EAAE,KAAK,IAAI,UAAU;IACvB,KAAK,CAAC,YAAY,GAAG,KAAI;IACzB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;;;AAGDK,IAAE,CAAC,uBAAuB,GAAG,SAAS,KAAK,EAAE;EAC3CL,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EAC1B,IAAI,eAAe,CAAC,EAAE,CAAC,EAAE;IACvB,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,KAAI;IAC9B,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;AACD,SAAS,eAAe,CAAC,EAAE,EAAE;EAC3B;IACE,CAAC,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI;KAChC,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI,SAAS;GAC3C;CACF;;;AAGDK,IAAE,CAAC,qCAAqC,GAAG,SAAS,KAAK,EAAE;EACzDL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;;EAEvB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;MAC3CA,IAAM,IAAI,GAAG,KAAK,CAAC,aAAY;MAC/B,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,EAAE;QACrDA,IAAM,gBAAgB,GAAG,KAAK,CAAC,IAAG;QAClC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;UACjGA,IAAM,KAAK,GAAG,KAAK,CAAC,aAAY;UAChC,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,EAAE;YACtC,KAAK,CAAC,YAAY,GAAG,CAAC,IAAI,GAAG,MAAM,IAAI,KAAK,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,QAAO;YACzE,OAAO,IAAI;WACZ;SACF;QACD,KAAK,CAAC,GAAG,GAAG,iBAAgB;QAC5B,KAAK,CAAC,YAAY,GAAG,KAAI;OAC1B;MACD,OAAO,IAAI;KACZ;IACD;MACE,KAAK,CAAC,OAAO;MACb,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS;MACvB,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;MAC/B,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS;MACvB,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC;MAClC;MACA,OAAO,IAAI;KACZ;IACD,IAAI,KAAK,CAAC,OAAO,EAAE;MACjB,KAAK,CAAC,KAAK,CAAC,wBAAwB,EAAC;KACtC;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;;EAED,OAAO,KAAK;EACb;AACD,SAAS,cAAc,CAAC,EAAE,EAAE;EAC1B,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,QAAQ;CACjC;;;AAGDK,IAAE,CAAC,wBAAwB,GAAG,SAAS,KAAK,EAAE;EAC5C,IAAI,KAAK,CAAC,OAAO,EAAE;IACjB,IAAI,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE;MACzC,OAAO,IAAI;KACZ;IACD,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;MAC3B,KAAK,CAAC,YAAY,GAAG,KAAI;MACzB,OAAO,IAAI;KACZ;IACD,OAAO,KAAK;GACb;;EAEDL,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EAC1B,IAAI,EAAE,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,KAAK,IAAI,SAAS,EAAE;IAClE,KAAK,CAAC,YAAY,GAAG,GAAE;IACvB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;;EAED,OAAO,KAAK;EACb;;;AAGDK,IAAE,CAAC,uBAAuB,GAAG,SAAS,KAAK,EAAE;EAC3C,KAAK,CAAC,YAAY,GAAG,EAAC;EACtBJ,IAAI,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EACxB,IAAI,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI,UAAU;IAC5C,GAAG;MACD,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,KAAK,CAAC,YAAY,IAAI,EAAE,GAAG,IAAI,UAAS;MAClE,KAAK,CAAC,OAAO,GAAE;KAChB,QAAQ,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,KAAK,IAAI,YAAY,EAAE,IAAI,IAAI,SAAS;IACtE,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;;;AAGDI,IAAE,CAAC,8BAA8B,GAAG,SAAS,KAAK,EAAE;EAClDL,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;;EAE1B,IAAI,sBAAsB,CAAC,EAAE,CAAC,EAAE;IAC9B,KAAK,CAAC,YAAY,GAAG,CAAC,EAAC;IACvB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;;EAED;IACE,KAAK,CAAC,OAAO;IACb,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;KAC5B,EAAE,KAAK,IAAI,YAAY,EAAE,KAAK,IAAI,SAAS;IAC5C;IACA,KAAK,CAAC,YAAY,GAAG,CAAC,EAAC;IACvB,KAAK,CAAC,OAAO,GAAE;IACf;MACE,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS;MACvB,IAAI,CAAC,wCAAwC,CAAC,KAAK,CAAC;MACpD,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS;MACvB;MACA,OAAO,IAAI;KACZ;IACD,KAAK,CAAC,KAAK,CAAC,uBAAuB,EAAC;GACrC;;EAED,OAAO,KAAK;EACb;AACD,SAAS,sBAAsB,CAAC,EAAE,EAAE;EAClC;IACE,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;IACX,EAAE,KAAK,IAAI;GACZ;CACF;;;;;AAKDK,IAAE,CAAC,wCAAwC,GAAG,SAAS,KAAK,EAAE;EAC5DL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;;;EAGvB,IAAI,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IACxEA,IAAM,IAAI,GAAG,KAAK,CAAC,gBAAe;IAClC,IAAI,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,EAAE;MAC9CA,IAAM,KAAK,GAAG,KAAK,CAAC,gBAAe;MACnC,IAAI,CAAC,0CAA0C,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC;MACnE,OAAO,IAAI;KACZ;GACF;EACD,KAAK,CAAC,GAAG,GAAG,MAAK;;;EAGjB,IAAI,IAAI,CAAC,wCAAwC,CAAC,KAAK,CAAC,EAAE;IACxDA,IAAM,WAAW,GAAG,KAAK,CAAC,gBAAe;IACzC,IAAI,CAAC,yCAAyC,CAAC,KAAK,EAAE,WAAW,EAAC;IAClE,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;AACDK,IAAE,CAAC,0CAA0C,GAAG,SAAS,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;EAC3E,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC;IAC/C,EAAA,KAAK,CAAC,KAAK,CAAC,uBAAuB,EAAC,EAAA;EACtC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IACtD,EAAA,KAAK,CAAC,KAAK,CAAC,wBAAwB,EAAC,EAAA;EACxC;AACDA,IAAE,CAAC,yCAAyC,GAAG,SAAS,KAAK,EAAE,WAAW,EAAE;EAC1E,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;IACnD,EAAA,KAAK,CAAC,KAAK,CAAC,uBAAuB,EAAC,EAAA;EACvC;;;;AAIDA,IAAE,CAAC,6BAA6B,GAAG,SAAS,KAAK,EAAE;EACjDJ,IAAI,EAAE,GAAG,EAAC;EACV,KAAK,CAAC,eAAe,GAAG,GAAE;EAC1B,OAAO,8BAA8B,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE;IAC3D,KAAK,CAAC,eAAe,IAAIY,mBAAiB,CAAC,EAAE,EAAC;IAC9C,KAAK,CAAC,OAAO,GAAE;GAChB;EACD,OAAO,KAAK,CAAC,eAAe,KAAK,EAAE;EACpC;AACD,SAAS,8BAA8B,CAAC,EAAE,EAAE;EAC1C,OAAO,eAAe,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,IAAI;CAC1C;;;;AAIDR,IAAE,CAAC,8BAA8B,GAAG,SAAS,KAAK,EAAE;EAClDJ,IAAI,EAAE,GAAG,EAAC;EACV,KAAK,CAAC,eAAe,GAAG,GAAE;EAC1B,OAAO,+BAA+B,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE;IAC5D,KAAK,CAAC,eAAe,IAAIY,mBAAiB,CAAC,EAAE,EAAC;IAC9C,KAAK,CAAC,OAAO,GAAE;GAChB;EACD,OAAO,KAAK,CAAC,eAAe,KAAK,EAAE;EACpC;AACD,SAAS,+BAA+B,CAAC,EAAE,EAAE;EAC3C,OAAO,8BAA8B,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,EAAE,CAAC;CAChE;;;;AAIDR,IAAE,CAAC,wCAAwC,GAAG,SAAS,KAAK,EAAE;EAC5D,OAAO,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC;EAClD;;;AAGDA,IAAE,CAAC,wBAAwB,GAAG,SAAS,KAAK,EAAE;EAC5C,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,KAAK,CAAC,GAAG,CAAC,IAAI,UAAS;IACvB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAC;IAC9B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;MAC3B,OAAO,IAAI;KACZ;;IAED,KAAK,CAAC,KAAK,CAAC,8BAA8B,EAAC;GAC5C;EACD,OAAO,KAAK;EACb;;;;;AAKDA,IAAE,CAAC,kBAAkB,GAAG,SAAS,KAAK,EAAE;;;EACtC,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;IACtCL,IAAM,IAAI,GAAG,KAAK,CAAC,aAAY;IAC/B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,IAAII,MAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;MAC9DJ,IAAM,KAAK,GAAG,KAAK,CAAC,aAAY;MAChC,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;QAClD,KAAK,CAAC,KAAK,CAAC,yBAAyB,EAAC;OACvC;MACD,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE;QAC/C,KAAK,CAAC,KAAK,CAAC,uCAAuC,EAAC;OACrD;KACF;GACF;EACF;;;;AAIDK,IAAE,CAAC,mBAAmB,GAAG,SAAS,KAAK,EAAE;EACvCL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;;EAEvB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,EAAE;MACrC,OAAO,IAAI;KACZ;IACD,IAAI,KAAK,CAAC,OAAO,EAAE;;MAEjBA,IAAMc,IAAE,GAAG,KAAK,CAAC,OAAO,GAAE;MAC1B,IAAIA,IAAE,KAAK,IAAI,YAAY,YAAY,CAACA,IAAE,CAAC,EAAE;QAC3C,KAAK,CAAC,KAAK,CAAC,sBAAsB,EAAC;OACpC;MACD,KAAK,CAAC,KAAK,CAAC,gBAAgB,EAAC;KAC9B;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;;EAEDd,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EAC1B,IAAI,EAAE,KAAK,IAAI,UAAU;IACvB,KAAK,CAAC,YAAY,GAAG,GAAE;IACvB,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;;EAED,OAAO,KAAK;EACb;;;AAGDK,IAAE,CAAC,qBAAqB,GAAG,SAAS,KAAK,EAAE;EACzCL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;;EAEvB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,KAAK,CAAC,YAAY,GAAG,KAAI;IACzB,OAAO,IAAI;GACZ;;EAED,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC5C,KAAK,CAAC,YAAY,GAAG,KAAI;IACzB,OAAO,IAAI;GACZ;;EAED,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC7C,IAAI,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAAE;MAC5C,OAAO,IAAI;KACZ;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;;EAED;IACE,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC;IAC1C,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC;GACtC;EACF;;;AAGDK,IAAE,CAAC,4BAA4B,GAAG,SAAS,KAAK,EAAE;EAChDL,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EAC1B,IAAI,cAAc,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,IAAI,UAAU;IAC7C,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,KAAI;IAC9B,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;;;AAGDK,IAAE,CAAC,2BAA2B,GAAG,SAAS,KAAK,EAAE;EAC/CL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;IAC3B,IAAI,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;MAC3C,OAAO,IAAI;KACZ;IACD,IAAI,KAAK,CAAC,OAAO,EAAE;MACjB,KAAK,CAAC,KAAK,CAAC,gBAAgB,EAAC;KAC9B;IACD,KAAK,CAAC,GAAG,GAAG,MAAK;GAClB;EACD,OAAO,KAAK;EACb;;;AAGDK,IAAE,CAAC,uBAAuB,GAAG,SAAS,KAAK,EAAE;EAC3CL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvBC,IAAI,EAAE,GAAG,EAAC;EACV,KAAK,CAAC,YAAY,GAAG,EAAC;EACtB,OAAO,cAAc,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE;IAC3C,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,KAAK,CAAC,YAAY,IAAI,EAAE,GAAG,IAAI,UAAS;IAClE,KAAK,CAAC,OAAO,GAAE;GAChB;EACD,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK;EAC3B;AACD,SAAS,cAAc,CAAC,EAAE,EAAE;EAC1B,OAAO,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI;CACxC;;;AAGDI,IAAE,CAAC,mBAAmB,GAAG,SAAS,KAAK,EAAE;EACvCL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvBC,IAAI,EAAE,GAAG,EAAC;EACV,KAAK,CAAC,YAAY,GAAG,EAAC;EACtB,OAAO,UAAU,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE;IACvC,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,EAAE,EAAC;IAC3D,KAAK,CAAC,OAAO,GAAE;GAChB;EACD,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK;EAC3B;AACD,SAAS,UAAU,CAAC,EAAE,EAAE;EACtB;IACE,CAAC,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI;KAChC,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI,SAAS;KACzC,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI,SAAS;GAC3C;CACF;AACD,SAAS,QAAQ,CAAC,EAAE,EAAE;EACpB,IAAI,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI,UAAU;IAC5C,OAAO,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;GAChC;EACD,IAAI,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI,UAAU;IAC5C,OAAO,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;GAChC;EACD,OAAO,EAAE,GAAG,IAAI;CACjB;;;;AAIDI,IAAE,CAAC,mCAAmC,GAAG,SAAS,KAAK,EAAE;EACvD,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;IACpCL,IAAM,EAAE,GAAG,KAAK,CAAC,aAAY;IAC7B,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;MACpCA,IAAM,EAAE,GAAG,KAAK,CAAC,aAAY;MAC7B,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;QAC/C,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,aAAY;OAC3D,MAAM;QACL,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,CAAC,GAAG,GAAE;OACjC;KACF,MAAM;MACL,KAAK,CAAC,YAAY,GAAG,GAAE;KACxB;IACD,OAAO,IAAI;GACZ;EACD,OAAO,KAAK;EACb;;;AAGDK,IAAE,CAAC,oBAAoB,GAAG,SAAS,KAAK,EAAE;EACxCL,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;EAC1B,IAAI,YAAY,CAAC,EAAE,CAAC,EAAE;IACpB,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,KAAI;IAC9B,KAAK,CAAC,OAAO,GAAE;IACf,OAAO,IAAI;GACZ;EACD,KAAK,CAAC,YAAY,GAAG,EAAC;EACtB,OAAO,KAAK;EACb;AACD,SAAS,YAAY,CAAC,EAAE,EAAE;EACxB,OAAO,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI;CACxC;;;;;AAKDK,IAAE,CAAC,wBAAwB,GAAG,SAAS,KAAK,EAAE,MAAM,EAAE;EACpDL,IAAM,KAAK,GAAG,KAAK,CAAC,IAAG;EACvB,KAAK,CAAC,YAAY,GAAG,EAAC;EACtB,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;IAC/BD,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAE;IAC1B,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;MACnB,KAAK,CAAC,GAAG,GAAG,MAAK;MACjB,OAAO,KAAK;KACb;IACD,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,EAAE,EAAC;IAC3D,KAAK,CAAC,OAAO,GAAE;GAChB;EACD,OAAO,IAAI;CACZ;;;;;;ACxgCD,AAAO,IAAM,KAAK,GAAC,cACN,CAAC,CAAC,EAAE;EACf,IAAM,CAAC,IAAI,GAAG,CAAC,CAAC,KAAI;EACpB,IAAM,CAAC,KAAK,GAAG,CAAC,CAAC,MAAK;EACtB,IAAM,CAAC,KAAK,GAAG,CAAC,CAAC,MAAK;EACtB,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,IAAG;EAClB,IAAM,CAAC,CAAC,OAAO,CAAC,SAAS;IACvB,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAC,EAAA;EAC1D,IAAM,CAAC,CAAC,OAAO,CAAC,MAAM;IACpB,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAC,EAAA;CAChC,CAAA;;;;AAKHA,IAAMK,IAAE,GAAG,MAAM,CAAC,UAAS;;;;AAI3BA,IAAE,CAAC,IAAI,GAAG,WAAW;EACnB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO;IACtB,EAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,EAAC,EAAA;;EAEvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAG;EAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAK;EAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAM;EAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAQ;EACpC,IAAI,CAAC,SAAS,GAAE;EACjB;;AAEDA,IAAE,CAAC,QAAQ,GAAG,WAAW;EACvB,IAAI,CAAC,IAAI,GAAE;EACX,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC;EACvB;;;AAGD,IAAI,OAAO,MAAM,KAAK,WAAW;EAC/B,EAAAA,IAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW;;;IAC/B,OAAO;MACL,IAAI,EAAE,YAAG;QACPJ,IAAI,KAAK,GAAGG,MAAI,CAAC,QAAQ,GAAE;QAC3B,OAAO;UACL,IAAI,EAAE,KAAK,CAAC,IAAI,KAAKD,KAAE,CAAC,GAAG;UAC3B,KAAK,EAAE,KAAK;SACb;OACF;KACF;IACF,EAAA;;;;;AAKHE,IAAE,CAAC,UAAU,GAAG,WAAW;EACzB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;EAC7C;;;;;AAKDA,IAAE,CAAC,SAAS,GAAG,WAAW;EACxBJ,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,GAAE;EAClC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAA,IAAI,CAAC,SAAS,GAAE,EAAA;;EAE9D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAG;EACrB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,GAAE,EAAA;EAC9D,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAA,OAAO,IAAI,CAAC,WAAW,CAACE,KAAE,CAAC,GAAG,CAAC,EAAA;;EAElE,IAAI,UAAU,CAAC,QAAQ,EAAE,EAAA,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAA;OACpD,EAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAC,EAAA;EAC9C;;AAEDE,IAAE,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE;;;EAG5B,IAAI,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE;IACvE,EAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,EAAA;;EAExB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;EACnC;;AAEDA,IAAE,CAAC,iBAAiB,GAAG,WAAW;EAChCJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAC;EAC1C,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,EAAE,EAAA,OAAO,IAAI,EAAA;EACjDA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC9C,OAAO,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,SAAS;EACvC;;AAEDI,IAAE,CAAC,gBAAgB,GAAG,WAAW;;;EAC/BJ,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,GAAE;EAC3DA,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,EAAC;EACnE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,sBAAsB,EAAC,EAAA;EAChE,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,EAAC;EAClB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;IAC1B,UAAU,CAAC,SAAS,GAAG,MAAK;IAC5BA,IAAI,MAAK;IACT,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE;MACtE,EAAEG,MAAI,CAAC,QAAO;MACdA,MAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;KAC/C;GACF;EACD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS;IACxB,EAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG;2BACvD,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,EAAC,EAAA;EACvD;;AAEDC,IAAE,CAAC,eAAe,GAAG,SAAS,SAAS,EAAE;;;EACvCJ,IAAI,KAAK,GAAG,IAAI,CAAC,IAAG;EACpBA,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,GAAE;EAC3DA,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,SAAS,EAAC;EACrD,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;IACrD,EAAE,GAAGG,MAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAEA,MAAI,CAAC,GAAG,EAAC;GACvC;EACD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS;IACxB,EAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG;2BACrE,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,EAAC,EAAA;EACvD;;;;;AAKDC,IAAE,CAAC,SAAS,GAAG,WAAW;;;EACxB,IAAI,EAAE,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;IACzCJ,IAAI,EAAE,GAAGG,MAAI,CAAC,KAAK,CAAC,UAAU,CAACA,MAAI,CAAC,GAAG,EAAC;IACxC,QAAQ,EAAE;IACV,KAAK,EAAE,CAAC,CAAC,KAAK,GAAG;MACf,EAAEA,MAAI,CAAC,IAAG;MACV,KAAK;IACP,KAAK,EAAE;MACL,IAAIA,MAAI,CAAC,KAAK,CAAC,UAAU,CAACA,MAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QAC9C,EAAEA,MAAI,CAAC,IAAG;OACX;IACH,KAAK,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI;MAC3B,EAAEA,MAAI,CAAC,IAAG;MACV,IAAIA,MAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QAC1B,EAAEA,MAAI,CAAC,QAAO;QACdA,MAAI,CAAC,SAAS,GAAGA,MAAI,CAAC,IAAG;OAC1B;MACD,KAAK;IACP,KAAK,EAAE;MACL,QAAQA,MAAI,CAAC,KAAK,CAAC,UAAU,CAACA,MAAI,CAAC,GAAG,GAAG,CAAC,CAAC;MAC3C,KAAK,EAAE;QACLA,MAAI,CAAC,gBAAgB,GAAE;QACvB,KAAK;MACP,KAAK,EAAE;QACLA,MAAI,CAAC,eAAe,CAAC,CAAC,EAAC;QACvB,KAAK;MACP;QACE,MAAM,IAAI;OACX;MACD,KAAK;IACP;MACE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QACvF,EAAEA,MAAI,CAAC,IAAG;OACX,MAAM;QACL,MAAM,IAAI;OACX;KACF;GACF;EACF;;;;;;;AAODC,IAAE,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,GAAG,EAAE;EACnC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAG;EACnB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,GAAE,EAAA;EAC5DJ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAI;EACxB,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,IAAI,CAAC,KAAK,GAAG,IAAG;;EAEhB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAC;EAC7B;;;;;;;;;;;AAWDI,IAAE,CAAC,aAAa,GAAG,WAAW;EAC5BJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC9C,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAA;EAC1DA,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK,KAAK,EAAE,EAAE;IAChE,IAAI,CAAC,GAAG,IAAI,EAAC;IACb,OAAO,IAAI,CAAC,WAAW,CAACE,KAAE,CAAC,QAAQ,CAAC;GACrC,MAAM;IACL,EAAE,IAAI,CAAC,IAAG;IACV,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,GAAG,CAAC;GAChC;EACF;;AAEDE,IAAE,CAAC,eAAe,GAAG,WAAW;EAC9BJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC9C,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,EAAE;EAC9D,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,QAAQ,CAACE,KAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAA;EACnD,OAAO,IAAI,CAAC,QAAQ,CAACA,KAAE,CAAC,KAAK,EAAE,CAAC,CAAC;EAClC;;AAEDE,IAAE,CAAC,yBAAyB,GAAG,SAAS,IAAI,EAAE;EAC5CJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC9CA,IAAI,IAAI,GAAG,EAAC;EACZA,IAAI,SAAS,GAAG,IAAI,KAAK,EAAE,GAAGE,KAAE,CAAC,IAAI,GAAGA,KAAE,CAAC,OAAM;;;EAGjD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;IAC/D,EAAE,KAAI;IACN,SAAS,GAAGA,KAAE,CAAC,SAAQ;IACvB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;GAC3C;;EAED,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,QAAQ,CAACA,KAAE,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,CAAC,EAAA;EAC1D,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC;EACtC;;AAEDE,IAAE,CAAC,kBAAkB,GAAG,SAAS,IAAI,EAAE;EACrCJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC9C,IAAI,IAAI,KAAK,IAAI,EAAE,EAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,GAAG,GAAGE,KAAE,CAAC,SAAS,GAAGA,KAAE,CAAC,UAAU,EAAE,CAAC,CAAC,EAAA;EACvF,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,QAAQ,CAACA,KAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAA;EACnD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,GAAG,GAAGA,KAAE,CAAC,SAAS,GAAGA,KAAE,CAAC,UAAU,EAAE,CAAC,CAAC;EACrE;;AAEDE,IAAE,CAAC,eAAe,GAAG,WAAW;EAC9BJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC9C,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,QAAQ,CAACE,KAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAA;EACnD,OAAO,IAAI,CAAC,QAAQ,CAACA,KAAE,CAAC,UAAU,EAAE,CAAC,CAAC;EACvC;;AAEDE,IAAE,CAAC,kBAAkB,GAAG,SAAS,IAAI,EAAE;EACrCJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC9C,IAAI,IAAI,KAAK,IAAI,EAAE;IACjB,IAAI,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE;SAC1E,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;;MAE1F,IAAI,CAAC,eAAe,CAAC,CAAC,EAAC;MACvB,IAAI,CAAC,SAAS,GAAE;MAChB,OAAO,IAAI,CAAC,SAAS,EAAE;KACxB;IACD,OAAO,IAAI,CAAC,QAAQ,CAACE,KAAE,CAAC,MAAM,EAAE,CAAC,CAAC;GACnC;EACD,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,QAAQ,CAACA,KAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAA;EACnD,OAAO,IAAI,CAAC,QAAQ,CAACA,KAAE,CAAC,OAAO,EAAE,CAAC,CAAC;EACpC;;AAEDE,IAAE,CAAC,eAAe,GAAG,SAAS,IAAI,EAAE;EAClCJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC9CA,IAAI,IAAI,GAAG,EAAC;EACZ,IAAI,IAAI,KAAK,IAAI,EAAE;IACjB,IAAI,GAAG,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,EAAC;IACxE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,QAAQ,CAACE,KAAE,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,CAAC,EAAA;IAC5F,OAAO,IAAI,CAAC,QAAQ,CAACA,KAAE,CAAC,QAAQ,EAAE,IAAI,CAAC;GACxC;EACD,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE;MAC1F,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;;IAE9C,IAAI,CAAC,eAAe,CAAC,CAAC,EAAC;IACvB,IAAI,CAAC,SAAS,GAAE;IAChB,OAAO,IAAI,CAAC,SAAS,EAAE;GACxB;EACD,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,IAAI,GAAG,EAAC,EAAA;EACzB,OAAO,IAAI,CAAC,QAAQ,CAACA,KAAE,CAAC,UAAU,EAAE,IAAI,CAAC;EAC1C;;AAEDE,IAAE,CAAC,iBAAiB,GAAG,SAAS,IAAI,EAAE;EACpCJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;EAC9C,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,QAAQ,CAACE,KAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAA;EACtG,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IAC/D,IAAI,CAAC,GAAG,IAAI,EAAC;IACb,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,KAAK,CAAC;GAClC;EACD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,EAAE,GAAGA,KAAE,CAAC,EAAE,GAAGA,KAAE,CAAC,MAAM,EAAE,CAAC,CAAC;EACzD;;AAEDE,IAAE,CAAC,gBAAgB,GAAG,SAAS,IAAI,EAAE;EACnC,QAAQ,IAAI;;;EAGZ,KAAK,EAAE;IACL,OAAO,IAAI,CAAC,aAAa,EAAE;;;EAG7B,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACF,KAAE,CAAC,MAAM,CAAC;EACvD,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,MAAM,CAAC;EACvD,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,IAAI,CAAC;EACrD,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,KAAK,CAAC;EACtD,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,QAAQ,CAAC;EACzD,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,QAAQ,CAAC;EACzD,KAAK,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,MAAM,CAAC;EACxD,KAAK,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,MAAM,CAAC;EACxD,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,KAAK,CAAC;EACtD,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,QAAQ,CAAC;;EAEzD,KAAK,EAAE;IACL,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,EAAE,EAAA,KAAK,EAAA;IACvC,EAAE,IAAI,CAAC,IAAG;IACV,OAAO,IAAI,CAAC,WAAW,CAACA,KAAE,CAAC,SAAS,CAAC;;EAEvC,KAAK,EAAE;IACLF,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAC;IAC9C,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAA;IAChE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;MACjC,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAA;MAC/D,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAA;KAC/D;;;;EAIH,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE;IAC7E,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;EAG/B,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE;IACd,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;;;;;;;EAO9B,KAAK,EAAE;IACL,OAAO,IAAI,CAAC,eAAe,EAAE;;EAE/B,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE;IACd,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;;EAE7C,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE;IACf,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;;EAEtC,KAAK,EAAE;IACL,OAAO,IAAI,CAAC,eAAe,EAAE;;EAE/B,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE;IACd,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;;EAEtC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE;IACd,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;;EAEnC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE;IACd,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;;EAErC,KAAK,GAAG;IACN,OAAO,IAAI,CAAC,QAAQ,CAACE,KAAE,CAAC,MAAM,EAAE,CAAC,CAAC;GACnC;;EAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,wBAAwB,GAAG,iBAAiB,CAAC,IAAI,CAAC,GAAG,GAAG,EAAC;EAC/E;;AAEDE,IAAE,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;EACjCJ,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAC;EACrD,IAAI,CAAC,GAAG,IAAI,KAAI;EAChB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC;EACnC;;AAEDI,IAAE,CAAC,UAAU,GAAG,WAAW;;;EACzBJ,IAAI,OAAO,EAAE,OAAO,EAAE,KAAK,GAAG,IAAI,CAAC,IAAG;EACtC,SAAS;IACP,IAAIG,MAAI,CAAC,GAAG,IAAIA,MAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAAA,MAAI,CAAC,KAAK,CAAC,KAAK,EAAE,iCAAiC,EAAC,EAAA;IACvFH,IAAI,EAAE,GAAGG,MAAI,CAAC,KAAK,CAAC,MAAM,CAACA,MAAI,CAAC,GAAG,EAAC;IACpC,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAAA,MAAI,CAAC,KAAK,CAAC,KAAK,EAAE,iCAAiC,EAAC,EAAA;IAC5E,IAAI,CAAC,OAAO,EAAE;MACZ,IAAI,EAAE,KAAK,GAAG,EAAE,EAAA,OAAO,GAAG,KAAI,EAAA;WACzB,IAAI,EAAE,KAAK,GAAG,IAAI,OAAO,EAAE,EAAA,OAAO,GAAG,MAAK,EAAA;WAC1C,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,EAAA,KAAK,EAAA;MACtC,OAAO,GAAG,EAAE,KAAK,KAAI;KACtB,MAAM,EAAA,OAAO,GAAG,MAAK,EAAA;IACtB,EAAEA,MAAI,CAAC,IAAG;GACX;EACDH,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAC;EAC/C,EAAE,IAAI,CAAC,IAAG;EACVA,IAAI,UAAU,GAAG,IAAI,CAAC,IAAG;EACzBA,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,GAAE;EAC5B,IAAI,IAAI,CAAC,WAAW,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,UAAU,EAAC,EAAA;;;EAGjDD,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAC;EACtF,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAC;EAClC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAC;EAC/B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAC;;;EAGjCC,IAAI,KAAK,GAAG,KAAI;EAChB,IAAI;IACF,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,EAAC;GACnC,CAAC,OAAO,CAAC,EAAE;;;GAGX;;EAED,OAAO,IAAI,CAAC,WAAW,CAACE,KAAE,CAAC,MAAM,EAAE,CAAC,SAAA,OAAO,EAAE,OAAA,KAAK,EAAE,OAAA,KAAK,CAAC,CAAC;EAC5D;;;;;;AAMDE,IAAE,CAAC,OAAO,GAAG,SAAS,KAAK,EAAE,GAAG,EAAE;;;EAChCJ,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,EAAC;EAC/B,KAAKA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,QAAQ,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;IAC5DA,IAAI,IAAI,GAAGG,MAAI,CAAC,KAAK,CAAC,UAAU,CAACA,MAAI,CAAC,GAAG,CAAC,EAAE,GAAG,YAAA;IAC/C,IAAI,IAAI,IAAI,EAAE,EAAE,EAAA,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG,GAAE,EAAA;SAC/B,IAAI,IAAI,IAAI,EAAE,EAAE,EAAA,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG,GAAE,EAAA;SACpC,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE,EAAA,GAAG,GAAG,IAAI,GAAG,GAAE,EAAA;SAC7C,EAAA,GAAG,GAAG,SAAQ,EAAA;IACnB,IAAI,GAAG,IAAI,KAAK,EAAE,EAAA,KAAK,EAAA;IACvB,EAAEA,MAAI,CAAC,IAAG;IACV,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,IAAG;GAC5B;EACD,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,EAAE,EAAA,OAAO,IAAI,EAAA;;EAE9E,OAAO,KAAK;EACb;;AAEDC,IAAE,CAAC,eAAe,GAAG,SAAS,KAAK,EAAE;EACnC,IAAI,CAAC,GAAG,IAAI,EAAC;EACbJ,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC;EAC7B,IAAI,GAAG,IAAI,IAAI,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,2BAA2B,GAAG,KAAK,EAAC,EAAA;EAChF,IAAI,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,kCAAkC,EAAC,EAAA;EACzG,OAAO,IAAI,CAAC,WAAW,CAACE,KAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACrC;;;;AAIDE,IAAE,CAAC,UAAU,GAAG,SAAS,aAAa,EAAE;EACtCJ,IAAI,KAAK,GAAG,IAAI,CAAC,IAAG;EACpB,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,gBAAgB,EAAC,EAAA;EACpFA,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAE;EACxE,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,gBAAgB,EAAC,EAAA;EAC7D,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA,KAAK,GAAG,MAAK,EAAA;EAC1EA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAC;EAC1C,IAAI,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;IACzB,EAAE,IAAI,CAAC,IAAG;IACV,IAAI,CAAC,OAAO,CAAC,EAAE,EAAC;IAChB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAC;GACvC;EACD,IAAI,CAAC,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;IAC3C,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,GAAG,EAAC;IACxC,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,EAAA,EAAE,IAAI,CAAC,IAAG,EAAA;IAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,gBAAgB,EAAC,EAAA;GACnE;EACD,IAAI,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,kCAAkC,EAAC,EAAA;;EAEzGA,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAC;EAC3CA,IAAI,GAAG,GAAG,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,EAAC;EACpD,OAAO,IAAI,CAAC,WAAW,CAACE,KAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACrC;;;;AAIDE,IAAE,CAAC,aAAa,GAAG,WAAW;EAC5BJ,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAI;;EAE9C,IAAI,EAAE,KAAK,GAAG,EAAE;IACd,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,EAAE,EAAA,IAAI,CAAC,UAAU,GAAE,EAAA;IACnDA,IAAI,OAAO,GAAG,EAAE,IAAI,CAAC,IAAG;IACxB,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAC;IACrE,EAAE,IAAI,CAAC,IAAG;IACV,IAAI,IAAI,GAAG,QAAQ,EAAE,EAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,0BAA0B,EAAC,EAAA;GAClF,MAAM;IACL,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAC;GAC3B;EACD,OAAO,IAAI;EACZ;;AAED,SAAS,iBAAiB,CAAC,IAAI,EAAE;;EAE/B,IAAI,IAAI,IAAI,MAAM,EAAE,EAAA,OAAO,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAA;EACpD,IAAI,IAAI,QAAO;EACf,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,IAAI,EAAE,IAAI,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC;CAC1E;;AAEDI,IAAE,CAAC,UAAU,GAAG,SAAS,KAAK,EAAE;;;EAC9BJ,IAAI,GAAG,GAAG,EAAE,EAAE,UAAU,GAAG,EAAE,IAAI,CAAC,IAAG;EACrC,SAAS;IACP,IAAIG,MAAI,CAAC,GAAG,IAAIA,MAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAAA,MAAI,CAAC,KAAK,CAACA,MAAI,CAAC,KAAK,EAAE,8BAA8B,EAAC,EAAA;IACzFH,IAAI,EAAE,GAAGG,MAAI,CAAC,KAAK,CAAC,UAAU,CAACA,MAAI,CAAC,GAAG,EAAC;IACxC,IAAI,EAAE,KAAK,KAAK,EAAE,EAAA,KAAK,EAAA;IACvB,IAAI,EAAE,KAAK,EAAE,EAAE;MACb,GAAG,IAAIA,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAEA,MAAI,CAAC,GAAG,EAAC;MAC7C,GAAG,IAAIA,MAAI,CAAC,eAAe,CAAC,KAAK,EAAC;MAClC,UAAU,GAAGA,MAAI,CAAC,IAAG;KACtB,MAAM;MACL,IAAI,SAAS,CAAC,EAAE,EAAEA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC,EAAE,EAAAA,MAAI,CAAC,KAAK,CAACA,MAAI,CAAC,KAAK,EAAE,8BAA8B,EAAC,EAAA;MACzG,EAAEA,MAAI,CAAC,IAAG;KACX;GACF;EACD,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC;EAC/C,OAAO,IAAI,CAAC,WAAW,CAACD,KAAE,CAAC,MAAM,EAAE,GAAG,CAAC;EACxC;;;;AAIDH,IAAM,6BAA6B,GAAG,GAAE;;AAExCK,IAAE,CAAC,oBAAoB,GAAG,WAAW;EACnC,IAAI,CAAC,iBAAiB,GAAG,KAAI;EAC7B,IAAI;IACF,IAAI,CAAC,aAAa,GAAE;GACrB,CAAC,OAAO,GAAG,EAAE;IACZ,IAAI,GAAG,KAAK,6BAA6B,EAAE;MACzC,IAAI,CAAC,wBAAwB,GAAE;KAChC,MAAM;MACL,MAAM,GAAG;KACV;GACF;;EAED,IAAI,CAAC,iBAAiB,GAAG,MAAK;EAC/B;;AAEDA,IAAE,CAAC,kBAAkB,GAAG,SAAS,QAAQ,EAAE,OAAO,EAAE;EAClD,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IAC3D,MAAM,6BAA6B;GACpC,MAAM;IACL,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAC;GAC9B;EACF;;AAEDA,IAAE,CAAC,aAAa,GAAG,WAAW;;;EAC5BJ,IAAI,GAAG,GAAG,EAAE,EAAE,UAAU,GAAG,IAAI,CAAC,IAAG;EACnC,SAAS;IACP,IAAIG,MAAI,CAAC,GAAG,IAAIA,MAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAAA,MAAI,CAAC,KAAK,CAACA,MAAI,CAAC,KAAK,EAAE,uBAAuB,EAAC,EAAA;IAClFH,IAAI,EAAE,GAAGG,MAAI,CAAC,KAAK,CAAC,UAAU,CAACA,MAAI,CAAC,GAAG,EAAC;IACxC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAIA,MAAI,CAAC,KAAK,CAAC,UAAU,CAACA,MAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MACzE,IAAIA,MAAI,CAAC,GAAG,KAAKA,MAAI,CAAC,KAAK,KAAKA,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,QAAQ,IAAIC,MAAI,CAAC,IAAI,KAAKD,KAAE,CAAC,eAAe,CAAC,EAAE;QAC9F,IAAI,EAAE,KAAK,EAAE,EAAE;UACbC,MAAI,CAAC,GAAG,IAAI,EAAC;UACb,OAAOA,MAAI,CAAC,WAAW,CAACD,KAAE,CAAC,YAAY,CAAC;SACzC,MAAM;UACL,EAAEC,MAAI,CAAC,IAAG;UACV,OAAOA,MAAI,CAAC,WAAW,CAACD,KAAE,CAAC,SAAS,CAAC;SACtC;OACF;MACD,GAAG,IAAIC,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAEA,MAAI,CAAC,GAAG,EAAC;MAC7C,OAAOA,MAAI,CAAC,WAAW,CAACD,KAAE,CAAC,QAAQ,EAAE,GAAG,CAAC;KAC1C;IACD,IAAI,EAAE,KAAK,EAAE,EAAE;MACb,GAAG,IAAIC,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAEA,MAAI,CAAC,GAAG,EAAC;MAC7C,GAAG,IAAIA,MAAI,CAAC,eAAe,CAAC,IAAI,EAAC;MACjC,UAAU,GAAGA,MAAI,CAAC,IAAG;KACtB,MAAM,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE;MACxB,GAAG,IAAIA,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAEA,MAAI,CAAC,GAAG,EAAC;MAC7C,EAAEA,MAAI,CAAC,IAAG;MACV,QAAQ,EAAE;MACV,KAAK,EAAE;QACL,IAAIA,MAAI,CAAC,KAAK,CAAC,UAAU,CAACA,MAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,EAAA,EAAEA,MAAI,CAAC,IAAG,EAAA;MACxD,KAAK,EAAE;QACL,GAAG,IAAI,KAAI;QACX,KAAK;MACP;QACE,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,EAAC;QAC9B,KAAK;OACN;MACD,IAAIA,MAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QAC1B,EAAEA,MAAI,CAAC,QAAO;QACdA,MAAI,CAAC,SAAS,GAAGA,MAAI,CAAC,IAAG;OAC1B;MACD,UAAU,GAAGA,MAAI,CAAC,IAAG;KACtB,MAAM;MACL,EAAEA,MAAI,CAAC,IAAG;KACX;GACF;EACF;;;AAGDC,IAAE,CAAC,wBAAwB,GAAG,WAAW;;;EACvC,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE;IAC/C,QAAQD,MAAI,CAAC,KAAK,CAACA,MAAI,CAAC,GAAG,CAAC;IAC5B,KAAK,IAAI;MACP,EAAEA,MAAI,CAAC,IAAG;MACV,KAAK;;IAEP,KAAK,GAAG;MACN,IAAIA,MAAI,CAAC,KAAK,CAACA,MAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QACpC,KAAK;OACN;;;IAGH,KAAK,GAAG;MACN,OAAOA,MAAI,CAAC,WAAW,CAACD,KAAE,CAAC,eAAe,EAAEC,MAAI,CAAC,KAAK,CAAC,KAAK,CAACA,MAAI,CAAC,KAAK,EAAEA,MAAI,CAAC,GAAG,CAAC,CAAC;;;KAGpF;GACF;EACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,uBAAuB,EAAC;EAChD;;;;AAIDC,IAAE,CAAC,eAAe,GAAG,SAAS,UAAU,EAAE;EACxCJ,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,GAAG,EAAC;EAC1C,EAAE,IAAI,CAAC,IAAG;EACV,QAAQ,EAAE;EACV,KAAK,GAAG,EAAE,OAAO,IAAI;EACrB,KAAK,GAAG,EAAE,OAAO,IAAI;EACrB,KAAK,GAAG,EAAE,OAAO,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;EACzD,KAAK,GAAG,EAAE,OAAO,iBAAiB,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;EACxD,KAAK,GAAG,EAAE,OAAO,IAAI;EACrB,KAAK,EAAE,EAAE,OAAO,IAAI;EACpB,KAAK,GAAG,EAAE,OAAO,QAAQ;EACzB,KAAK,GAAG,EAAE,OAAO,IAAI;EACrB,KAAK,EAAE,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,EAAA,EAAE,IAAI,CAAC,IAAG,EAAA;EAC/D,KAAK,EAAE;IACL,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAO,EAAE;IACzE,OAAO,EAAE;EACX;IACE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;MACxBA,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAC;MACrEA,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAC;MACjC,IAAI,KAAK,GAAG,GAAG,EAAE;QACf,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC;QAChC,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAC;OAC9B;MACD,IAAI,CAAC,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAC;MAC/B,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAC;MACpC,IAAI,CAAC,QAAQ,KAAK,GAAG,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,EAAE;QAC/E,IAAI,CAAC,kBAAkB;UACrB,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM;UAC9B,UAAU;cACN,kCAAkC;cAClC,8BAA8B;UACnC;OACF;MACD,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;KAClC;IACD,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE;;;MAGjB,OAAO,EAAE;KACV;IACD,OAAO,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;GAC/B;EACF;;;;AAIDI,IAAE,CAAC,WAAW,GAAG,SAAS,GAAG,EAAE;EAC7BJ,IAAI,OAAO,GAAG,IAAI,CAAC,IAAG;EACtBA,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAC;EAC7B,IAAI,CAAC,KAAK,IAAI,EAAE,EAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,+BAA+B,EAAC,EAAA;EACjF,OAAO,CAAC;EACT;;;;;;;;AAQDI,IAAE,CAAC,SAAS,GAAG,WAAW;;;EACxB,IAAI,CAAC,WAAW,GAAG,MAAK;EACxBJ,IAAI,IAAI,GAAG,EAAE,EAAE,KAAK,GAAG,IAAI,EAAE,UAAU,GAAG,IAAI,CAAC,IAAG;EAClDA,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,EAAC;EAC1C,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;IACnCA,IAAI,EAAE,GAAGG,MAAI,CAAC,iBAAiB,GAAE;IACjC,IAAI,gBAAgB,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;MAChCA,MAAI,CAAC,GAAG,IAAI,EAAE,IAAI,MAAM,GAAG,CAAC,GAAG,EAAC;KACjC,MAAM,IAAI,EAAE,KAAK,EAAE,EAAE;MACpBA,MAAI,CAAC,WAAW,GAAG,KAAI;MACvB,IAAI,IAAIA,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAEA,MAAI,CAAC,GAAG,EAAC;MAC9CH,IAAI,QAAQ,GAAGG,MAAI,CAAC,IAAG;MACvB,IAAIA,MAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAEA,MAAI,CAAC,GAAG,CAAC,KAAK,GAAG;QAC3C,EAAAA,MAAI,CAAC,kBAAkB,CAACA,MAAI,CAAC,GAAG,EAAE,2CAA2C,EAAC,EAAA;MAChF,EAAEA,MAAI,CAAC,IAAG;MACVH,IAAI,GAAG,GAAGG,MAAI,CAAC,aAAa,GAAE;MAC9B,IAAI,CAAC,CAAC,KAAK,GAAG,iBAAiB,GAAG,gBAAgB,EAAE,GAAG,EAAE,MAAM,CAAC;QAC9D,EAAAA,MAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,wBAAwB,EAAC,EAAA;MAC7D,IAAI,IAAI,iBAAiB,CAAC,GAAG,EAAC;MAC9B,UAAU,GAAGA,MAAI,CAAC,IAAG;KACtB,MAAM;MACL,KAAK;KACN;IACD,KAAK,GAAG,MAAK;GACd;EACD,OAAO,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC;EACrD;;;;;AAKDC,IAAE,CAAC,QAAQ,GAAG,WAAW;EACvBJ,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3BA,IAAI,IAAI,GAAGE,KAAE,CAAC,KAAI;EAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;IAC5B,IAAI,IAAI,CAAC,WAAW,EAAE,EAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,6BAA6B,GAAG,IAAI,EAAC,EAAA;IAC7F,IAAI,GAAGY,UAAY,CAAC,IAAI,EAAC;GAC1B;EACD,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;CACpC;;AC9rBD;;;;;;;;;;;;;;;;AAgBA,AAkBOf,IAAM,OAAO,GAAG,QAAO;;;;;;;;;AAS9B,AAAO,SAAS,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE;EACpC,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC;CACpC;;;;;;AAMD,AAAO,SAAS,iBAAiB,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;EACrD,OAAO,MAAM,CAAC,iBAAiB,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC;CACrD;;;;;AAKD,AAAO,SAAS,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;EACxC,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC;CACxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}