{"name": "@webassemblyjs/wasm-parser", "version": "1.7.11", "keywords": ["webassembly", "javascript", "ast", "parser", "wasm"], "description": "WebAssembly binary format parser", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "mocha"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-api-error": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/ieee754": "1.7.11", "@webassemblyjs/leb128": "1.7.11", "@webassemblyjs/utf8": "1.7.11"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"@webassemblyjs/helper-buffer": "1.7.11", "@webassemblyjs/helper-test-framework": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/wasm-gen": "1.7.11", "@webassemblyjs/wast-parser": "1.7.11", "wabt": "1.0.0-nightly.20180421"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909"}