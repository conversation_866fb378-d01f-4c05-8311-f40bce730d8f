{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///long.js", "webpack:///webpack/bootstrap d8921b8c3ad0790b3cc1", "webpack:///./src/long.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "<PERSON>", "low", "high", "unsigned", "isLong", "obj", "fromInt", "value", "cachedObj", "cache", "UINT_CACHE", "fromBits", "INT_CACHE", "fromNumber", "isNaN", "UZERO", "ZERO", "TWO_PWR_64_DBL", "MAX_UNSIGNED_VALUE", "TWO_PWR_63_DBL", "MIN_VALUE", "MAX_VALUE", "neg", "TWO_PWR_32_DBL", "lowBits", "highBits", "fromString", "str", "radix", "length", "Error", "RangeError", "indexOf", "substring", "radixToPower", "pow_dbl", "result", "size", "Math", "min", "parseInt", "power", "mul", "add", "fromValue", "val", "wasm", "WebAssembly", "Instance", "<PERSON><PERSON><PERSON>", "Uint8Array", "e", "__isLong__", "pow", "TWO_PWR_16_DBL", "TWO_PWR_24", "ONE", "UONE", "NEG_ONE", "LongPrototype", "toInt", "toNumber", "toString", "isZero", "isNegative", "eq", "radixLong", "div", "rem1", "sub", "rem", "remDiv", "intval", "digits", "getHighBits", "getHighBitsUnsigned", "getLowBits", "getLowBitsUnsigned", "getNumBitsAbs", "bit", "eqz", "isPositive", "isOdd", "isEven", "equals", "other", "notEquals", "neq", "ne", "lessThan", "comp", "lt", "lessThanOrEqual", "lte", "le", "greaterThan", "gt", "greaterThanOrEqual", "gte", "ge", "compare", "thisNeg", "otherNeg", "negate", "not", "addend", "a48", "a32", "a16", "a00", "b48", "b32", "b16", "b00", "c48", "c32", "c16", "c00", "subtract", "subtrahend", "multiply", "multiplier", "get_high", "divide", "divisor", "div_u", "div_s", "approx", "res", "toUnsigned", "shru", "shr", "shl", "max", "floor", "log2", "ceil", "log", "LN2", "delta", "approxRes", "approxRem", "modulo", "rem_u", "rem_s", "mod", "and", "or", "xor", "shiftLeft", "numBits", "shiftRight", "shiftRightUnsigned", "shr_u", "toSigned", "toBytes", "toBytesLE", "toBytesBE", "hi", "lo", "fromBytes", "bytes", "fromBytesLE", "fromBytesBE"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,UAAAH,GACA,gBAAAC,SACAA,QAAA,KAAAD,IAEAD,EAAA,KAAAC,KACC,mBAAAK,WAAAC,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAR,OAGA,IAAAC,GAAAQ,EAAAD,IACAE,EAAAF,EACAG,GAAA,EACAX,WAUA,OANAM,GAAAE,GAAAI,KAAAX,EAAAD,QAAAC,IAAAD,QAAAO,GAGAN,EAAAU,GAAA,EAGAV,EAAAD,QAvBA,GAAAS,KA4DA,OAhCAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,SAAAf,EAAAgB,EAAAC,GACAV,EAAAW,EAAAlB,EAAAgB,IACAG,OAAAC,eAAApB,EAAAgB,GACAK,cAAA,EACAC,YAAA,EACAC,IAAAN,KAMAV,EAAAiB,EAAA,SAAAvB,GACA,GAAAgB,GAAAhB,KAAAwB,WACA,WAA2B,MAAAxB,GAAA,SAC3B,WAAiC,MAAAA,GAEjC,OADAM,GAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAQ,EAAAC,GAAsD,MAAAR,QAAAS,UAAAC,eAAAjB,KAAAc,EAAAC,IAGtDpB,EAAAuB,EAAA,GAGAvB,IAAAwB,EAAA,KDgBM,SAAU9B,EAAQD,GEpDxB,QAAAgC,GAAAC,EAAAC,EAAAC,GAMA9B,KAAA4B,IAAA,EAAAA,EAMA5B,KAAA6B,KAAA,EAAAA,EAMA7B,KAAA8B,aAoCA,QAAAC,GAAAC,GACA,YAAAA,KAAA,YA+BA,QAAAC,GAAAC,EAAAJ,GACA,GAAAE,GAAAG,EAAAC,CACA,OAAAN,IACAI,KAAA,GACAE,EAAA,GAAAF,KAAA,OACAC,EAAAE,EAAAH,IAEAC,GAEAH,EAAAM,EAAAJ,GAAA,EAAAA,GAAA,WACAE,IACAC,EAAAH,GAAAF,GACAA,KAEAE,GAAA,GACAE,GAAA,KAAAF,KAAA,OACAC,EAAAI,EAAAL,IAEAC,GAEAH,EAAAM,EAAAJ,IAAA,WACAE,IACAG,EAAAL,GAAAF,GACAA,IAmBA,QAAAQ,GAAAN,EAAAJ,GACA,GAAAW,MAAAP,GACA,MAAAJ,GAAAY,EAAAC,CACA,IAAAb,EAAA,CACA,GAAAI,EAAA,EACA,MAAAQ,EACA,IAAAR,GAAAU,EACA,MAAAC,OACK,CACL,GAAAX,IAAAY,EACA,MAAAC,EACA,IAAAb,EAAA,GAAAY,EACA,MAAAE,GAEA,MAAAd,GAAA,EACAM,GAAAN,EAAAJ,GAAAmB,MACAX,EAAAJ,EAAAgB,EAAA,EAAAhB,EAAAgB,EAAA,EAAApB,GAmBA,QAAAQ,GAAAa,EAAAC,EAAAtB,GACA,UAAAH,GAAAwB,EAAAC,EAAAtB,GA8BA,QAAAuB,GAAAC,EAAAxB,EAAAyB,GACA,OAAAD,EAAAE,OACA,KAAAC,OAAA,eACA,YAAAH,GAAA,aAAAA,GAAA,cAAAA,GAAA,cAAAA,EACA,MAAAX,EASA,IARA,gBAAAb,IAEAyB,EAAAzB,EACAA,GAAA,GAEAA,OAEAyB,KAAA,IACA,MAAAA,EACA,KAAAG,YAAA,QAEA,IAAAjC,EACA,KAAAA,EAAA6B,EAAAK,QAAA,QACA,KAAAF,OAAA,kBACA,QAAAhC,EACA,MAAA4B,GAAAC,EAAAM,UAAA,GAAA9B,EAAAyB,GAAAN,KAQA,QAHAY,GAAArB,EAAAsB,EAAAP,EAAA,IAEAQ,EAAApB,EACAtC,EAAA,EAAmBA,EAAAiD,EAAAE,OAAgBnD,GAAA,GACnC,GAAA2D,GAAAC,KAAAC,IAAA,EAAAZ,EAAAE,OAAAnD,GACA6B,EAAAiC,SAAAb,EAAAM,UAAAvD,IAAA2D,GAAAT,EACA,IAAAS,EAAA,GACA,GAAAI,GAAA5B,EAAAsB,EAAAP,EAAAS,GACAD,KAAAM,IAAAD,GAAAE,IAAA9B,EAAAN,QAEA6B,KAAAM,IAAAR,GACAE,IAAAO,IAAA9B,EAAAN,IAIA,MADA6B,GAAAjC,WACAiC,EAoBA,QAAAQ,GAAAC,EAAA1C,GACA,sBAAA0C,GACAhC,EAAAgC,EAAA1C,GACA,gBAAA0C,GACAnB,EAAAmB,EAAA1C,GAEAQ,EAAAkC,EAAA5C,IAAA4C,EAAA3C,KAAA,iBAAAC,KAAA0C,EAAA1C,UA7RAlC,EAAAD,QAAAgC,CAKA,IAAA8C,GAAA,IAEA,KACAA,EAAA,GAAAC,aAAAC,SAAA,GAAAD,aAAAE,OAAA,GAAAC,aACA,u2BACSlF,QACR,MAAAmF,IA0DDnD,EAAAJ,UAAAwD,WAEAjE,OAAAC,eAAAY,EAAAJ,UAAA,cAAqDW,OAAA,IAkBrDP,EAAAI,QAOA,IAAAQ,MAOAF,IA0CAV,GAAAM,UAkCAN,EAAAa,aAsBAb,EAAAW,UASA,IAAAwB,GAAAG,KAAAe,GA4DArD,GAAA0B,aAyBA1B,EAAA4C,WAUA,IAcArB,GAAA+B,WAOArC,EAAAM,IAOAJ,EAAAF,EAAA,EAOAsC,EAAAjD,EA5BA,OAkCAU,EAAAV,EAAA,EAMAN,GAAAgB,MAMA,IAAAD,GAAAT,EAAA,KAMAN,GAAAe,OAMA,IAAAyC,GAAAlD,EAAA,EAMAN,GAAAwD,KAMA,IAAAC,GAAAnD,EAAA,KAMAN,GAAAyD,MAMA,IAAAC,GAAApD,GAAA,EAMAN,GAAA0D,SAMA,IAAArC,GAAAV,GAAA,gBAMAX,GAAAqB,WAMA,IAAAH,GAAAP,GAAA,QAMAX,GAAAkB,oBAMA,IAAAE,GAAAT,EAAA,iBAMAX,GAAAoB,WAMA,IAAAuC,GAAA3D,EAAAJ,SAMA+D,GAAAC,MAAA,WACA,MAAAvF,MAAA8B,SAAA9B,KAAA4B,MAAA,EAAA5B,KAAA4B,KAOA0D,EAAAE,SAAA,WACA,MAAAxF,MAAA8B,UACA9B,KAAA6B,OAAA,GAAAqB,GAAAlD,KAAA4B,MAAA,GACA5B,KAAA6B,KAAAqB,GAAAlD,KAAA4B,MAAA,IAUA0D,EAAAG,SAAA,SAAAlC,GAEA,IADAA,KAAA,IACA,MAAAA,EACA,KAAAG,YAAA,QACA,IAAA1D,KAAA0F,SACA,SACA,IAAA1F,KAAA2F,aAAA,CACA,GAAA3F,KAAA4F,GAAA7C,GAAA,CAGA,GAAA8C,GAAArD,EAAAe,GACAuC,EAAA9F,KAAA8F,IAAAD,GACAE,EAAAD,EAAAzB,IAAAwB,GAAAG,IAAAhG,KACA,OAAA8F,GAAAL,SAAAlC,GAAAwC,EAAAR,QAAAE,SAAAlC,GAEA,UAAAvD,KAAAiD,MAAAwC,SAAAlC,GAQA,IAHA,GAAAM,GAAArB,EAAAsB,EAAAP,EAAA,GAAAvD,KAAA8B,UACAmE,EAAAjG,KACA+D,EAAA,KACA,CACA,GAAAmC,GAAAD,EAAAH,IAAAjC,GACAsC,EAAAF,EAAAD,IAAAE,EAAA7B,IAAAR,IAAA0B,UAAA,EACAa,EAAAD,EAAAV,SAAAlC,EAEA,IADA0C,EAAAC,EACAD,EAAAP,SACA,MAAAU,GAAArC,CAEA,MAAAqC,EAAA5C,OAAA,GACA4C,EAAA,IAAAA,CACArC,GAAA,GAAAqC,EAAArC,IASAuB,EAAAe,YAAA,WACA,MAAArG,MAAA6B,MAOAyD,EAAAgB,oBAAA,WACA,MAAAtG,MAAA6B,OAAA,GAOAyD,EAAAiB,WAAA,WACA,MAAAvG,MAAA4B,KAOA0D,EAAAkB,mBAAA,WACA,MAAAxG,MAAA4B,MAAA,GAOA0D,EAAAmB,cAAA,WACA,GAAAzG,KAAA2F,aACA,MAAA3F,MAAA4F,GAAA7C,GAAA,GAAA/C,KAAAiD,MAAAwD,eAEA,QADAjC,GAAA,GAAAxE,KAAA6B,KAAA7B,KAAA6B,KAAA7B,KAAA4B,IACA8E,EAAA,GAAsBA,EAAA,GACtB,IAAAlC,EAAA,GAAAkC,GAD+BA,KAG/B,UAAA1G,KAAA6B,KAAA6E,EAAA,GAAAA,EAAA,GAOApB,EAAAI,OAAA,WACA,WAAA1F,KAAA6B,MAAA,IAAA7B,KAAA4B,KAOA0D,EAAAqB,IAAArB,EAAAI,OAMAJ,EAAAK,WAAA,WACA,OAAA3F,KAAA8B,UAAA9B,KAAA6B,KAAA,GAOAyD,EAAAsB,WAAA,WACA,MAAA5G,MAAA8B,UAAA9B,KAAA6B,MAAA,GAOAyD,EAAAuB,MAAA,WACA,aAAA7G,KAAA4B,MAOA0D,EAAAwB,OAAA,WACA,aAAA9G,KAAA4B,MAQA0D,EAAAyB,OAAA,SAAAC,GAGA,MAFAjF,GAAAiF,KACAA,EAAAzC,EAAAyC,KACAhH,KAAA8B,WAAAkF,EAAAlF,UAAA9B,KAAA6B,OAAA,OAAAmF,EAAAnF,OAAA,SAEA7B,KAAA6B,OAAAmF,EAAAnF,MAAA7B,KAAA4B,MAAAoF,EAAApF,MASA0D,EAAAM,GAAAN,EAAAyB,OAOAzB,EAAA2B,UAAA,SAAAD,GACA,OAAAhH,KAAA4F,GAAAoB,IASA1B,EAAA4B,IAAA5B,EAAA2B,UAQA3B,EAAA6B,GAAA7B,EAAA2B,UAOA3B,EAAA8B,SAAA,SAAAJ,GACA,MAAAhH,MAAAqH,KAAAL,GAAA,GASA1B,EAAAgC,GAAAhC,EAAA8B,SAOA9B,EAAAiC,gBAAA,SAAAP,GACA,MAAAhH,MAAAqH,KAAAL,IAAA,GASA1B,EAAAkC,IAAAlC,EAAAiC,gBAQAjC,EAAAmC,GAAAnC,EAAAiC,gBAOAjC,EAAAoC,YAAA,SAAAV,GACA,MAAAhH,MAAAqH,KAAAL,GAAA,GASA1B,EAAAqC,GAAArC,EAAAoC,YAOApC,EAAAsC,mBAAA,SAAAZ,GACA,MAAAhH,MAAAqH,KAAAL,IAAA,GASA1B,EAAAuC,IAAAvC,EAAAsC,mBAQAtC,EAAAwC,GAAAxC,EAAAsC,mBAQAtC,EAAAyC,QAAA,SAAAf,GAGA,GAFAjF,EAAAiF,KACAA,EAAAzC,EAAAyC,IACAhH,KAAA4F,GAAAoB,GACA,QACA,IAAAgB,GAAAhI,KAAA2F,aACAsC,EAAAjB,EAAArB,YACA,OAAAqC,KAAAC,GACA,GACAD,GAAAC,EACA,EAEAjI,KAAA8B,SAGAkF,EAAAnF,OAAA,EAAA7B,KAAA6B,OAAA,GAAAmF,EAAAnF,OAAA7B,KAAA6B,MAAAmF,EAAApF,MAAA,EAAA5B,KAAA4B,MAAA,OAFA5B,KAAAgG,IAAAgB,GAAArB,cAAA,KAYAL,EAAA+B,KAAA/B,EAAAyC,QAMAzC,EAAA4C,OAAA,WACA,OAAAlI,KAAA8B,UAAA9B,KAAA4F,GAAA7C,GACAA,EACA/C,KAAAmI,MAAA7D,IAAAa,IAQAG,EAAArC,IAAAqC,EAAA4C,OAOA5C,EAAAhB,IAAA,SAAA8D,GACArG,EAAAqG,KACAA,EAAA7D,EAAA6D,GAIA,IAAAC,GAAArI,KAAA6B,OAAA,GACAyG,EAAA,MAAAtI,KAAA6B,KACA0G,EAAAvI,KAAA4B,MAAA,GACA4G,EAAA,MAAAxI,KAAA4B,IAEA6G,EAAAL,EAAAvG,OAAA,GACA6G,EAAA,MAAAN,EAAAvG,KACA8G,EAAAP,EAAAxG,MAAA,GACAgH,EAAA,MAAAR,EAAAxG,IAEAiH,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,CAYA,OAXAA,IAAAR,EAAAI,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAI,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAI,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAI,EACAI,GAAA,MACAvG,EAAAyG,GAAA,GAAAC,EAAAH,GAAA,GAAAC,EAAA9I,KAAA8B,WAQAwD,EAAA2D,SAAA,SAAAC,GAGA,MAFAnH,GAAAmH,KACAA,EAAA3E,EAAA2E,IACAlJ,KAAAsE,IAAA4E,EAAAjG,QASAqC,EAAAU,IAAAV,EAAA2D,SAOA3D,EAAA6D,SAAA,SAAAC,GACA,GAAApJ,KAAA0F,SACA,MAAA/C,EAKA,IAJAZ,EAAAqH,KACAA,EAAA7E,EAAA6E,IAGA3E,EAAA,CAKA,MAAAnC,GAJAmC,EAAAJ,IAAArE,KAAA4B,IACA5B,KAAA6B,KACAuH,EAAAxH,IACAwH,EAAAvH,MACA4C,EAAA4E,WAAArJ,KAAA8B,UAGA,GAAAsH,EAAA1D,SACA,MAAA/C,EACA,IAAA3C,KAAA4F,GAAA7C,GACA,MAAAqG,GAAAvC,QAAA9D,EAAAJ,CACA,IAAAyG,EAAAxD,GAAA7C,GACA,MAAA/C,MAAA6G,QAAA9D,EAAAJ,CAEA,IAAA3C,KAAA2F,aACA,MAAAyD,GAAAzD,aACA3F,KAAAiD,MAAAoB,IAAA+E,EAAAnG,OAEAjD,KAAAiD,MAAAoB,IAAA+E,GAAAnG,KACK,IAAAmG,EAAAzD,aACL,MAAA3F,MAAAqE,IAAA+E,EAAAnG,YAGA,IAAAjD,KAAAsH,GAAApC,IAAAkE,EAAA9B,GAAApC,GACA,MAAA1C,GAAAxC,KAAAwF,WAAA4D,EAAA5D,WAAAxF,KAAA8B,SAKA,IAAAuG,GAAArI,KAAA6B,OAAA,GACAyG,EAAA,MAAAtI,KAAA6B,KACA0G,EAAAvI,KAAA4B,MAAA,GACA4G,EAAA,MAAAxI,KAAA4B,IAEA6G,EAAAW,EAAAvH,OAAA,GACA6G,EAAA,MAAAU,EAAAvH,KACA8G,EAAAS,EAAAxH,MAAA,GACAgH,EAAA,MAAAQ,EAAAxH,IAEAiH,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,CAqBA,OApBAA,IAAAR,EAAAI,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAK,EACAE,GAAAC,IAAA,GACAA,GAAA,MACAA,GAAAP,EAAAG,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAM,EACAC,GAAAC,IAAA,GACAA,GAAA,MACAA,GAAAP,EAAAI,EACAE,GAAAC,IAAA,GACAA,GAAA,MACAA,GAAAN,EAAAE,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAO,EAAAN,EAAAK,EAAAJ,EAAAG,EAAAF,EAAAC,EACAI,GAAA,MACAvG,EAAAyG,GAAA,GAAAC,EAAAH,GAAA,GAAAC,EAAA9I,KAAA8B,WASAwD,EAAAjB,IAAAiB,EAAA6D,SAQA7D,EAAAgE,OAAA,SAAAC,GAGA,GAFAxH,EAAAwH,KACAA,EAAAhF,EAAAgF,IACAA,EAAA7D,SACA,KAAAjC,OAAA,mBAGA,IAAAgB,EAAA,CAIA,IAAAzE,KAAA8B,WACA,aAAA9B,KAAA6B,OACA,IAAA0H,EAAA3H,MAAA,IAAA2H,EAAA1H,KAEA,MAAA7B,KAQA,OAAAsC,IANAtC,KAAA8B,SAAA2C,EAAA+E,MAAA/E,EAAAgF,OACAzJ,KAAA4B,IACA5B,KAAA6B,KACA0H,EAAA3H,IACA2H,EAAA1H,MAEA4C,EAAA4E,WAAArJ,KAAA8B,UAGA,GAAA9B,KAAA0F,SACA,MAAA1F,MAAA8B,SAAAY,EAAAC,CACA,IAAA+G,GAAAzD,EAAA0D,CACA,IAAA3J,KAAA8B,SA6BK,CAKL,GAFAyH,EAAAzH,WACAyH,IAAAK,cACAL,EAAA5B,GAAA3H,MACA,MAAA0C,EACA,IAAA6G,EAAA5B,GAAA3H,KAAA6J,KAAA,IACA,MAAAzE,EACAuE,GAAAjH,MAtCA,CAGA,GAAA1C,KAAA4F,GAAA7C,GAAA,CACA,GAAAwG,EAAA3D,GAAAT,IAAAoE,EAAA3D,GAAAP,GACA,MAAAtC,EACA,IAAAwG,EAAA3D,GAAA7C,GACA,MAAAoC,EAKA,OADAuE,GADA1J,KAAA8J,IAAA,GACAhE,IAAAyD,GAAAQ,IAAA,GACAL,EAAA9D,GAAAjD,GACA4G,EAAA5D,aAAAR,EAAAE,GAEAY,EAAAjG,KAAAgG,IAAAuD,EAAAlF,IAAAqF,IACAC,EAAAD,EAAApF,IAAA2B,EAAAH,IAAAyD,KAIS,GAAAA,EAAA3D,GAAA7C,GACT,MAAA/C,MAAA8B,SAAAY,EAAAC,CACA,IAAA3C,KAAA2F,aACA,MAAA4D,GAAA5D,aACA3F,KAAAiD,MAAA6C,IAAAyD,EAAAtG,OACAjD,KAAAiD,MAAA6C,IAAAyD,GAAAtG,KACS,IAAAsG,EAAA5D,aACT,MAAA3F,MAAA8F,IAAAyD,EAAAtG,YACA0G,GAAAhH,EAmBA,IADAsD,EAAAjG,KACAiG,EAAA4B,IAAA0B,IAAA,CAGAG,EAAAzF,KAAA+F,IAAA,EAAA/F,KAAAgG,MAAAhE,EAAAT,WAAA+D,EAAA/D,YAWA,KAPA,GAAA0E,GAAAjG,KAAAkG,KAAAlG,KAAAmG,IAAAV,GAAAzF,KAAAoG,KACAC,EAAAJ,GAAA,KAAApG,EAAA,EAAAoG,EAAA,IAIAK,EAAA/H,EAAAkH,GACAc,EAAAD,EAAAlG,IAAAkF,GACAiB,EAAA7E,cAAA6E,EAAA7C,GAAA1B,IACAyD,GAAAY,EACAC,EAAA/H,EAAAkH,EAAA1J,KAAA8B,UACA0I,EAAAD,EAAAlG,IAAAkF,EAKAgB,GAAA7E,WACA6E,EAAApF,GAEAwE,IAAArF,IAAAiG,GACAtE,IAAAD,IAAAwE,GAEA,MAAAb,IASArE,EAAAQ,IAAAR,EAAAgE,OAOAhE,EAAAmF,OAAA,SAAAlB,GAKA,GAJAxH,EAAAwH,KACAA,EAAAhF,EAAAgF,IAGA9E,EAAA,CAOA,MAAAnC,IANAtC,KAAA8B,SAAA2C,EAAAiG,MAAAjG,EAAAkG,OACA3K,KAAA4B,IACA5B,KAAA6B,KACA0H,EAAA3H,IACA2H,EAAA1H,MAEA4C,EAAA4E,WAAArJ,KAAA8B,UAGA,MAAA9B,MAAAgG,IAAAhG,KAAA8F,IAAAyD,GAAAlF,IAAAkF,KASAjE,EAAAsF,IAAAtF,EAAAmF,OAQAnF,EAAAW,IAAAX,EAAAmF,OAMAnF,EAAA6C,IAAA,WACA,MAAA7F,IAAAtC,KAAA4B,KAAA5B,KAAA6B,KAAA7B,KAAA8B,WAQAwD,EAAAuF,IAAA,SAAA7D,GAGA,MAFAjF,GAAAiF,KACAA,EAAAzC,EAAAyC,IACA1E,EAAAtC,KAAA4B,IAAAoF,EAAApF,IAAA5B,KAAA6B,KAAAmF,EAAAnF,KAAA7B,KAAA8B,WAQAwD,EAAAwF,GAAA,SAAA9D,GAGA,MAFAjF,GAAAiF,KACAA,EAAAzC,EAAAyC,IACA1E,EAAAtC,KAAA4B,IAAAoF,EAAApF,IAAA5B,KAAA6B,KAAAmF,EAAAnF,KAAA7B,KAAA8B,WAQAwD,EAAAyF,IAAA,SAAA/D,GAGA,MAFAjF,GAAAiF,KACAA,EAAAzC,EAAAyC,IACA1E,EAAAtC,KAAA4B,IAAAoF,EAAApF,IAAA5B,KAAA6B,KAAAmF,EAAAnF,KAAA7B,KAAA8B,WAQAwD,EAAA0F,UAAA,SAAAC,GAGA,MAFAlJ,GAAAkJ,KACAA,IAAA1F,SACA,IAAA0F,GAAA,IACAjL,KACAiL,EAAA,GACA3I,EAAAtC,KAAA4B,KAAAqJ,EAAAjL,KAAA6B,MAAAoJ,EAAAjL,KAAA4B,MAAA,GAAAqJ,EAAAjL,KAAA8B,UAEAQ,EAAA,EAAAtC,KAAA4B,KAAAqJ,EAAA,GAAAjL,KAAA8B,WASAwD,EAAAyE,IAAAzE,EAAA0F,UAOA1F,EAAA4F,WAAA,SAAAD,GAGA,MAFAlJ,GAAAkJ,KACAA,IAAA1F,SACA,IAAA0F,GAAA,IACAjL,KACAiL,EAAA,GACA3I,EAAAtC,KAAA4B,MAAAqJ,EAAAjL,KAAA6B,MAAA,GAAAoJ,EAAAjL,KAAA6B,MAAAoJ,EAAAjL,KAAA8B,UAEAQ,EAAAtC,KAAA6B,MAAAoJ,EAAA,GAAAjL,KAAA6B,MAAA,OAAA7B,KAAA8B,WASAwD,EAAAwE,IAAAxE,EAAA4F,WAOA5F,EAAA6F,mBAAA,SAAAF,GAIA,GAHAlJ,EAAAkJ,KACAA,IAAA1F,SAEA,KADA0F,GAAA,IAEA,MAAAjL,KAEA,IAAA6B,GAAA7B,KAAA6B,IACA,IAAAoJ,EAAA,IAEA,MAAA3I,GADAtC,KAAA4B,MACAqJ,EAAApJ,GAAA,GAAAoJ,EAAApJ,IAAAoJ,EAAAjL,KAAA8B,UACS,YAAAmJ,EACT3I,EAAAT,EAAA,EAAA7B,KAAA8B,UAEAQ,EAAAT,IAAAoJ,EAAA,KAAAjL,KAAA8B,WAUAwD,EAAAuE,KAAAvE,EAAA6F,mBAQA7F,EAAA8F,MAAA9F,EAAA6F,mBAMA7F,EAAA+F,SAAA,WACA,MAAArL,MAAA8B,SAEAQ,EAAAtC,KAAA4B,IAAA5B,KAAA6B,MAAA,GADA7B,MAQAsF,EAAAsE,WAAA,WACA,MAAA5J,MAAA8B,SACA9B,KACAsC,EAAAtC,KAAA4B,IAAA5B,KAAA6B,MAAA,IAQAyD,EAAAgG,QAAA,SAAA7D,GACA,MAAAA,GAAAzH,KAAAuL,YAAAvL,KAAAwL,aAOAlG,EAAAiG,UAAA,WACA,GAAAE,GAAAzL,KAAA6B,KACA6J,EAAA1L,KAAA4B,GACA,QACA,IAAA8J,EACAA,IAAA,MACAA,IAAA,OACAA,IAAA,GACA,IAAAD,EACAA,IAAA,MACAA,IAAA,OACAA,IAAA,KAQAnG,EAAAkG,UAAA,WACA,GAAAC,GAAAzL,KAAA6B,KACA6J,EAAA1L,KAAA4B,GACA,QACA6J,IAAA,GACAA,IAAA,OACAA,IAAA,MACA,IAAAA,EACAC,IAAA,GACAA,IAAA,OACAA,IAAA,MACA,IAAAA,IAWA/J,EAAAgK,UAAA,SAAAC,EAAA9J,EAAA2F,GACA,MAAAA,GAAA9F,EAAAkK,YAAAD,EAAA9J,GAAAH,EAAAmK,YAAAF,EAAA9J,IASAH,EAAAkK,YAAA,SAAAD,EAAA9J,GACA,UAAAH,GACAiK,EAAA,GACAA,EAAA,MACAA,EAAA,OACAA,EAAA,OACAA,EAAA,GACAA,EAAA,MACAA,EAAA,OACAA,EAAA,OACA9J,IAUAH,EAAAmK,YAAA,SAAAF,EAAA9J,GACA,UAAAH,GACAiK,EAAA,OACAA,EAAA,OACAA,EAAA,MACAA,EAAA,GACAA,EAAA,OACAA,EAAA,OACAA,EAAA,MACAA,EAAA,GACA9J", "file": "long.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Long\"] = factory();\n\telse\n\t\troot[\"Long\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Long\"] = factory();\n\telse\n\t\troot[\"Long\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\nmodule.exports = Long;\r\n\r\n/**\r\n * wasm optimizations, to do native i64 multiplication and divide\r\n */\r\nvar wasm = null;\r\n\r\ntry {\r\n  wasm = new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([\r\n    0, 97, 115, 109, 1, 0, 0, 0, 1, 13, 2, 96, 0, 1, 127, 96, 4, 127, 127, 127, 127, 1, 127, 3, 7, 6, 0, 1, 1, 1, 1, 1, 6, 6, 1, 127, 1, 65, 0, 11, 7, 50, 6, 3, 109, 117, 108, 0, 1, 5, 100, 105, 118, 95, 115, 0, 2, 5, 100, 105, 118, 95, 117, 0, 3, 5, 114, 101, 109, 95, 115, 0, 4, 5, 114, 101, 109, 95, 117, 0, 5, 8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0, 10, 191, 1, 6, 4, 0, 35, 0, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11\r\n  ])), {}).exports;\r\n} catch (e) {\r\n  // no wasm support :(\r\n}\r\n\r\n/**\r\n * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\r\n *  See the from* functions below for more convenient ways of constructing Longs.\r\n * @exports Long\r\n * @class A Long class for representing a 64 bit two's-complement integer value.\r\n * @param {number} low The low (signed) 32 bits of the long\r\n * @param {number} high The high (signed) 32 bits of the long\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @constructor\r\n */\r\nfunction Long(low, high, unsigned) {\r\n\r\n    /**\r\n     * The low 32 bits as a signed value.\r\n     * @type {number}\r\n     */\r\n    this.low = low | 0;\r\n\r\n    /**\r\n     * The high 32 bits as a signed value.\r\n     * @type {number}\r\n     */\r\n    this.high = high | 0;\r\n\r\n    /**\r\n     * Whether unsigned or not.\r\n     * @type {boolean}\r\n     */\r\n    this.unsigned = !!unsigned;\r\n}\r\n\r\n// The internal representation of a long is the two given signed, 32-bit values.\r\n// We use 32-bit pieces because these are the size of integers on which\r\n// Javascript performs bit-operations.  For operations like addition and\r\n// multiplication, we split each number into 16 bit pieces, which can easily be\r\n// multiplied within Javascript's floating-point representation without overflow\r\n// or change in sign.\r\n//\r\n// In the algorithms below, we frequently reduce the negative case to the\r\n// positive case by negating the input(s) and then post-processing the result.\r\n// Note that we must ALWAYS check specially whether those values are MIN_VALUE\r\n// (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\r\n// a positive number, it overflows back into a negative).  Not handling this\r\n// case would often result in infinite recursion.\r\n//\r\n// Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\r\n// methods on which they depend.\r\n\r\n/**\r\n * An indicator used to reliably determine if an object is a Long or not.\r\n * @type {boolean}\r\n * @const\r\n * @private\r\n */\r\nLong.prototype.__isLong__;\r\n\r\nObject.defineProperty(Long.prototype, \"__isLong__\", { value: true });\r\n\r\n/**\r\n * @function\r\n * @param {*} obj Object\r\n * @returns {boolean}\r\n * @inner\r\n */\r\nfunction isLong(obj) {\r\n    return (obj && obj[\"__isLong__\"]) === true;\r\n}\r\n\r\n/**\r\n * Tests if the specified object is a Long.\r\n * @function\r\n * @param {*} obj Object\r\n * @returns {boolean}\r\n */\r\nLong.isLong = isLong;\r\n\r\n/**\r\n * A cache of the Long representations of small integer values.\r\n * @type {!Object}\r\n * @inner\r\n */\r\nvar INT_CACHE = {};\r\n\r\n/**\r\n * A cache of the Long representations of small unsigned integer values.\r\n * @type {!Object}\r\n * @inner\r\n */\r\nvar UINT_CACHE = {};\r\n\r\n/**\r\n * @param {number} value\r\n * @param {boolean=} unsigned\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromInt(value, unsigned) {\r\n    var obj, cachedObj, cache;\r\n    if (unsigned) {\r\n        value >>>= 0;\r\n        if (cache = (0 <= value && value < 256)) {\r\n            cachedObj = UINT_CACHE[value];\r\n            if (cachedObj)\r\n                return cachedObj;\r\n        }\r\n        obj = fromBits(value, (value | 0) < 0 ? -1 : 0, true);\r\n        if (cache)\r\n            UINT_CACHE[value] = obj;\r\n        return obj;\r\n    } else {\r\n        value |= 0;\r\n        if (cache = (-128 <= value && value < 128)) {\r\n            cachedObj = INT_CACHE[value];\r\n            if (cachedObj)\r\n                return cachedObj;\r\n        }\r\n        obj = fromBits(value, value < 0 ? -1 : 0, false);\r\n        if (cache)\r\n            INT_CACHE[value] = obj;\r\n        return obj;\r\n    }\r\n}\r\n\r\n/**\r\n * Returns a Long representing the given 32 bit integer value.\r\n * @function\r\n * @param {number} value The 32 bit integer in question\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {!Long} The corresponding Long value\r\n */\r\nLong.fromInt = fromInt;\r\n\r\n/**\r\n * @param {number} value\r\n * @param {boolean=} unsigned\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromNumber(value, unsigned) {\r\n    if (isNaN(value))\r\n        return unsigned ? UZERO : ZERO;\r\n    if (unsigned) {\r\n        if (value < 0)\r\n            return UZERO;\r\n        if (value >= TWO_PWR_64_DBL)\r\n            return MAX_UNSIGNED_VALUE;\r\n    } else {\r\n        if (value <= -TWO_PWR_63_DBL)\r\n            return MIN_VALUE;\r\n        if (value + 1 >= TWO_PWR_63_DBL)\r\n            return MAX_VALUE;\r\n    }\r\n    if (value < 0)\r\n        return fromNumber(-value, unsigned).neg();\r\n    return fromBits((value % TWO_PWR_32_DBL) | 0, (value / TWO_PWR_32_DBL) | 0, unsigned);\r\n}\r\n\r\n/**\r\n * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\r\n * @function\r\n * @param {number} value The number in question\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {!Long} The corresponding Long value\r\n */\r\nLong.fromNumber = fromNumber;\r\n\r\n/**\r\n * @param {number} lowBits\r\n * @param {number} highBits\r\n * @param {boolean=} unsigned\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromBits(lowBits, highBits, unsigned) {\r\n    return new Long(lowBits, highBits, unsigned);\r\n}\r\n\r\n/**\r\n * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\r\n *  assumed to use 32 bits.\r\n * @function\r\n * @param {number} lowBits The low 32 bits\r\n * @param {number} highBits The high 32 bits\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {!Long} The corresponding Long value\r\n */\r\nLong.fromBits = fromBits;\r\n\r\n/**\r\n * @function\r\n * @param {number} base\r\n * @param {number} exponent\r\n * @returns {number}\r\n * @inner\r\n */\r\nvar pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\r\n\r\n/**\r\n * @param {string} str\r\n * @param {(boolean|number)=} unsigned\r\n * @param {number=} radix\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromString(str, unsigned, radix) {\r\n    if (str.length === 0)\r\n        throw Error('empty string');\r\n    if (str === \"NaN\" || str === \"Infinity\" || str === \"+Infinity\" || str === \"-Infinity\")\r\n        return ZERO;\r\n    if (typeof unsigned === 'number') {\r\n        // For goog.math.long compatibility\r\n        radix = unsigned,\r\n        unsigned = false;\r\n    } else {\r\n        unsigned = !! unsigned;\r\n    }\r\n    radix = radix || 10;\r\n    if (radix < 2 || 36 < radix)\r\n        throw RangeError('radix');\r\n\r\n    var p;\r\n    if ((p = str.indexOf('-')) > 0)\r\n        throw Error('interior hyphen');\r\n    else if (p === 0) {\r\n        return fromString(str.substring(1), unsigned, radix).neg();\r\n    }\r\n\r\n    // Do several (8) digits each time through the loop, so as to\r\n    // minimize the calls to the very expensive emulated div.\r\n    var radixToPower = fromNumber(pow_dbl(radix, 8));\r\n\r\n    var result = ZERO;\r\n    for (var i = 0; i < str.length; i += 8) {\r\n        var size = Math.min(8, str.length - i),\r\n            value = parseInt(str.substring(i, i + size), radix);\r\n        if (size < 8) {\r\n            var power = fromNumber(pow_dbl(radix, size));\r\n            result = result.mul(power).add(fromNumber(value));\r\n        } else {\r\n            result = result.mul(radixToPower);\r\n            result = result.add(fromNumber(value));\r\n        }\r\n    }\r\n    result.unsigned = unsigned;\r\n    return result;\r\n}\r\n\r\n/**\r\n * Returns a Long representation of the given string, written using the specified radix.\r\n * @function\r\n * @param {string} str The textual representation of the Long\r\n * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\r\n * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\r\n * @returns {!Long} The corresponding Long value\r\n */\r\nLong.fromString = fromString;\r\n\r\n/**\r\n * @function\r\n * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\r\n * @param {boolean=} unsigned\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromValue(val, unsigned) {\r\n    if (typeof val === 'number')\r\n        return fromNumber(val, unsigned);\r\n    if (typeof val === 'string')\r\n        return fromString(val, unsigned);\r\n    // Throws for non-objects, converts non-instanceof Long:\r\n    return fromBits(val.low, val.high, typeof unsigned === 'boolean' ? unsigned : val.unsigned);\r\n}\r\n\r\n/**\r\n * Converts the specified value to a Long using the appropriate from* function for its type.\r\n * @function\r\n * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val Value\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {!Long}\r\n */\r\nLong.fromValue = fromValue;\r\n\r\n// NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\r\n// no runtime penalty for these.\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_16_DBL = 1 << 16;\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_24_DBL = 1 << 24;\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\r\n\r\n/**\r\n * @type {!Long}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar ZERO = fromInt(0);\r\n\r\n/**\r\n * Signed zero.\r\n * @type {!Long}\r\n */\r\nLong.ZERO = ZERO;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar UZERO = fromInt(0, true);\r\n\r\n/**\r\n * Unsigned zero.\r\n * @type {!Long}\r\n */\r\nLong.UZERO = UZERO;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar ONE = fromInt(1);\r\n\r\n/**\r\n * Signed one.\r\n * @type {!Long}\r\n */\r\nLong.ONE = ONE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar UONE = fromInt(1, true);\r\n\r\n/**\r\n * Unsigned one.\r\n * @type {!Long}\r\n */\r\nLong.UONE = UONE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar NEG_ONE = fromInt(-1);\r\n\r\n/**\r\n * Signed negative one.\r\n * @type {!Long}\r\n */\r\nLong.NEG_ONE = NEG_ONE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar MAX_VALUE = fromBits(0xFFFFFFFF|0, 0x7FFFFFFF|0, false);\r\n\r\n/**\r\n * Maximum signed value.\r\n * @type {!Long}\r\n */\r\nLong.MAX_VALUE = MAX_VALUE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar MAX_UNSIGNED_VALUE = fromBits(0xFFFFFFFF|0, 0xFFFFFFFF|0, true);\r\n\r\n/**\r\n * Maximum unsigned value.\r\n * @type {!Long}\r\n */\r\nLong.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar MIN_VALUE = fromBits(0, 0x80000000|0, false);\r\n\r\n/**\r\n * Minimum signed value.\r\n * @type {!Long}\r\n */\r\nLong.MIN_VALUE = MIN_VALUE;\r\n\r\n/**\r\n * @alias Long.prototype\r\n * @inner\r\n */\r\nvar LongPrototype = Long.prototype;\r\n\r\n/**\r\n * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\r\n * @returns {number}\r\n */\r\nLongPrototype.toInt = function toInt() {\r\n    return this.unsigned ? this.low >>> 0 : this.low;\r\n};\r\n\r\n/**\r\n * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\r\n * @returns {number}\r\n */\r\nLongPrototype.toNumber = function toNumber() {\r\n    if (this.unsigned)\r\n        return ((this.high >>> 0) * TWO_PWR_32_DBL) + (this.low >>> 0);\r\n    return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\r\n};\r\n\r\n/**\r\n * Converts the Long to a string written in the specified radix.\r\n * @param {number=} radix Radix (2-36), defaults to 10\r\n * @returns {string}\r\n * @override\r\n * @throws {RangeError} If `radix` is out of range\r\n */\r\nLongPrototype.toString = function toString(radix) {\r\n    radix = radix || 10;\r\n    if (radix < 2 || 36 < radix)\r\n        throw RangeError('radix');\r\n    if (this.isZero())\r\n        return '0';\r\n    if (this.isNegative()) { // Unsigned Longs are never negative\r\n        if (this.eq(MIN_VALUE)) {\r\n            // We need to change the Long value before it can be negated, so we remove\r\n            // the bottom-most digit in this base and then recurse to do the rest.\r\n            var radixLong = fromNumber(radix),\r\n                div = this.div(radixLong),\r\n                rem1 = div.mul(radixLong).sub(this);\r\n            return div.toString(radix) + rem1.toInt().toString(radix);\r\n        } else\r\n            return '-' + this.neg().toString(radix);\r\n    }\r\n\r\n    // Do several (6) digits each time through the loop, so as to\r\n    // minimize the calls to the very expensive emulated div.\r\n    var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\r\n        rem = this;\r\n    var result = '';\r\n    while (true) {\r\n        var remDiv = rem.div(radixToPower),\r\n            intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\r\n            digits = intval.toString(radix);\r\n        rem = remDiv;\r\n        if (rem.isZero())\r\n            return digits + result;\r\n        else {\r\n            while (digits.length < 6)\r\n                digits = '0' + digits;\r\n            result = '' + digits + result;\r\n        }\r\n    }\r\n};\r\n\r\n/**\r\n * Gets the high 32 bits as a signed integer.\r\n * @returns {number} Signed high bits\r\n */\r\nLongPrototype.getHighBits = function getHighBits() {\r\n    return this.high;\r\n};\r\n\r\n/**\r\n * Gets the high 32 bits as an unsigned integer.\r\n * @returns {number} Unsigned high bits\r\n */\r\nLongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\r\n    return this.high >>> 0;\r\n};\r\n\r\n/**\r\n * Gets the low 32 bits as a signed integer.\r\n * @returns {number} Signed low bits\r\n */\r\nLongPrototype.getLowBits = function getLowBits() {\r\n    return this.low;\r\n};\r\n\r\n/**\r\n * Gets the low 32 bits as an unsigned integer.\r\n * @returns {number} Unsigned low bits\r\n */\r\nLongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\r\n    return this.low >>> 0;\r\n};\r\n\r\n/**\r\n * Gets the number of bits needed to represent the absolute value of this Long.\r\n * @returns {number}\r\n */\r\nLongPrototype.getNumBitsAbs = function getNumBitsAbs() {\r\n    if (this.isNegative()) // Unsigned Longs are never negative\r\n        return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\r\n    var val = this.high != 0 ? this.high : this.low;\r\n    for (var bit = 31; bit > 0; bit--)\r\n        if ((val & (1 << bit)) != 0)\r\n            break;\r\n    return this.high != 0 ? bit + 33 : bit + 1;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value equals zero.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isZero = function isZero() {\r\n    return this.high === 0 && this.low === 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.eqz = LongPrototype.isZero;\r\n\r\n/**\r\n * Tests if this Long's value is negative.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isNegative = function isNegative() {\r\n    return !this.unsigned && this.high < 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is positive.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isPositive = function isPositive() {\r\n    return this.unsigned || this.high >= 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is odd.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isOdd = function isOdd() {\r\n    return (this.low & 1) === 1;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is even.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isEven = function isEven() {\r\n    return (this.low & 1) === 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value equals the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.equals = function equals(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    if (this.unsigned !== other.unsigned && (this.high >>> 31) === 1 && (other.high >>> 31) === 1)\r\n        return false;\r\n    return this.high === other.high && this.low === other.low;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.eq = LongPrototype.equals;\r\n\r\n/**\r\n * Tests if this Long's value differs from the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.notEquals = function notEquals(other) {\r\n    return !this.eq(/* validates */ other);\r\n};\r\n\r\n/**\r\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.neq = LongPrototype.notEquals;\r\n\r\n/**\r\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.ne = LongPrototype.notEquals;\r\n\r\n/**\r\n * Tests if this Long's value is less than the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.lessThan = function lessThan(other) {\r\n    return this.comp(/* validates */ other) < 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.lt = LongPrototype.lessThan;\r\n\r\n/**\r\n * Tests if this Long's value is less than or equal the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\r\n    return this.comp(/* validates */ other) <= 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.lte = LongPrototype.lessThanOrEqual;\r\n\r\n/**\r\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.le = LongPrototype.lessThanOrEqual;\r\n\r\n/**\r\n * Tests if this Long's value is greater than the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.greaterThan = function greaterThan(other) {\r\n    return this.comp(/* validates */ other) > 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.gt = LongPrototype.greaterThan;\r\n\r\n/**\r\n * Tests if this Long's value is greater than or equal the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\r\n    return this.comp(/* validates */ other) >= 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.gte = LongPrototype.greaterThanOrEqual;\r\n\r\n/**\r\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.ge = LongPrototype.greaterThanOrEqual;\r\n\r\n/**\r\n * Compares this Long's value with the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\r\n *  if the given one is greater\r\n */\r\nLongPrototype.compare = function compare(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    if (this.eq(other))\r\n        return 0;\r\n    var thisNeg = this.isNegative(),\r\n        otherNeg = other.isNegative();\r\n    if (thisNeg && !otherNeg)\r\n        return -1;\r\n    if (!thisNeg && otherNeg)\r\n        return 1;\r\n    // At this point the sign bits are the same\r\n    if (!this.unsigned)\r\n        return this.sub(other).isNegative() ? -1 : 1;\r\n    // Both are positive if at least one is unsigned\r\n    return (other.high >>> 0) > (this.high >>> 0) || (other.high === this.high && (other.low >>> 0) > (this.low >>> 0)) ? -1 : 1;\r\n};\r\n\r\n/**\r\n * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\r\n *  if the given one is greater\r\n */\r\nLongPrototype.comp = LongPrototype.compare;\r\n\r\n/**\r\n * Negates this Long's value.\r\n * @returns {!Long} Negated Long\r\n */\r\nLongPrototype.negate = function negate() {\r\n    if (!this.unsigned && this.eq(MIN_VALUE))\r\n        return MIN_VALUE;\r\n    return this.not().add(ONE);\r\n};\r\n\r\n/**\r\n * Negates this Long's value. This is an alias of {@link Long#negate}.\r\n * @function\r\n * @returns {!Long} Negated Long\r\n */\r\nLongPrototype.neg = LongPrototype.negate;\r\n\r\n/**\r\n * Returns the sum of this and the specified Long.\r\n * @param {!Long|number|string} addend Addend\r\n * @returns {!Long} Sum\r\n */\r\nLongPrototype.add = function add(addend) {\r\n    if (!isLong(addend))\r\n        addend = fromValue(addend);\r\n\r\n    // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\r\n\r\n    var a48 = this.high >>> 16;\r\n    var a32 = this.high & 0xFFFF;\r\n    var a16 = this.low >>> 16;\r\n    var a00 = this.low & 0xFFFF;\r\n\r\n    var b48 = addend.high >>> 16;\r\n    var b32 = addend.high & 0xFFFF;\r\n    var b16 = addend.low >>> 16;\r\n    var b00 = addend.low & 0xFFFF;\r\n\r\n    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\r\n    c00 += a00 + b00;\r\n    c16 += c00 >>> 16;\r\n    c00 &= 0xFFFF;\r\n    c16 += a16 + b16;\r\n    c32 += c16 >>> 16;\r\n    c16 &= 0xFFFF;\r\n    c32 += a32 + b32;\r\n    c48 += c32 >>> 16;\r\n    c32 &= 0xFFFF;\r\n    c48 += a48 + b48;\r\n    c48 &= 0xFFFF;\r\n    return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the difference of this and the specified Long.\r\n * @param {!Long|number|string} subtrahend Subtrahend\r\n * @returns {!Long} Difference\r\n */\r\nLongPrototype.subtract = function subtract(subtrahend) {\r\n    if (!isLong(subtrahend))\r\n        subtrahend = fromValue(subtrahend);\r\n    return this.add(subtrahend.neg());\r\n};\r\n\r\n/**\r\n * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\r\n * @function\r\n * @param {!Long|number|string} subtrahend Subtrahend\r\n * @returns {!Long} Difference\r\n */\r\nLongPrototype.sub = LongPrototype.subtract;\r\n\r\n/**\r\n * Returns the product of this and the specified Long.\r\n * @param {!Long|number|string} multiplier Multiplier\r\n * @returns {!Long} Product\r\n */\r\nLongPrototype.multiply = function multiply(multiplier) {\r\n    if (this.isZero())\r\n        return ZERO;\r\n    if (!isLong(multiplier))\r\n        multiplier = fromValue(multiplier);\r\n\r\n    // use wasm support if present\r\n    if (wasm) {\r\n        var low = wasm.mul(this.low,\r\n                           this.high,\r\n                           multiplier.low,\r\n                           multiplier.high);\r\n        return fromBits(low, wasm.get_high(), this.unsigned);\r\n    }\r\n\r\n    if (multiplier.isZero())\r\n        return ZERO;\r\n    if (this.eq(MIN_VALUE))\r\n        return multiplier.isOdd() ? MIN_VALUE : ZERO;\r\n    if (multiplier.eq(MIN_VALUE))\r\n        return this.isOdd() ? MIN_VALUE : ZERO;\r\n\r\n    if (this.isNegative()) {\r\n        if (multiplier.isNegative())\r\n            return this.neg().mul(multiplier.neg());\r\n        else\r\n            return this.neg().mul(multiplier).neg();\r\n    } else if (multiplier.isNegative())\r\n        return this.mul(multiplier.neg()).neg();\r\n\r\n    // If both longs are small, use float multiplication\r\n    if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24))\r\n        return fromNumber(this.toNumber() * multiplier.toNumber(), this.unsigned);\r\n\r\n    // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\r\n    // We can skip products that would overflow.\r\n\r\n    var a48 = this.high >>> 16;\r\n    var a32 = this.high & 0xFFFF;\r\n    var a16 = this.low >>> 16;\r\n    var a00 = this.low & 0xFFFF;\r\n\r\n    var b48 = multiplier.high >>> 16;\r\n    var b32 = multiplier.high & 0xFFFF;\r\n    var b16 = multiplier.low >>> 16;\r\n    var b00 = multiplier.low & 0xFFFF;\r\n\r\n    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\r\n    c00 += a00 * b00;\r\n    c16 += c00 >>> 16;\r\n    c00 &= 0xFFFF;\r\n    c16 += a16 * b00;\r\n    c32 += c16 >>> 16;\r\n    c16 &= 0xFFFF;\r\n    c16 += a00 * b16;\r\n    c32 += c16 >>> 16;\r\n    c16 &= 0xFFFF;\r\n    c32 += a32 * b00;\r\n    c48 += c32 >>> 16;\r\n    c32 &= 0xFFFF;\r\n    c32 += a16 * b16;\r\n    c48 += c32 >>> 16;\r\n    c32 &= 0xFFFF;\r\n    c32 += a00 * b32;\r\n    c48 += c32 >>> 16;\r\n    c32 &= 0xFFFF;\r\n    c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\r\n    c48 &= 0xFFFF;\r\n    return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\r\n * @function\r\n * @param {!Long|number|string} multiplier Multiplier\r\n * @returns {!Long} Product\r\n */\r\nLongPrototype.mul = LongPrototype.multiply;\r\n\r\n/**\r\n * Returns this Long divided by the specified. The result is signed if this Long is signed or\r\n *  unsigned if this Long is unsigned.\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Quotient\r\n */\r\nLongPrototype.divide = function divide(divisor) {\r\n    if (!isLong(divisor))\r\n        divisor = fromValue(divisor);\r\n    if (divisor.isZero())\r\n        throw Error('division by zero');\r\n\r\n    // use wasm support if present\r\n    if (wasm) {\r\n        // guard against signed division overflow: the largest\r\n        // negative number / -1 would be 1 larger than the largest\r\n        // positive number, due to two's complement.\r\n        if (!this.unsigned &&\r\n            this.high === -0x80000000 &&\r\n            divisor.low === -1 && divisor.high === -1) {\r\n            // be consistent with non-wasm code path\r\n            return this;\r\n        }\r\n        var low = (this.unsigned ? wasm.div_u : wasm.div_s)(\r\n            this.low,\r\n            this.high,\r\n            divisor.low,\r\n            divisor.high\r\n        );\r\n        return fromBits(low, wasm.get_high(), this.unsigned);\r\n    }\r\n\r\n    if (this.isZero())\r\n        return this.unsigned ? UZERO : ZERO;\r\n    var approx, rem, res;\r\n    if (!this.unsigned) {\r\n        // This section is only relevant for signed longs and is derived from the\r\n        // closure library as a whole.\r\n        if (this.eq(MIN_VALUE)) {\r\n            if (divisor.eq(ONE) || divisor.eq(NEG_ONE))\r\n                return MIN_VALUE;  // recall that -MIN_VALUE == MIN_VALUE\r\n            else if (divisor.eq(MIN_VALUE))\r\n                return ONE;\r\n            else {\r\n                // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\r\n                var halfThis = this.shr(1);\r\n                approx = halfThis.div(divisor).shl(1);\r\n                if (approx.eq(ZERO)) {\r\n                    return divisor.isNegative() ? ONE : NEG_ONE;\r\n                } else {\r\n                    rem = this.sub(divisor.mul(approx));\r\n                    res = approx.add(rem.div(divisor));\r\n                    return res;\r\n                }\r\n            }\r\n        } else if (divisor.eq(MIN_VALUE))\r\n            return this.unsigned ? UZERO : ZERO;\r\n        if (this.isNegative()) {\r\n            if (divisor.isNegative())\r\n                return this.neg().div(divisor.neg());\r\n            return this.neg().div(divisor).neg();\r\n        } else if (divisor.isNegative())\r\n            return this.div(divisor.neg()).neg();\r\n        res = ZERO;\r\n    } else {\r\n        // The algorithm below has not been made for unsigned longs. It's therefore\r\n        // required to take special care of the MSB prior to running it.\r\n        if (!divisor.unsigned)\r\n            divisor = divisor.toUnsigned();\r\n        if (divisor.gt(this))\r\n            return UZERO;\r\n        if (divisor.gt(this.shru(1))) // 15 >>> 1 = 7 ; with divisor = 8 ; true\r\n            return UONE;\r\n        res = UZERO;\r\n    }\r\n\r\n    // Repeat the following until the remainder is less than other:  find a\r\n    // floating-point that approximates remainder / other *from below*, add this\r\n    // into the result, and subtract it from the remainder.  It is critical that\r\n    // the approximate value is less than or equal to the real value so that the\r\n    // remainder never becomes negative.\r\n    rem = this;\r\n    while (rem.gte(divisor)) {\r\n        // Approximate the result of division. This may be a little greater or\r\n        // smaller than the actual value.\r\n        approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\r\n\r\n        // We will tweak the approximate result by changing it in the 48-th digit or\r\n        // the smallest non-fractional digit, whichever is larger.\r\n        var log2 = Math.ceil(Math.log(approx) / Math.LN2),\r\n            delta = (log2 <= 48) ? 1 : pow_dbl(2, log2 - 48),\r\n\r\n        // Decrease the approximation until it is smaller than the remainder.  Note\r\n        // that if it is too large, the product overflows and is negative.\r\n            approxRes = fromNumber(approx),\r\n            approxRem = approxRes.mul(divisor);\r\n        while (approxRem.isNegative() || approxRem.gt(rem)) {\r\n            approx -= delta;\r\n            approxRes = fromNumber(approx, this.unsigned);\r\n            approxRem = approxRes.mul(divisor);\r\n        }\r\n\r\n        // We know the answer can't be zero... and actually, zero would cause\r\n        // infinite recursion since we would make no progress.\r\n        if (approxRes.isZero())\r\n            approxRes = ONE;\r\n\r\n        res = res.add(approxRes);\r\n        rem = rem.sub(approxRem);\r\n    }\r\n    return res;\r\n};\r\n\r\n/**\r\n * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\r\n * @function\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Quotient\r\n */\r\nLongPrototype.div = LongPrototype.divide;\r\n\r\n/**\r\n * Returns this Long modulo the specified.\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Remainder\r\n */\r\nLongPrototype.modulo = function modulo(divisor) {\r\n    if (!isLong(divisor))\r\n        divisor = fromValue(divisor);\r\n\r\n    // use wasm support if present\r\n    if (wasm) {\r\n        var low = (this.unsigned ? wasm.rem_u : wasm.rem_s)(\r\n            this.low,\r\n            this.high,\r\n            divisor.low,\r\n            divisor.high\r\n        );\r\n        return fromBits(low, wasm.get_high(), this.unsigned);\r\n    }\r\n\r\n    return this.sub(this.div(divisor).mul(divisor));\r\n};\r\n\r\n/**\r\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\r\n * @function\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Remainder\r\n */\r\nLongPrototype.mod = LongPrototype.modulo;\r\n\r\n/**\r\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\r\n * @function\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Remainder\r\n */\r\nLongPrototype.rem = LongPrototype.modulo;\r\n\r\n/**\r\n * Returns the bitwise NOT of this Long.\r\n * @returns {!Long}\r\n */\r\nLongPrototype.not = function not() {\r\n    return fromBits(~this.low, ~this.high, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the bitwise AND of this Long and the specified.\r\n * @param {!Long|number|string} other Other Long\r\n * @returns {!Long}\r\n */\r\nLongPrototype.and = function and(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    return fromBits(this.low & other.low, this.high & other.high, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the bitwise OR of this Long and the specified.\r\n * @param {!Long|number|string} other Other Long\r\n * @returns {!Long}\r\n */\r\nLongPrototype.or = function or(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    return fromBits(this.low | other.low, this.high | other.high, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the bitwise XOR of this Long and the given one.\r\n * @param {!Long|number|string} other Other Long\r\n * @returns {!Long}\r\n */\r\nLongPrototype.xor = function xor(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    return fromBits(this.low ^ other.low, this.high ^ other.high, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns this Long with bits shifted to the left by the given amount.\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shiftLeft = function shiftLeft(numBits) {\r\n    if (isLong(numBits))\r\n        numBits = numBits.toInt();\r\n    if ((numBits &= 63) === 0)\r\n        return this;\r\n    else if (numBits < 32)\r\n        return fromBits(this.low << numBits, (this.high << numBits) | (this.low >>> (32 - numBits)), this.unsigned);\r\n    else\r\n        return fromBits(0, this.low << (numBits - 32), this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\r\n * @function\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shl = LongPrototype.shiftLeft;\r\n\r\n/**\r\n * Returns this Long with bits arithmetically shifted to the right by the given amount.\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shiftRight = function shiftRight(numBits) {\r\n    if (isLong(numBits))\r\n        numBits = numBits.toInt();\r\n    if ((numBits &= 63) === 0)\r\n        return this;\r\n    else if (numBits < 32)\r\n        return fromBits((this.low >>> numBits) | (this.high << (32 - numBits)), this.high >> numBits, this.unsigned);\r\n    else\r\n        return fromBits(this.high >> (numBits - 32), this.high >= 0 ? 0 : -1, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\r\n * @function\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shr = LongPrototype.shiftRight;\r\n\r\n/**\r\n * Returns this Long with bits logically shifted to the right by the given amount.\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\r\n    if (isLong(numBits))\r\n        numBits = numBits.toInt();\r\n    numBits &= 63;\r\n    if (numBits === 0)\r\n        return this;\r\n    else {\r\n        var high = this.high;\r\n        if (numBits < 32) {\r\n            var low = this.low;\r\n            return fromBits((low >>> numBits) | (high << (32 - numBits)), high >>> numBits, this.unsigned);\r\n        } else if (numBits === 32)\r\n            return fromBits(high, 0, this.unsigned);\r\n        else\r\n            return fromBits(high >>> (numBits - 32), 0, this.unsigned);\r\n    }\r\n};\r\n\r\n/**\r\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\r\n * @function\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shru = LongPrototype.shiftRightUnsigned;\r\n\r\n/**\r\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\r\n * @function\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\r\n\r\n/**\r\n * Converts this Long to signed.\r\n * @returns {!Long} Signed long\r\n */\r\nLongPrototype.toSigned = function toSigned() {\r\n    if (!this.unsigned)\r\n        return this;\r\n    return fromBits(this.low, this.high, false);\r\n};\r\n\r\n/**\r\n * Converts this Long to unsigned.\r\n * @returns {!Long} Unsigned long\r\n */\r\nLongPrototype.toUnsigned = function toUnsigned() {\r\n    if (this.unsigned)\r\n        return this;\r\n    return fromBits(this.low, this.high, true);\r\n};\r\n\r\n/**\r\n * Converts this Long to its byte representation.\r\n * @param {boolean=} le Whether little or big endian, defaults to big endian\r\n * @returns {!Array.<number>} Byte representation\r\n */\r\nLongPrototype.toBytes = function toBytes(le) {\r\n    return le ? this.toBytesLE() : this.toBytesBE();\r\n};\r\n\r\n/**\r\n * Converts this Long to its little endian byte representation.\r\n * @returns {!Array.<number>} Little endian byte representation\r\n */\r\nLongPrototype.toBytesLE = function toBytesLE() {\r\n    var hi = this.high,\r\n        lo = this.low;\r\n    return [\r\n        lo        & 0xff,\r\n        lo >>>  8 & 0xff,\r\n        lo >>> 16 & 0xff,\r\n        lo >>> 24       ,\r\n        hi        & 0xff,\r\n        hi >>>  8 & 0xff,\r\n        hi >>> 16 & 0xff,\r\n        hi >>> 24\r\n    ];\r\n};\r\n\r\n/**\r\n * Converts this Long to its big endian byte representation.\r\n * @returns {!Array.<number>} Big endian byte representation\r\n */\r\nLongPrototype.toBytesBE = function toBytesBE() {\r\n    var hi = this.high,\r\n        lo = this.low;\r\n    return [\r\n        hi >>> 24       ,\r\n        hi >>> 16 & 0xff,\r\n        hi >>>  8 & 0xff,\r\n        hi        & 0xff,\r\n        lo >>> 24       ,\r\n        lo >>> 16 & 0xff,\r\n        lo >>>  8 & 0xff,\r\n        lo        & 0xff\r\n    ];\r\n};\r\n\r\n/**\r\n * Creates a Long from its byte representation.\r\n * @param {!Array.<number>} bytes Byte representation\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @param {boolean=} le Whether little or big endian, defaults to big endian\r\n * @returns {Long} The corresponding Long value\r\n */\r\nLong.fromBytes = function fromBytes(bytes, unsigned, le) {\r\n    return le ? Long.fromBytesLE(bytes, unsigned) : Long.fromBytesBE(bytes, unsigned);\r\n};\r\n\r\n/**\r\n * Creates a Long from its little endian byte representation.\r\n * @param {!Array.<number>} bytes Little endian byte representation\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {Long} The corresponding Long value\r\n */\r\nLong.fromBytesLE = function fromBytesLE(bytes, unsigned) {\r\n    return new Long(\r\n        bytes[0]       |\r\n        bytes[1] <<  8 |\r\n        bytes[2] << 16 |\r\n        bytes[3] << 24,\r\n        bytes[4]       |\r\n        bytes[5] <<  8 |\r\n        bytes[6] << 16 |\r\n        bytes[7] << 24,\r\n        unsigned\r\n    );\r\n};\r\n\r\n/**\r\n * Creates a Long from its big endian byte representation.\r\n * @param {!Array.<number>} bytes Big endian byte representation\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {Long} The corresponding Long value\r\n */\r\nLong.fromBytesBE = function fromBytesBE(bytes, unsigned) {\r\n    return new Long(\r\n        bytes[4] << 24 |\r\n        bytes[5] << 16 |\r\n        bytes[6] <<  8 |\r\n        bytes[7],\r\n        bytes[0] << 24 |\r\n        bytes[1] << 16 |\r\n        bytes[2] <<  8 |\r\n        bytes[3],\r\n        unsigned\r\n    );\r\n};\r\n\n\n/***/ })\n/******/ ]);\n});\n\n\n// WEBPACK FOOTER //\n// long.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap d8921b8c3ad0790b3cc1", "module.exports = Long;\r\n\r\n/**\r\n * wasm optimizations, to do native i64 multiplication and divide\r\n */\r\nvar wasm = null;\r\n\r\ntry {\r\n  wasm = new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([\r\n    0, 97, 115, 109, 1, 0, 0, 0, 1, 13, 2, 96, 0, 1, 127, 96, 4, 127, 127, 127, 127, 1, 127, 3, 7, 6, 0, 1, 1, 1, 1, 1, 6, 6, 1, 127, 1, 65, 0, 11, 7, 50, 6, 3, 109, 117, 108, 0, 1, 5, 100, 105, 118, 95, 115, 0, 2, 5, 100, 105, 118, 95, 117, 0, 3, 5, 114, 101, 109, 95, 115, 0, 4, 5, 114, 101, 109, 95, 117, 0, 5, 8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0, 10, 191, 1, 6, 4, 0, 35, 0, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11\r\n  ])), {}).exports;\r\n} catch (e) {\r\n  // no wasm support :(\r\n}\r\n\r\n/**\r\n * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\r\n *  See the from* functions below for more convenient ways of constructing Longs.\r\n * @exports Long\r\n * @class A Long class for representing a 64 bit two's-complement integer value.\r\n * @param {number} low The low (signed) 32 bits of the long\r\n * @param {number} high The high (signed) 32 bits of the long\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @constructor\r\n */\r\nfunction Long(low, high, unsigned) {\r\n\r\n    /**\r\n     * The low 32 bits as a signed value.\r\n     * @type {number}\r\n     */\r\n    this.low = low | 0;\r\n\r\n    /**\r\n     * The high 32 bits as a signed value.\r\n     * @type {number}\r\n     */\r\n    this.high = high | 0;\r\n\r\n    /**\r\n     * Whether unsigned or not.\r\n     * @type {boolean}\r\n     */\r\n    this.unsigned = !!unsigned;\r\n}\r\n\r\n// The internal representation of a long is the two given signed, 32-bit values.\r\n// We use 32-bit pieces because these are the size of integers on which\r\n// Javascript performs bit-operations.  For operations like addition and\r\n// multiplication, we split each number into 16 bit pieces, which can easily be\r\n// multiplied within Javascript's floating-point representation without overflow\r\n// or change in sign.\r\n//\r\n// In the algorithms below, we frequently reduce the negative case to the\r\n// positive case by negating the input(s) and then post-processing the result.\r\n// Note that we must ALWAYS check specially whether those values are MIN_VALUE\r\n// (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\r\n// a positive number, it overflows back into a negative).  Not handling this\r\n// case would often result in infinite recursion.\r\n//\r\n// Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\r\n// methods on which they depend.\r\n\r\n/**\r\n * An indicator used to reliably determine if an object is a Long or not.\r\n * @type {boolean}\r\n * @const\r\n * @private\r\n */\r\nLong.prototype.__isLong__;\r\n\r\nObject.defineProperty(Long.prototype, \"__isLong__\", { value: true });\r\n\r\n/**\r\n * @function\r\n * @param {*} obj Object\r\n * @returns {boolean}\r\n * @inner\r\n */\r\nfunction isLong(obj) {\r\n    return (obj && obj[\"__isLong__\"]) === true;\r\n}\r\n\r\n/**\r\n * Tests if the specified object is a Long.\r\n * @function\r\n * @param {*} obj Object\r\n * @returns {boolean}\r\n */\r\nLong.isLong = isLong;\r\n\r\n/**\r\n * A cache of the Long representations of small integer values.\r\n * @type {!Object}\r\n * @inner\r\n */\r\nvar INT_CACHE = {};\r\n\r\n/**\r\n * A cache of the Long representations of small unsigned integer values.\r\n * @type {!Object}\r\n * @inner\r\n */\r\nvar UINT_CACHE = {};\r\n\r\n/**\r\n * @param {number} value\r\n * @param {boolean=} unsigned\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromInt(value, unsigned) {\r\n    var obj, cachedObj, cache;\r\n    if (unsigned) {\r\n        value >>>= 0;\r\n        if (cache = (0 <= value && value < 256)) {\r\n            cachedObj = UINT_CACHE[value];\r\n            if (cachedObj)\r\n                return cachedObj;\r\n        }\r\n        obj = fromBits(value, (value | 0) < 0 ? -1 : 0, true);\r\n        if (cache)\r\n            UINT_CACHE[value] = obj;\r\n        return obj;\r\n    } else {\r\n        value |= 0;\r\n        if (cache = (-128 <= value && value < 128)) {\r\n            cachedObj = INT_CACHE[value];\r\n            if (cachedObj)\r\n                return cachedObj;\r\n        }\r\n        obj = fromBits(value, value < 0 ? -1 : 0, false);\r\n        if (cache)\r\n            INT_CACHE[value] = obj;\r\n        return obj;\r\n    }\r\n}\r\n\r\n/**\r\n * Returns a Long representing the given 32 bit integer value.\r\n * @function\r\n * @param {number} value The 32 bit integer in question\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {!Long} The corresponding Long value\r\n */\r\nLong.fromInt = fromInt;\r\n\r\n/**\r\n * @param {number} value\r\n * @param {boolean=} unsigned\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromNumber(value, unsigned) {\r\n    if (isNaN(value))\r\n        return unsigned ? UZERO : ZERO;\r\n    if (unsigned) {\r\n        if (value < 0)\r\n            return UZERO;\r\n        if (value >= TWO_PWR_64_DBL)\r\n            return MAX_UNSIGNED_VALUE;\r\n    } else {\r\n        if (value <= -TWO_PWR_63_DBL)\r\n            return MIN_VALUE;\r\n        if (value + 1 >= TWO_PWR_63_DBL)\r\n            return MAX_VALUE;\r\n    }\r\n    if (value < 0)\r\n        return fromNumber(-value, unsigned).neg();\r\n    return fromBits((value % TWO_PWR_32_DBL) | 0, (value / TWO_PWR_32_DBL) | 0, unsigned);\r\n}\r\n\r\n/**\r\n * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\r\n * @function\r\n * @param {number} value The number in question\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {!Long} The corresponding Long value\r\n */\r\nLong.fromNumber = fromNumber;\r\n\r\n/**\r\n * @param {number} lowBits\r\n * @param {number} highBits\r\n * @param {boolean=} unsigned\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromBits(lowBits, highBits, unsigned) {\r\n    return new Long(lowBits, highBits, unsigned);\r\n}\r\n\r\n/**\r\n * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\r\n *  assumed to use 32 bits.\r\n * @function\r\n * @param {number} lowBits The low 32 bits\r\n * @param {number} highBits The high 32 bits\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {!Long} The corresponding Long value\r\n */\r\nLong.fromBits = fromBits;\r\n\r\n/**\r\n * @function\r\n * @param {number} base\r\n * @param {number} exponent\r\n * @returns {number}\r\n * @inner\r\n */\r\nvar pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\r\n\r\n/**\r\n * @param {string} str\r\n * @param {(boolean|number)=} unsigned\r\n * @param {number=} radix\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromString(str, unsigned, radix) {\r\n    if (str.length === 0)\r\n        throw Error('empty string');\r\n    if (str === \"NaN\" || str === \"Infinity\" || str === \"+Infinity\" || str === \"-Infinity\")\r\n        return ZERO;\r\n    if (typeof unsigned === 'number') {\r\n        // For goog.math.long compatibility\r\n        radix = unsigned,\r\n        unsigned = false;\r\n    } else {\r\n        unsigned = !! unsigned;\r\n    }\r\n    radix = radix || 10;\r\n    if (radix < 2 || 36 < radix)\r\n        throw RangeError('radix');\r\n\r\n    var p;\r\n    if ((p = str.indexOf('-')) > 0)\r\n        throw Error('interior hyphen');\r\n    else if (p === 0) {\r\n        return fromString(str.substring(1), unsigned, radix).neg();\r\n    }\r\n\r\n    // Do several (8) digits each time through the loop, so as to\r\n    // minimize the calls to the very expensive emulated div.\r\n    var radixToPower = fromNumber(pow_dbl(radix, 8));\r\n\r\n    var result = ZERO;\r\n    for (var i = 0; i < str.length; i += 8) {\r\n        var size = Math.min(8, str.length - i),\r\n            value = parseInt(str.substring(i, i + size), radix);\r\n        if (size < 8) {\r\n            var power = fromNumber(pow_dbl(radix, size));\r\n            result = result.mul(power).add(fromNumber(value));\r\n        } else {\r\n            result = result.mul(radixToPower);\r\n            result = result.add(fromNumber(value));\r\n        }\r\n    }\r\n    result.unsigned = unsigned;\r\n    return result;\r\n}\r\n\r\n/**\r\n * Returns a Long representation of the given string, written using the specified radix.\r\n * @function\r\n * @param {string} str The textual representation of the Long\r\n * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\r\n * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\r\n * @returns {!Long} The corresponding Long value\r\n */\r\nLong.fromString = fromString;\r\n\r\n/**\r\n * @function\r\n * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\r\n * @param {boolean=} unsigned\r\n * @returns {!Long}\r\n * @inner\r\n */\r\nfunction fromValue(val, unsigned) {\r\n    if (typeof val === 'number')\r\n        return fromNumber(val, unsigned);\r\n    if (typeof val === 'string')\r\n        return fromString(val, unsigned);\r\n    // Throws for non-objects, converts non-instanceof Long:\r\n    return fromBits(val.low, val.high, typeof unsigned === 'boolean' ? unsigned : val.unsigned);\r\n}\r\n\r\n/**\r\n * Converts the specified value to a Long using the appropriate from* function for its type.\r\n * @function\r\n * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val Value\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {!Long}\r\n */\r\nLong.fromValue = fromValue;\r\n\r\n// NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\r\n// no runtime penalty for these.\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_16_DBL = 1 << 16;\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_24_DBL = 1 << 24;\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\r\n\r\n/**\r\n * @type {number}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\r\n\r\n/**\r\n * @type {!Long}\r\n * @const\r\n * @inner\r\n */\r\nvar TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar ZERO = fromInt(0);\r\n\r\n/**\r\n * Signed zero.\r\n * @type {!Long}\r\n */\r\nLong.ZERO = ZERO;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar UZERO = fromInt(0, true);\r\n\r\n/**\r\n * Unsigned zero.\r\n * @type {!Long}\r\n */\r\nLong.UZERO = UZERO;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar ONE = fromInt(1);\r\n\r\n/**\r\n * Signed one.\r\n * @type {!Long}\r\n */\r\nLong.ONE = ONE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar UONE = fromInt(1, true);\r\n\r\n/**\r\n * Unsigned one.\r\n * @type {!Long}\r\n */\r\nLong.UONE = UONE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar NEG_ONE = fromInt(-1);\r\n\r\n/**\r\n * Signed negative one.\r\n * @type {!Long}\r\n */\r\nLong.NEG_ONE = NEG_ONE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar MAX_VALUE = fromBits(0xFFFFFFFF|0, 0x7FFFFFFF|0, false);\r\n\r\n/**\r\n * Maximum signed value.\r\n * @type {!Long}\r\n */\r\nLong.MAX_VALUE = MAX_VALUE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar MAX_UNSIGNED_VALUE = fromBits(0xFFFFFFFF|0, 0xFFFFFFFF|0, true);\r\n\r\n/**\r\n * Maximum unsigned value.\r\n * @type {!Long}\r\n */\r\nLong.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\r\n\r\n/**\r\n * @type {!Long}\r\n * @inner\r\n */\r\nvar MIN_VALUE = fromBits(0, 0x80000000|0, false);\r\n\r\n/**\r\n * Minimum signed value.\r\n * @type {!Long}\r\n */\r\nLong.MIN_VALUE = MIN_VALUE;\r\n\r\n/**\r\n * @alias Long.prototype\r\n * @inner\r\n */\r\nvar LongPrototype = Long.prototype;\r\n\r\n/**\r\n * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\r\n * @returns {number}\r\n */\r\nLongPrototype.toInt = function toInt() {\r\n    return this.unsigned ? this.low >>> 0 : this.low;\r\n};\r\n\r\n/**\r\n * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\r\n * @returns {number}\r\n */\r\nLongPrototype.toNumber = function toNumber() {\r\n    if (this.unsigned)\r\n        return ((this.high >>> 0) * TWO_PWR_32_DBL) + (this.low >>> 0);\r\n    return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\r\n};\r\n\r\n/**\r\n * Converts the Long to a string written in the specified radix.\r\n * @param {number=} radix Radix (2-36), defaults to 10\r\n * @returns {string}\r\n * @override\r\n * @throws {RangeError} If `radix` is out of range\r\n */\r\nLongPrototype.toString = function toString(radix) {\r\n    radix = radix || 10;\r\n    if (radix < 2 || 36 < radix)\r\n        throw RangeError('radix');\r\n    if (this.isZero())\r\n        return '0';\r\n    if (this.isNegative()) { // Unsigned Longs are never negative\r\n        if (this.eq(MIN_VALUE)) {\r\n            // We need to change the Long value before it can be negated, so we remove\r\n            // the bottom-most digit in this base and then recurse to do the rest.\r\n            var radixLong = fromNumber(radix),\r\n                div = this.div(radixLong),\r\n                rem1 = div.mul(radixLong).sub(this);\r\n            return div.toString(radix) + rem1.toInt().toString(radix);\r\n        } else\r\n            return '-' + this.neg().toString(radix);\r\n    }\r\n\r\n    // Do several (6) digits each time through the loop, so as to\r\n    // minimize the calls to the very expensive emulated div.\r\n    var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\r\n        rem = this;\r\n    var result = '';\r\n    while (true) {\r\n        var remDiv = rem.div(radixToPower),\r\n            intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\r\n            digits = intval.toString(radix);\r\n        rem = remDiv;\r\n        if (rem.isZero())\r\n            return digits + result;\r\n        else {\r\n            while (digits.length < 6)\r\n                digits = '0' + digits;\r\n            result = '' + digits + result;\r\n        }\r\n    }\r\n};\r\n\r\n/**\r\n * Gets the high 32 bits as a signed integer.\r\n * @returns {number} Signed high bits\r\n */\r\nLongPrototype.getHighBits = function getHighBits() {\r\n    return this.high;\r\n};\r\n\r\n/**\r\n * Gets the high 32 bits as an unsigned integer.\r\n * @returns {number} Unsigned high bits\r\n */\r\nLongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\r\n    return this.high >>> 0;\r\n};\r\n\r\n/**\r\n * Gets the low 32 bits as a signed integer.\r\n * @returns {number} Signed low bits\r\n */\r\nLongPrototype.getLowBits = function getLowBits() {\r\n    return this.low;\r\n};\r\n\r\n/**\r\n * Gets the low 32 bits as an unsigned integer.\r\n * @returns {number} Unsigned low bits\r\n */\r\nLongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\r\n    return this.low >>> 0;\r\n};\r\n\r\n/**\r\n * Gets the number of bits needed to represent the absolute value of this Long.\r\n * @returns {number}\r\n */\r\nLongPrototype.getNumBitsAbs = function getNumBitsAbs() {\r\n    if (this.isNegative()) // Unsigned Longs are never negative\r\n        return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\r\n    var val = this.high != 0 ? this.high : this.low;\r\n    for (var bit = 31; bit > 0; bit--)\r\n        if ((val & (1 << bit)) != 0)\r\n            break;\r\n    return this.high != 0 ? bit + 33 : bit + 1;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value equals zero.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isZero = function isZero() {\r\n    return this.high === 0 && this.low === 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.eqz = LongPrototype.isZero;\r\n\r\n/**\r\n * Tests if this Long's value is negative.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isNegative = function isNegative() {\r\n    return !this.unsigned && this.high < 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is positive.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isPositive = function isPositive() {\r\n    return this.unsigned || this.high >= 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is odd.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isOdd = function isOdd() {\r\n    return (this.low & 1) === 1;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is even.\r\n * @returns {boolean}\r\n */\r\nLongPrototype.isEven = function isEven() {\r\n    return (this.low & 1) === 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value equals the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.equals = function equals(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    if (this.unsigned !== other.unsigned && (this.high >>> 31) === 1 && (other.high >>> 31) === 1)\r\n        return false;\r\n    return this.high === other.high && this.low === other.low;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.eq = LongPrototype.equals;\r\n\r\n/**\r\n * Tests if this Long's value differs from the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.notEquals = function notEquals(other) {\r\n    return !this.eq(/* validates */ other);\r\n};\r\n\r\n/**\r\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.neq = LongPrototype.notEquals;\r\n\r\n/**\r\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.ne = LongPrototype.notEquals;\r\n\r\n/**\r\n * Tests if this Long's value is less than the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.lessThan = function lessThan(other) {\r\n    return this.comp(/* validates */ other) < 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.lt = LongPrototype.lessThan;\r\n\r\n/**\r\n * Tests if this Long's value is less than or equal the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\r\n    return this.comp(/* validates */ other) <= 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.lte = LongPrototype.lessThanOrEqual;\r\n\r\n/**\r\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.le = LongPrototype.lessThanOrEqual;\r\n\r\n/**\r\n * Tests if this Long's value is greater than the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.greaterThan = function greaterThan(other) {\r\n    return this.comp(/* validates */ other) > 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.gt = LongPrototype.greaterThan;\r\n\r\n/**\r\n * Tests if this Long's value is greater than or equal the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\r\n    return this.comp(/* validates */ other) >= 0;\r\n};\r\n\r\n/**\r\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.gte = LongPrototype.greaterThanOrEqual;\r\n\r\n/**\r\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {boolean}\r\n */\r\nLongPrototype.ge = LongPrototype.greaterThanOrEqual;\r\n\r\n/**\r\n * Compares this Long's value with the specified's.\r\n * @param {!Long|number|string} other Other value\r\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\r\n *  if the given one is greater\r\n */\r\nLongPrototype.compare = function compare(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    if (this.eq(other))\r\n        return 0;\r\n    var thisNeg = this.isNegative(),\r\n        otherNeg = other.isNegative();\r\n    if (thisNeg && !otherNeg)\r\n        return -1;\r\n    if (!thisNeg && otherNeg)\r\n        return 1;\r\n    // At this point the sign bits are the same\r\n    if (!this.unsigned)\r\n        return this.sub(other).isNegative() ? -1 : 1;\r\n    // Both are positive if at least one is unsigned\r\n    return (other.high >>> 0) > (this.high >>> 0) || (other.high === this.high && (other.low >>> 0) > (this.low >>> 0)) ? -1 : 1;\r\n};\r\n\r\n/**\r\n * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\r\n * @function\r\n * @param {!Long|number|string} other Other value\r\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\r\n *  if the given one is greater\r\n */\r\nLongPrototype.comp = LongPrototype.compare;\r\n\r\n/**\r\n * Negates this Long's value.\r\n * @returns {!Long} Negated Long\r\n */\r\nLongPrototype.negate = function negate() {\r\n    if (!this.unsigned && this.eq(MIN_VALUE))\r\n        return MIN_VALUE;\r\n    return this.not().add(ONE);\r\n};\r\n\r\n/**\r\n * Negates this Long's value. This is an alias of {@link Long#negate}.\r\n * @function\r\n * @returns {!Long} Negated Long\r\n */\r\nLongPrototype.neg = LongPrototype.negate;\r\n\r\n/**\r\n * Returns the sum of this and the specified Long.\r\n * @param {!Long|number|string} addend Addend\r\n * @returns {!Long} Sum\r\n */\r\nLongPrototype.add = function add(addend) {\r\n    if (!isLong(addend))\r\n        addend = fromValue(addend);\r\n\r\n    // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\r\n\r\n    var a48 = this.high >>> 16;\r\n    var a32 = this.high & 0xFFFF;\r\n    var a16 = this.low >>> 16;\r\n    var a00 = this.low & 0xFFFF;\r\n\r\n    var b48 = addend.high >>> 16;\r\n    var b32 = addend.high & 0xFFFF;\r\n    var b16 = addend.low >>> 16;\r\n    var b00 = addend.low & 0xFFFF;\r\n\r\n    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\r\n    c00 += a00 + b00;\r\n    c16 += c00 >>> 16;\r\n    c00 &= 0xFFFF;\r\n    c16 += a16 + b16;\r\n    c32 += c16 >>> 16;\r\n    c16 &= 0xFFFF;\r\n    c32 += a32 + b32;\r\n    c48 += c32 >>> 16;\r\n    c32 &= 0xFFFF;\r\n    c48 += a48 + b48;\r\n    c48 &= 0xFFFF;\r\n    return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the difference of this and the specified Long.\r\n * @param {!Long|number|string} subtrahend Subtrahend\r\n * @returns {!Long} Difference\r\n */\r\nLongPrototype.subtract = function subtract(subtrahend) {\r\n    if (!isLong(subtrahend))\r\n        subtrahend = fromValue(subtrahend);\r\n    return this.add(subtrahend.neg());\r\n};\r\n\r\n/**\r\n * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\r\n * @function\r\n * @param {!Long|number|string} subtrahend Subtrahend\r\n * @returns {!Long} Difference\r\n */\r\nLongPrototype.sub = LongPrototype.subtract;\r\n\r\n/**\r\n * Returns the product of this and the specified Long.\r\n * @param {!Long|number|string} multiplier Multiplier\r\n * @returns {!Long} Product\r\n */\r\nLongPrototype.multiply = function multiply(multiplier) {\r\n    if (this.isZero())\r\n        return ZERO;\r\n    if (!isLong(multiplier))\r\n        multiplier = fromValue(multiplier);\r\n\r\n    // use wasm support if present\r\n    if (wasm) {\r\n        var low = wasm.mul(this.low,\r\n                           this.high,\r\n                           multiplier.low,\r\n                           multiplier.high);\r\n        return fromBits(low, wasm.get_high(), this.unsigned);\r\n    }\r\n\r\n    if (multiplier.isZero())\r\n        return ZERO;\r\n    if (this.eq(MIN_VALUE))\r\n        return multiplier.isOdd() ? MIN_VALUE : ZERO;\r\n    if (multiplier.eq(MIN_VALUE))\r\n        return this.isOdd() ? MIN_VALUE : ZERO;\r\n\r\n    if (this.isNegative()) {\r\n        if (multiplier.isNegative())\r\n            return this.neg().mul(multiplier.neg());\r\n        else\r\n            return this.neg().mul(multiplier).neg();\r\n    } else if (multiplier.isNegative())\r\n        return this.mul(multiplier.neg()).neg();\r\n\r\n    // If both longs are small, use float multiplication\r\n    if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24))\r\n        return fromNumber(this.toNumber() * multiplier.toNumber(), this.unsigned);\r\n\r\n    // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\r\n    // We can skip products that would overflow.\r\n\r\n    var a48 = this.high >>> 16;\r\n    var a32 = this.high & 0xFFFF;\r\n    var a16 = this.low >>> 16;\r\n    var a00 = this.low & 0xFFFF;\r\n\r\n    var b48 = multiplier.high >>> 16;\r\n    var b32 = multiplier.high & 0xFFFF;\r\n    var b16 = multiplier.low >>> 16;\r\n    var b00 = multiplier.low & 0xFFFF;\r\n\r\n    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\r\n    c00 += a00 * b00;\r\n    c16 += c00 >>> 16;\r\n    c00 &= 0xFFFF;\r\n    c16 += a16 * b00;\r\n    c32 += c16 >>> 16;\r\n    c16 &= 0xFFFF;\r\n    c16 += a00 * b16;\r\n    c32 += c16 >>> 16;\r\n    c16 &= 0xFFFF;\r\n    c32 += a32 * b00;\r\n    c48 += c32 >>> 16;\r\n    c32 &= 0xFFFF;\r\n    c32 += a16 * b16;\r\n    c48 += c32 >>> 16;\r\n    c32 &= 0xFFFF;\r\n    c32 += a00 * b32;\r\n    c48 += c32 >>> 16;\r\n    c32 &= 0xFFFF;\r\n    c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\r\n    c48 &= 0xFFFF;\r\n    return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\r\n * @function\r\n * @param {!Long|number|string} multiplier Multiplier\r\n * @returns {!Long} Product\r\n */\r\nLongPrototype.mul = LongPrototype.multiply;\r\n\r\n/**\r\n * Returns this Long divided by the specified. The result is signed if this Long is signed or\r\n *  unsigned if this Long is unsigned.\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Quotient\r\n */\r\nLongPrototype.divide = function divide(divisor) {\r\n    if (!isLong(divisor))\r\n        divisor = fromValue(divisor);\r\n    if (divisor.isZero())\r\n        throw Error('division by zero');\r\n\r\n    // use wasm support if present\r\n    if (wasm) {\r\n        // guard against signed division overflow: the largest\r\n        // negative number / -1 would be 1 larger than the largest\r\n        // positive number, due to two's complement.\r\n        if (!this.unsigned &&\r\n            this.high === -0x80000000 &&\r\n            divisor.low === -1 && divisor.high === -1) {\r\n            // be consistent with non-wasm code path\r\n            return this;\r\n        }\r\n        var low = (this.unsigned ? wasm.div_u : wasm.div_s)(\r\n            this.low,\r\n            this.high,\r\n            divisor.low,\r\n            divisor.high\r\n        );\r\n        return fromBits(low, wasm.get_high(), this.unsigned);\r\n    }\r\n\r\n    if (this.isZero())\r\n        return this.unsigned ? UZERO : ZERO;\r\n    var approx, rem, res;\r\n    if (!this.unsigned) {\r\n        // This section is only relevant for signed longs and is derived from the\r\n        // closure library as a whole.\r\n        if (this.eq(MIN_VALUE)) {\r\n            if (divisor.eq(ONE) || divisor.eq(NEG_ONE))\r\n                return MIN_VALUE;  // recall that -MIN_VALUE == MIN_VALUE\r\n            else if (divisor.eq(MIN_VALUE))\r\n                return ONE;\r\n            else {\r\n                // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\r\n                var halfThis = this.shr(1);\r\n                approx = halfThis.div(divisor).shl(1);\r\n                if (approx.eq(ZERO)) {\r\n                    return divisor.isNegative() ? ONE : NEG_ONE;\r\n                } else {\r\n                    rem = this.sub(divisor.mul(approx));\r\n                    res = approx.add(rem.div(divisor));\r\n                    return res;\r\n                }\r\n            }\r\n        } else if (divisor.eq(MIN_VALUE))\r\n            return this.unsigned ? UZERO : ZERO;\r\n        if (this.isNegative()) {\r\n            if (divisor.isNegative())\r\n                return this.neg().div(divisor.neg());\r\n            return this.neg().div(divisor).neg();\r\n        } else if (divisor.isNegative())\r\n            return this.div(divisor.neg()).neg();\r\n        res = ZERO;\r\n    } else {\r\n        // The algorithm below has not been made for unsigned longs. It's therefore\r\n        // required to take special care of the MSB prior to running it.\r\n        if (!divisor.unsigned)\r\n            divisor = divisor.toUnsigned();\r\n        if (divisor.gt(this))\r\n            return UZERO;\r\n        if (divisor.gt(this.shru(1))) // 15 >>> 1 = 7 ; with divisor = 8 ; true\r\n            return UONE;\r\n        res = UZERO;\r\n    }\r\n\r\n    // Repeat the following until the remainder is less than other:  find a\r\n    // floating-point that approximates remainder / other *from below*, add this\r\n    // into the result, and subtract it from the remainder.  It is critical that\r\n    // the approximate value is less than or equal to the real value so that the\r\n    // remainder never becomes negative.\r\n    rem = this;\r\n    while (rem.gte(divisor)) {\r\n        // Approximate the result of division. This may be a little greater or\r\n        // smaller than the actual value.\r\n        approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\r\n\r\n        // We will tweak the approximate result by changing it in the 48-th digit or\r\n        // the smallest non-fractional digit, whichever is larger.\r\n        var log2 = Math.ceil(Math.log(approx) / Math.LN2),\r\n            delta = (log2 <= 48) ? 1 : pow_dbl(2, log2 - 48),\r\n\r\n        // Decrease the approximation until it is smaller than the remainder.  Note\r\n        // that if it is too large, the product overflows and is negative.\r\n            approxRes = fromNumber(approx),\r\n            approxRem = approxRes.mul(divisor);\r\n        while (approxRem.isNegative() || approxRem.gt(rem)) {\r\n            approx -= delta;\r\n            approxRes = fromNumber(approx, this.unsigned);\r\n            approxRem = approxRes.mul(divisor);\r\n        }\r\n\r\n        // We know the answer can't be zero... and actually, zero would cause\r\n        // infinite recursion since we would make no progress.\r\n        if (approxRes.isZero())\r\n            approxRes = ONE;\r\n\r\n        res = res.add(approxRes);\r\n        rem = rem.sub(approxRem);\r\n    }\r\n    return res;\r\n};\r\n\r\n/**\r\n * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\r\n * @function\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Quotient\r\n */\r\nLongPrototype.div = LongPrototype.divide;\r\n\r\n/**\r\n * Returns this Long modulo the specified.\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Remainder\r\n */\r\nLongPrototype.modulo = function modulo(divisor) {\r\n    if (!isLong(divisor))\r\n        divisor = fromValue(divisor);\r\n\r\n    // use wasm support if present\r\n    if (wasm) {\r\n        var low = (this.unsigned ? wasm.rem_u : wasm.rem_s)(\r\n            this.low,\r\n            this.high,\r\n            divisor.low,\r\n            divisor.high\r\n        );\r\n        return fromBits(low, wasm.get_high(), this.unsigned);\r\n    }\r\n\r\n    return this.sub(this.div(divisor).mul(divisor));\r\n};\r\n\r\n/**\r\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\r\n * @function\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Remainder\r\n */\r\nLongPrototype.mod = LongPrototype.modulo;\r\n\r\n/**\r\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\r\n * @function\r\n * @param {!Long|number|string} divisor Divisor\r\n * @returns {!Long} Remainder\r\n */\r\nLongPrototype.rem = LongPrototype.modulo;\r\n\r\n/**\r\n * Returns the bitwise NOT of this Long.\r\n * @returns {!Long}\r\n */\r\nLongPrototype.not = function not() {\r\n    return fromBits(~this.low, ~this.high, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the bitwise AND of this Long and the specified.\r\n * @param {!Long|number|string} other Other Long\r\n * @returns {!Long}\r\n */\r\nLongPrototype.and = function and(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    return fromBits(this.low & other.low, this.high & other.high, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the bitwise OR of this Long and the specified.\r\n * @param {!Long|number|string} other Other Long\r\n * @returns {!Long}\r\n */\r\nLongPrototype.or = function or(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    return fromBits(this.low | other.low, this.high | other.high, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns the bitwise XOR of this Long and the given one.\r\n * @param {!Long|number|string} other Other Long\r\n * @returns {!Long}\r\n */\r\nLongPrototype.xor = function xor(other) {\r\n    if (!isLong(other))\r\n        other = fromValue(other);\r\n    return fromBits(this.low ^ other.low, this.high ^ other.high, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns this Long with bits shifted to the left by the given amount.\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shiftLeft = function shiftLeft(numBits) {\r\n    if (isLong(numBits))\r\n        numBits = numBits.toInt();\r\n    if ((numBits &= 63) === 0)\r\n        return this;\r\n    else if (numBits < 32)\r\n        return fromBits(this.low << numBits, (this.high << numBits) | (this.low >>> (32 - numBits)), this.unsigned);\r\n    else\r\n        return fromBits(0, this.low << (numBits - 32), this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\r\n * @function\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shl = LongPrototype.shiftLeft;\r\n\r\n/**\r\n * Returns this Long with bits arithmetically shifted to the right by the given amount.\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shiftRight = function shiftRight(numBits) {\r\n    if (isLong(numBits))\r\n        numBits = numBits.toInt();\r\n    if ((numBits &= 63) === 0)\r\n        return this;\r\n    else if (numBits < 32)\r\n        return fromBits((this.low >>> numBits) | (this.high << (32 - numBits)), this.high >> numBits, this.unsigned);\r\n    else\r\n        return fromBits(this.high >> (numBits - 32), this.high >= 0 ? 0 : -1, this.unsigned);\r\n};\r\n\r\n/**\r\n * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\r\n * @function\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shr = LongPrototype.shiftRight;\r\n\r\n/**\r\n * Returns this Long with bits logically shifted to the right by the given amount.\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\r\n    if (isLong(numBits))\r\n        numBits = numBits.toInt();\r\n    numBits &= 63;\r\n    if (numBits === 0)\r\n        return this;\r\n    else {\r\n        var high = this.high;\r\n        if (numBits < 32) {\r\n            var low = this.low;\r\n            return fromBits((low >>> numBits) | (high << (32 - numBits)), high >>> numBits, this.unsigned);\r\n        } else if (numBits === 32)\r\n            return fromBits(high, 0, this.unsigned);\r\n        else\r\n            return fromBits(high >>> (numBits - 32), 0, this.unsigned);\r\n    }\r\n};\r\n\r\n/**\r\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\r\n * @function\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shru = LongPrototype.shiftRightUnsigned;\r\n\r\n/**\r\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\r\n * @function\r\n * @param {number|!Long} numBits Number of bits\r\n * @returns {!Long} Shifted Long\r\n */\r\nLongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\r\n\r\n/**\r\n * Converts this Long to signed.\r\n * @returns {!Long} Signed long\r\n */\r\nLongPrototype.toSigned = function toSigned() {\r\n    if (!this.unsigned)\r\n        return this;\r\n    return fromBits(this.low, this.high, false);\r\n};\r\n\r\n/**\r\n * Converts this Long to unsigned.\r\n * @returns {!Long} Unsigned long\r\n */\r\nLongPrototype.toUnsigned = function toUnsigned() {\r\n    if (this.unsigned)\r\n        return this;\r\n    return fromBits(this.low, this.high, true);\r\n};\r\n\r\n/**\r\n * Converts this Long to its byte representation.\r\n * @param {boolean=} le Whether little or big endian, defaults to big endian\r\n * @returns {!Array.<number>} Byte representation\r\n */\r\nLongPrototype.toBytes = function toBytes(le) {\r\n    return le ? this.toBytesLE() : this.toBytesBE();\r\n};\r\n\r\n/**\r\n * Converts this Long to its little endian byte representation.\r\n * @returns {!Array.<number>} Little endian byte representation\r\n */\r\nLongPrototype.toBytesLE = function toBytesLE() {\r\n    var hi = this.high,\r\n        lo = this.low;\r\n    return [\r\n        lo        & 0xff,\r\n        lo >>>  8 & 0xff,\r\n        lo >>> 16 & 0xff,\r\n        lo >>> 24       ,\r\n        hi        & 0xff,\r\n        hi >>>  8 & 0xff,\r\n        hi >>> 16 & 0xff,\r\n        hi >>> 24\r\n    ];\r\n};\r\n\r\n/**\r\n * Converts this Long to its big endian byte representation.\r\n * @returns {!Array.<number>} Big endian byte representation\r\n */\r\nLongPrototype.toBytesBE = function toBytesBE() {\r\n    var hi = this.high,\r\n        lo = this.low;\r\n    return [\r\n        hi >>> 24       ,\r\n        hi >>> 16 & 0xff,\r\n        hi >>>  8 & 0xff,\r\n        hi        & 0xff,\r\n        lo >>> 24       ,\r\n        lo >>> 16 & 0xff,\r\n        lo >>>  8 & 0xff,\r\n        lo        & 0xff\r\n    ];\r\n};\r\n\r\n/**\r\n * Creates a Long from its byte representation.\r\n * @param {!Array.<number>} bytes Byte representation\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @param {boolean=} le Whether little or big endian, defaults to big endian\r\n * @returns {Long} The corresponding Long value\r\n */\r\nLong.fromBytes = function fromBytes(bytes, unsigned, le) {\r\n    return le ? Long.fromBytesLE(bytes, unsigned) : Long.fromBytesBE(bytes, unsigned);\r\n};\r\n\r\n/**\r\n * Creates a Long from its little endian byte representation.\r\n * @param {!Array.<number>} bytes Little endian byte representation\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {Long} The corresponding Long value\r\n */\r\nLong.fromBytesLE = function fromBytesLE(bytes, unsigned) {\r\n    return new Long(\r\n        bytes[0]       |\r\n        bytes[1] <<  8 |\r\n        bytes[2] << 16 |\r\n        bytes[3] << 24,\r\n        bytes[4]       |\r\n        bytes[5] <<  8 |\r\n        bytes[6] << 16 |\r\n        bytes[7] << 24,\r\n        unsigned\r\n    );\r\n};\r\n\r\n/**\r\n * Creates a Long from its big endian byte representation.\r\n * @param {!Array.<number>} bytes Big endian byte representation\r\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\r\n * @returns {Long} The corresponding Long value\r\n */\r\nLong.fromBytesBE = function fromBytesBE(bytes, unsigned) {\r\n    return new Long(\r\n        bytes[4] << 24 |\r\n        bytes[5] << 16 |\r\n        bytes[6] <<  8 |\r\n        bytes[7],\r\n        bytes[0] << 24 |\r\n        bytes[1] << 16 |\r\n        bytes[2] <<  8 |\r\n        bytes[3],\r\n        unsigned\r\n    );\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/long.js\n// module id = 0\n// module chunks = 0"], "sourceRoot": ""}