{"transform-template-literals": {"chrome": "41", "edge": "13", "firefox": "34", "safari": "9", "node": "4", "ios": "9", "opera": "28", "electron": "0.24"}, "transform-literals": {"chrome": "44", "edge": "12", "firefox": "53", "safari": "9", "node": "4", "ios": "9", "opera": "31", "electron": "0.31"}, "transform-function-name": {"chrome": "51", "firefox": "53", "safari": "10", "node": "6.5", "ios": "10", "opera": "38", "electron": "1.2"}, "transform-arrow-functions": {"chrome": "47", "edge": "13", "firefox": "45", "safari": "10", "node": "6", "ios": "10", "opera": "34", "electron": "0.36"}, "transform-block-scoped-functions": {"chrome": "41", "edge": "12", "firefox": "46", "safari": "10", "node": "4", "ie": "11", "ios": "10", "opera": "28", "electron": "0.24"}, "transform-classes": {"chrome": "46", "edge": "13", "firefox": "45", "safari": "10", "node": "5", "ios": "10", "opera": "33", "electron": "0.36"}, "transform-object-super": {"chrome": "46", "edge": "13", "firefox": "45", "safari": "10", "node": "5", "ios": "10", "opera": "33", "electron": "0.36"}, "transform-shorthand-properties": {"chrome": "43", "edge": "12", "firefox": "33", "safari": "9", "node": "4", "ios": "9", "opera": "30", "electron": "0.29"}, "transform-duplicate-keys": {"chrome": "42", "edge": "12", "firefox": "34", "safari": "9", "node": "4", "ios": "9", "opera": "29", "electron": "0.27"}, "transform-computed-properties": {"chrome": "44", "edge": "12", "firefox": "34", "safari": "7.1", "node": "4", "ios": "8", "opera": "31", "electron": "0.31"}, "transform-for-of": {"chrome": "51", "edge": "15", "firefox": "53", "safari": "10", "node": "6.5", "ios": "10", "opera": "38", "electron": "1.2"}, "transform-sticky-regex": {"chrome": "49", "edge": "13", "firefox": "3", "safari": "10", "node": "6", "ios": "10", "opera": "36", "electron": "1"}, "transform-dotall-regex": {"chrome": "62", "safari": "11.1", "node": "8.10", "ios": "11.3", "opera": "49", "electron": "3"}, "transform-unicode-regex": {"chrome": "50", "edge": "13", "firefox": "46", "safari": "12", "node": "6", "ios": "12", "opera": "37", "electron": "1.1"}, "transform-spread": {"chrome": "46", "edge": "13", "firefox": "36", "safari": "10", "node": "5", "ios": "10", "opera": "33", "electron": "0.36"}, "transform-parameters": {"chrome": "49", "edge": "18", "firefox": "53", "safari": "10", "node": "6", "ios": "10", "opera": "36", "electron": "1"}, "transform-destructuring": {"chrome": "51", "edge": "18", "firefox": "53", "safari": "10", "node": "6.5", "ios": "10", "opera": "38", "electron": "1.2"}, "transform-block-scoping": {"chrome": "49", "edge": "14", "firefox": "51", "safari": "10.1", "node": "6", "ios": "10.3", "opera": "36", "electron": "1"}, "transform-typeof-symbol": {"chrome": "38", "edge": "12", "firefox": "36", "safari": "9", "node": "0.12", "ios": "9", "opera": "25", "electron": "0.2"}, "transform-new-target": {"chrome": "46", "edge": "14", "firefox": "41", "safari": "10", "node": "5", "ios": "10", "opera": "33", "electron": "0.36"}, "transform-regenerator": {"chrome": "50", "edge": "13", "firefox": "53", "safari": "10", "node": "6", "ios": "10", "opera": "37", "electron": "1.1"}, "transform-exponentiation-operator": {"chrome": "52", "edge": "14", "firefox": "52", "safari": "10.1", "node": "7", "ios": "10.3", "opera": "39", "electron": "1.3"}, "transform-async-to-generator": {"chrome": "55", "edge": "15", "firefox": "52", "safari": "11", "node": "7.6", "ios": "11", "opera": "42", "electron": "1.6"}, "proposal-async-generator-functions": {"chrome": "63", "firefox": "57", "safari": "12", "node": "10.0", "ios": "12", "opera": "50", "electron": "3"}, "proposal-object-rest-spread": {"chrome": "60", "firefox": "55", "safari": "11.1", "node": "8.3", "ios": "11.3", "opera": "47", "electron": "2.1"}, "proposal-unicode-property-regex": {"chrome": "64", "safari": "11.1", "node": "10.0", "ios": "11.3", "opera": "51", "electron": "3"}, "proposal-json-strings": {"chrome": "66", "firefox": "62", "safari": "12", "node": "10.0", "ios": "12", "opera": "53", "electron": "3"}, "proposal-optional-catch-binding": {"chrome": "66", "firefox": "58", "safari": "11.1", "node": "10.0", "ios": "11.3", "opera": "53", "electron": "3"}, "transform-named-capturing-groups-regex": {"chrome": "64", "safari": "11.1", "node": "10.0", "ios": "11.3", "opera": "51", "electron": "3"}}