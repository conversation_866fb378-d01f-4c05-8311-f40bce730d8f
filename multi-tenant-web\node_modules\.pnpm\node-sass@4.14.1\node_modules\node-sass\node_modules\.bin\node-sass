#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/node-sass@4.14.1/node_modules/node-sass/bin/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/node-sass@4.14.1/node_modules/node-sass/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/node-sass@4.14.1/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/node-sass@4.14.1/node_modules/node-sass/bin/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/node-sass@4.14.1/node_modules/node-sass/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/node-sass@4.14.1/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/node-sass" "$@"
else
  exec node  "$basedir/../../bin/node-sass" "$@"
fi
