{"name": "@webassemblyjs/helper-buffer", "version": "1.7.11", "description": "Buffer manipulation utility", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.11", "jest-diff": "^22.4.0"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909"}