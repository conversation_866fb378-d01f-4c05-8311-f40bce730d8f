ruoyi.name=RuoYi
ruoyi.version=2.3.0
ruoyi.copyrightYear=2019
ruoyi.demoEnabled=true
# �ļ�·�� ʾ���� Windows����D:/ruoyi/uploadPath��Linux���� /home/<USER>/uploadPath��
ruoyi.profile=D:/ruoyi/uploadPath
# ��ȡip��ַ����
ruoyi.addressEnabled=false

# ������������
# ��������HTTP�˿ڣ�Ĭ��Ϊ8080
server.port=8080
# Ӧ�õķ���·��
server.servlet.context-path=/
# tomcat��URI����
server.tomcat.uri-encoding=UTF-8
# tomcat����߳�����Ĭ��Ϊ200
server.tomcat.max-threads=800
# Tomcat������ʼ�����߳�����Ĭ��ֵ25
server.tomcat.min-spare-threads=30

# ��־����
logging.level.com.ruoyi=debug
logging.level.org.springframework=warn

# Spring����
# ��Դ��Ϣ
# ���ʻ���Դ�ļ�·��
spring.messages.basename=i18n/messages
#spring.profiles.active=druid
# �ļ��ϴ�
# �����ļ���С
spring.servlet.multipart.max-file-size=10MB
# �������ϴ����ļ���С
spring.servlet.multipart.max-request-size=20MB

# ����ģ��
# �Ȳ��𿪹�
spring.devtools.restart.enabled=true

# redis ����
spring.redis.host=*************
spring.redis.port=6379
spring.redis.password=wojinxaoRoot_wojin@8899

# ���ӳ�ʱʱ��
spring.redis.timeout=10s
# ���ӳ��е���С��������
spring.redis.lettuce.pool.min-idle=1
# ���ӳ��е�����������
spring.redis.lettuce.pool.max-idle=8
# ���ӳص�������ݿ�������
spring.redis.lettuce.pool.max-active=8
#���ӳ���������ȴ�ʱ�䣨ʹ�ø�ֵ��ʾû�����ƣ�
spring.redis.lettuce.pool.max-wait=-1ms

# token����
# �����Զ����ʶ
token.header=Authorization
# ������Կ
token.secret=abcdefghijklmnopqrstuvwxyz
# ������Ч�ڣ�Ĭ��30���ӣ�
token.expireTime=60

# MyBatis����
# ����ָ��������
mybatis.typeAliasesPackage=com.ruoyi.project.**.domain
# ����mapper��ɨ�裬�ҵ����е�mapper.xmlӳ���ļ�
mybatis.mapperLocations=classpath*:mybatis/**/*Mapper.xml
# ����ȫ�ֵ������ļ�
mybatis.configLocation=classpath:mybatis/mybatis-config.xml

# PageHelper��ҳ���
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql

# Swagger����
# �Ƿ���swagger
swagger.enabled=true
# ����ǰ׺
swagger.pathMapping=/dev-api

# ��ֹXSS����
# ���˿���
xss.enabled=true
# �ų����ӣ�����ö��ŷָ���
xss.excludes=/system/notice/*
# ƥ������
xss.urlPatterns=/system/*,/monitor/*,/tool/*

# ��������
# ����
gen.author=ruoyi
# Ĭ�����ɰ�·�� system ��ĳ��Լ���ģ������ �� system monitor tool
gen.packageName=com.ruoyi.project.business
# �Զ�ȥ����ǰ׺��Ĭ����true
gen.autoRemovePre=false
# ��ǰ׺�������������������ǰ׺������ö��ŷָ���
gen.tablePrefix=sys_

# �����⻧�˺�Ĭ�ϵ�����
# �˺�Ĭ��Ϊ�ֻ��ţ��������޸�
sys.default.pwd=123456.

#aliyun-oss�������,���ʹ�ð�����OSS�洢�ļ����滻��Ӧ���ü���
aliyun.oss.endpoint = http://oss-cn-shanghai.aliyuncs.com
aliyun.oss.accessKeyId = xxxxxxxxxx
aliyun.oss.accessKeySecret = xxxxxxxxxx
aliyun.oss.defaultBucketName = xxxxxxxxxx
aliyun.oss.endpointContent = oss-cn-shanghai.aliyuncs.com

#####################druid######################
# ����Դ����
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
# ��������Դ
spring.datasource.druid.master.url=*****************************************************************************************************************************************************************************************
spring.datasource.druid.master.username=xiaochengxv
spring.datasource.druid.master.password=wojinRoot_wojin@888

# �ӿ�����Դ
# ������Դ����/Ĭ�Ϲر�
spring.datasource.druid.slave.enabled=false
spring.datasource.druid.slave.url=
spring.datasource.druid.slave.username=
spring.datasource.druid.slave.password=
#####################druid######################
