const definitions = require("../src/definitions");
const flatMap = require("array.prototype.flatmap");
const { typeSignature, mapProps, iterateProps, unique } = require("./util");

const stdout = process.stdout;

function params(fields) {
  return mapProps(fields)
    .map(typeSignature)
    .join(",");
}

function generate() {
  stdout.write(`
    // @flow
    /* eslint no-unused-vars: off */

    // THIS FILE IS AUTOGENERATED
    // see scripts/generateTypeDefinitions.js
  `);

  // generate union types
  const unionTypes = unique(
    flatMap(mapProps(definitions).filter(d => d.unionType), d => d.unionType)
  );
  unionTypes.forEach(unionType => {
    stdout.write(
      `type ${unionType} = ` +
        mapProps(definitions)
          .filter(d => d.unionType && d.unionType.includes(unionType))
          .map(d => d.name)
          .join("|") +
        ";\n\n"
    );
  });

  // generate the type definitions
  iterateProps(definitions, typeDef => {
    stdout.write(`type ${typeDef.name} = {
        ...BaseNode,
        type: "${typeDef.name}",
        ${params(typeDef.fields)}
      };\n\n`);
  });
}

generate();
