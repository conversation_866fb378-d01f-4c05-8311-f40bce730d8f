{"name": "@babel/register", "version": "7.0.0", "description": "babel require hook", "license": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-register", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "browser": {"./lib/node.js": "./lib/browser.js"}, "dependencies": {"core-js": "^2.5.7", "find-cache-dir": "^1.0.0", "home-or-tmp": "^3.0.0", "lodash": "^4.17.10", "mkdirp": "^0.5.1", "pirates": "^4.0.0", "source-map-support": "^0.5.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0", "default-require-extensions": "^2.0.0"}}