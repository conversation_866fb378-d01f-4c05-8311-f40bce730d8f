{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperModuleTransforms", "_core", "_dynamicImport", "_lazy", "_hooks", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_api$assumption2", "_api$assumption3", "assertVersion", "strictNamespace", "mjsStrictNamespace", "allowTopLevelThis", "strict", "strictMode", "noInterop", "importInterop", "lazy", "allowCommonJSExports", "loose", "constantReexports", "assumption", "enumerableModuleMeta", "noIncompleteNsImportDetection", "Array", "isArray", "every", "item", "Error", "getAssertion", "localName", "template", "expression", "ast", "moduleExportsVisitor", "ReferencedIdentifier", "path", "node", "name", "localBinding", "scope", "getBinding", "rootBinding", "parentPath", "isObjectProperty", "value", "isObjectPattern", "isAssignmentExpression", "left", "replaceWith", "UpdateExpression", "arg", "get", "isIdentifier", "t", "assignmentExpression", "operator", "AssignmentExpression", "right", "sequenceExpression", "isPattern", "ids", "getOuterBindingIdentifiers", "Object", "keys", "find", "pre", "file", "set", "defineCommonJSHook", "lazyImportsHook", "visitor", "types", "importExpression", "has", "isCallExpression", "isImport", "callee", "rename", "parent", "transformDynamicImport", "Program", "exit", "state", "isModule", "traverse", "moduleName", "getModuleName", "opts", "stringLiteral", "hooks", "makeInvokers", "meta", "headers", "rewriteModuleStatementsAndPrepareHeader", "exportName", "wrapReference", "getWrapperPayload", "esNamespaceOnly", "filename", "test", "source", "metadata", "loadExpr", "callExpression", "identifier", "header", "isSideEffectImport", "wrap", "expressionStatement", "init", "wrapInterop", "interop", "res", "buildRequireWrapper", "referenced", "statement", "loc", "push", "buildNamespaceInitStatements", "ensureStatementsHoisted", "unshiftContainer", "for<PERSON>ach", "includes", "isVariableDeclaration", "registerDeclaration"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport {\n  isModule,\n  rewriteModuleStatementsAndPrepareHeader,\n  type RewriteModuleStatementsAndPrepareHeaderOptions,\n  isSideEffectImport,\n  buildNamespaceInitStatements,\n  ensureStatementsHoisted,\n  wrapInterop,\n  getModuleName,\n} from \"@babel/helper-module-transforms\";\nimport { template, types as t } from \"@babel/core\";\nimport type { PluginPass, Visitor, Scope, NodePath } from \"@babel/core\";\nimport type { PluginOptions } from \"@babel/helper-module-transforms\";\n\nimport { transformDynamicImport } from \"./dynamic-import.ts\";\nimport { lazyImportsHook } from \"./lazy.ts\";\n\nimport { defineCommonJSHook, makeInvokers } from \"./hooks.ts\";\nexport { defineCommonJSHook };\n\nexport interface Options extends PluginOptions {\n  allowCommonJSExports?: boolean;\n  allowTopLevelThis?: boolean;\n  importInterop?: RewriteModuleStatementsAndPrepareHeaderOptions[\"importInterop\"];\n  lazy?: RewriteModuleStatementsAndPrepareHeaderOptions[\"lazy\"];\n  loose?: boolean;\n  mjsStrictNamespace?: boolean;\n  noInterop?: boolean;\n  strict?: boolean;\n  strictMode?: boolean;\n  strictNamespace?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const {\n    // 'true' for imports to strictly have .default, instead of having\n    // destructuring-like behavior for their properties. This matches the behavior\n    // of the initial Node.js (v12) behavior when importing a CommonJS without\n    // the __esMoule property.\n    // .strictNamespace is for non-mjs files, mjsStrictNamespace if for mjs files.\n    strictNamespace = false,\n    mjsStrictNamespace = strictNamespace,\n\n    allowTopLevelThis,\n    strict,\n    strictMode,\n    noInterop,\n    importInterop,\n    lazy = false,\n    // Defaulting to 'true' for now. May change before 7.x major.\n    allowCommonJSExports = true,\n    loose = false,\n  } = options;\n\n  const constantReexports = api.assumption(\"constantReexports\") ?? loose;\n  const enumerableModuleMeta = api.assumption(\"enumerableModuleMeta\") ?? loose;\n  const noIncompleteNsImportDetection =\n    api.assumption(\"noIncompleteNsImportDetection\") ?? false;\n\n  if (\n    typeof lazy !== \"boolean\" &&\n    typeof lazy !== \"function\" &&\n    (!Array.isArray(lazy) || !lazy.every(item => typeof item === \"string\"))\n  ) {\n    throw new Error(`.lazy must be a boolean, array of strings, or a function`);\n  }\n\n  if (typeof strictNamespace !== \"boolean\") {\n    throw new Error(`.strictNamespace must be a boolean, or undefined`);\n  }\n  if (typeof mjsStrictNamespace !== \"boolean\") {\n    throw new Error(`.mjsStrictNamespace must be a boolean, or undefined`);\n  }\n\n  const getAssertion = (localName: string) => template.expression.ast`\n    (function(){\n      throw new Error(\n        \"The CommonJS '\" + \"${localName}\" + \"' variable is not available in ES6 modules.\" +\n        \"Consider setting setting sourceType:script or sourceType:unambiguous in your \" +\n        \"Babel config for this file.\");\n    })()\n  `;\n\n  const moduleExportsVisitor: Visitor<{ scope: Scope }> = {\n    ReferencedIdentifier(path) {\n      const localName = path.node.name;\n      if (localName !== \"module\" && localName !== \"exports\") return;\n\n      const localBinding = path.scope.getBinding(localName);\n      const rootBinding = this.scope.getBinding(localName);\n\n      if (\n        // redeclared in this scope\n        rootBinding !== localBinding ||\n        (path.parentPath.isObjectProperty({ value: path.node }) &&\n          path.parentPath.parentPath.isObjectPattern()) ||\n        path.parentPath.isAssignmentExpression({ left: path.node }) ||\n        path.isAssignmentExpression({ left: path.node })\n      ) {\n        return;\n      }\n\n      path.replaceWith(getAssertion(localName));\n    },\n\n    UpdateExpression(path) {\n      const arg = path.get(\"argument\");\n      if (!arg.isIdentifier()) return;\n      const localName = arg.node.name;\n      if (localName !== \"module\" && localName !== \"exports\") return;\n\n      const localBinding = path.scope.getBinding(localName);\n      const rootBinding = this.scope.getBinding(localName);\n\n      // redeclared in this scope\n      if (rootBinding !== localBinding) return;\n\n      path.replaceWith(\n        t.assignmentExpression(\n          // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n          (path.node.operator[0] + \"=\") as t.AssignmentExpression[\"operator\"],\n          arg.node,\n          getAssertion(localName),\n        ),\n      );\n    },\n\n    AssignmentExpression(path) {\n      const left = path.get(\"left\");\n      if (left.isIdentifier()) {\n        const localName = left.node.name;\n        if (localName !== \"module\" && localName !== \"exports\") return;\n\n        const localBinding = path.scope.getBinding(localName);\n        const rootBinding = this.scope.getBinding(localName);\n\n        // redeclared in this scope\n        if (rootBinding !== localBinding) return;\n\n        const right = path.get(\"right\");\n        right.replaceWith(\n          t.sequenceExpression([right.node, getAssertion(localName)]),\n        );\n      } else if (left.isPattern()) {\n        const ids = left.getOuterBindingIdentifiers();\n        const localName = Object.keys(ids).find(localName => {\n          if (localName !== \"module\" && localName !== \"exports\") return false;\n\n          return (\n            this.scope.getBinding(localName) ===\n            path.scope.getBinding(localName)\n          );\n        });\n\n        if (localName) {\n          const right = path.get(\"right\");\n          right.replaceWith(\n            t.sequenceExpression([right.node, getAssertion(localName)]),\n          );\n        }\n      }\n    },\n  };\n\n  return {\n    name: \"transform-modules-commonjs\",\n\n    pre() {\n      this.file.set(\"@babel/plugin-transform-modules-*\", \"commonjs\");\n\n      if (lazy) defineCommonJSHook(this.file, lazyImportsHook(lazy));\n    },\n\n    visitor: {\n      [\"CallExpression\" +\n        (api.types.importExpression ? \"|ImportExpression\" : \"\")](\n        this: PluginPass,\n        path: NodePath<t.CallExpression | t.ImportExpression>,\n      ) {\n        if (!this.file.has(\"@babel/plugin-proposal-dynamic-import\")) return;\n        if (path.isCallExpression() && !t.isImport(path.node.callee)) return;\n\n        let { scope } = path;\n        do {\n          scope.rename(\"require\");\n        } while ((scope = scope.parent));\n\n        transformDynamicImport(path, noInterop, this.file);\n      },\n\n      Program: {\n        exit(path, state) {\n          if (!isModule(path)) return;\n\n          // Rename the bindings auto-injected into the scope so there is no\n          // risk of conflict between the bindings.\n          path.scope.rename(\"exports\");\n          path.scope.rename(\"module\");\n          path.scope.rename(\"require\");\n          path.scope.rename(\"__filename\");\n          path.scope.rename(\"__dirname\");\n\n          // Rewrite references to 'module' and 'exports' to throw exceptions.\n          // These objects are specific to CommonJS and are not available in\n          // real ES6 implementations.\n          if (!allowCommonJSExports) {\n            path.traverse(moduleExportsVisitor, {\n              scope: path.scope,\n            });\n          }\n\n          let moduleName = getModuleName(this.file.opts, options);\n          // @ts-expect-error todo(flow->ts): do not reuse variables\n          if (moduleName) moduleName = t.stringLiteral(moduleName);\n\n          const hooks = makeInvokers(this.file);\n\n          const { meta, headers } = rewriteModuleStatementsAndPrepareHeader(\n            path,\n            {\n              exportName: \"exports\",\n              constantReexports,\n              enumerableModuleMeta,\n              strict,\n              strictMode,\n              allowTopLevelThis,\n              noInterop,\n              importInterop,\n              wrapReference: hooks.wrapReference,\n              getWrapperPayload: hooks.getWrapperPayload,\n              esNamespaceOnly:\n                typeof state.filename === \"string\" &&\n                /\\.mjs$/.test(state.filename)\n                  ? mjsStrictNamespace\n                  : strictNamespace,\n              noIncompleteNsImportDetection,\n              filename: this.file.opts.filename,\n            },\n          );\n\n          for (const [source, metadata] of meta.source) {\n            const loadExpr = t.callExpression(t.identifier(\"require\"), [\n              t.stringLiteral(source),\n            ]);\n\n            let header: t.Statement;\n            if (isSideEffectImport(metadata)) {\n              if (lazy && metadata.wrap === \"function\") {\n                throw new Error(\"Assertion failure\");\n              }\n\n              header = t.expressionStatement(loadExpr);\n            } else {\n              const init =\n                wrapInterop(path, loadExpr, metadata.interop) || loadExpr;\n\n              if (metadata.wrap) {\n                const res = hooks.buildRequireWrapper(\n                  metadata.name,\n                  init,\n                  metadata.wrap,\n                  metadata.referenced,\n                );\n                if (res === false) continue;\n                else header = res;\n              }\n              header ??= template.statement.ast`\n                var ${metadata.name} = ${init};\n              `;\n            }\n            header.loc = metadata.loc;\n\n            headers.push(header);\n            headers.push(\n              ...buildNamespaceInitStatements(\n                meta,\n                metadata,\n                constantReexports,\n                hooks.wrapReference,\n              ),\n            );\n          }\n\n          ensureStatementsHoisted(headers);\n          path.unshiftContainer(\"body\", headers);\n          path.get(\"body\").forEach(path => {\n            if (!headers.includes(path.node)) return;\n            if (path.isVariableDeclaration()) {\n              path.scope.registerDeclaration(path);\n            }\n          });\n        },\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AAUA,IAAAE,KAAA,GAAAF,OAAA;AAIA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AAEA,IAAAK,MAAA,GAAAL,OAAA;AAA8D,IAAAM,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAgB/C,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;EAChDJ,GAAG,CAACK,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAM;IAMJC,eAAe,GAAG,KAAK;IACvBC,kBAAkB,GAAGD,eAAe;IAEpCE,iBAAiB;IACjBC,MAAM;IACNC,UAAU;IACVC,SAAS;IACTC,aAAa;IACbC,IAAI,GAAG,KAAK;IAEZC,oBAAoB,GAAG,IAAI;IAC3BC,KAAK,GAAG;EACV,CAAC,GAAGd,OAAO;EAEX,MAAMe,iBAAiB,IAAAd,eAAA,GAAGF,GAAG,CAACiB,UAAU,CAAC,mBAAmB,CAAC,YAAAf,eAAA,GAAIa,KAAK;EACtE,MAAMG,oBAAoB,IAAAf,gBAAA,GAAGH,GAAG,CAACiB,UAAU,CAAC,sBAAsB,CAAC,YAAAd,gBAAA,GAAIY,KAAK;EAC5E,MAAMI,6BAA6B,IAAAf,gBAAA,GACjCJ,GAAG,CAACiB,UAAU,CAAC,+BAA+B,CAAC,YAAAb,gBAAA,GAAI,KAAK;EAE1D,IACE,OAAOS,IAAI,KAAK,SAAS,IACzB,OAAOA,IAAI,KAAK,UAAU,KACzB,CAACO,KAAK,CAACC,OAAO,CAACR,IAAI,CAAC,IAAI,CAACA,IAAI,CAACS,KAAK,CAACC,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,CAAC,CAAC,EACvE;IACA,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EAEA,IAAI,OAAOlB,eAAe,KAAK,SAAS,EAAE;IACxC,MAAM,IAAIkB,KAAK,CAAC,kDAAkD,CAAC;EACrE;EACA,IAAI,OAAOjB,kBAAkB,KAAK,SAAS,EAAE;IAC3C,MAAM,IAAIiB,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAMC,YAAY,GAAIC,SAAiB,IAAKC,cAAQ,CAACC,UAAU,CAACC,GAAG;AACrE;AACA;AACA,8BAA8BH,SAAS;AACvC;AACA;AACA;AACA,GAAG;EAED,MAAMI,oBAA+C,GAAG;IACtDC,oBAAoBA,CAACC,IAAI,EAAE;MACzB,MAAMN,SAAS,GAAGM,IAAI,CAACC,IAAI,CAACC,IAAI;MAChC,IAAIR,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,SAAS,EAAE;MAEvD,MAAMS,YAAY,GAAGH,IAAI,CAACI,KAAK,CAACC,UAAU,CAACX,SAAS,CAAC;MACrD,MAAMY,WAAW,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACX,SAAS,CAAC;MAEpD,IAEEY,WAAW,KAAKH,YAAY,IAC3BH,IAAI,CAACO,UAAU,CAACC,gBAAgB,CAAC;QAAEC,KAAK,EAAET,IAAI,CAACC;MAAK,CAAC,CAAC,IACrDD,IAAI,CAACO,UAAU,CAACA,UAAU,CAACG,eAAe,CAAC,CAAE,IAC/CV,IAAI,CAACO,UAAU,CAACI,sBAAsB,CAAC;QAAEC,IAAI,EAAEZ,IAAI,CAACC;MAAK,CAAC,CAAC,IAC3DD,IAAI,CAACW,sBAAsB,CAAC;QAAEC,IAAI,EAAEZ,IAAI,CAACC;MAAK,CAAC,CAAC,EAChD;QACA;MACF;MAEAD,IAAI,CAACa,WAAW,CAACpB,YAAY,CAACC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAEDoB,gBAAgBA,CAACd,IAAI,EAAE;MACrB,MAAMe,GAAG,GAAGf,IAAI,CAACgB,GAAG,CAAC,UAAU,CAAC;MAChC,IAAI,CAACD,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE;MACzB,MAAMvB,SAAS,GAAGqB,GAAG,CAACd,IAAI,CAACC,IAAI;MAC/B,IAAIR,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,SAAS,EAAE;MAEvD,MAAMS,YAAY,GAAGH,IAAI,CAACI,KAAK,CAACC,UAAU,CAACX,SAAS,CAAC;MACrD,MAAMY,WAAW,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACX,SAAS,CAAC;MAGpD,IAAIY,WAAW,KAAKH,YAAY,EAAE;MAElCH,IAAI,CAACa,WAAW,CACdK,WAAC,CAACC,oBAAoB,CAEnBnB,IAAI,CAACC,IAAI,CAACmB,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,EAC5BL,GAAG,CAACd,IAAI,EACRR,YAAY,CAACC,SAAS,CACxB,CACF,CAAC;IACH,CAAC;IAED2B,oBAAoBA,CAACrB,IAAI,EAAE;MACzB,MAAMY,IAAI,GAAGZ,IAAI,CAACgB,GAAG,CAAC,MAAM,CAAC;MAC7B,IAAIJ,IAAI,CAACK,YAAY,CAAC,CAAC,EAAE;QACvB,MAAMvB,SAAS,GAAGkB,IAAI,CAACX,IAAI,CAACC,IAAI;QAChC,IAAIR,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,SAAS,EAAE;QAEvD,MAAMS,YAAY,GAAGH,IAAI,CAACI,KAAK,CAACC,UAAU,CAACX,SAAS,CAAC;QACrD,MAAMY,WAAW,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACX,SAAS,CAAC;QAGpD,IAAIY,WAAW,KAAKH,YAAY,EAAE;QAElC,MAAMmB,KAAK,GAAGtB,IAAI,CAACgB,GAAG,CAAC,OAAO,CAAC;QAC/BM,KAAK,CAACT,WAAW,CACfK,WAAC,CAACK,kBAAkB,CAAC,CAACD,KAAK,CAACrB,IAAI,EAAER,YAAY,CAACC,SAAS,CAAC,CAAC,CAC5D,CAAC;MACH,CAAC,MAAM,IAAIkB,IAAI,CAACY,SAAS,CAAC,CAAC,EAAE;QAC3B,MAAMC,GAAG,GAAGb,IAAI,CAACc,0BAA0B,CAAC,CAAC;QAC7C,MAAMhC,SAAS,GAAGiC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAACI,IAAI,CAACnC,SAAS,IAAI;UACnD,IAAIA,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,SAAS,EAAE,OAAO,KAAK;UAEnE,OACE,IAAI,CAACU,KAAK,CAACC,UAAU,CAACX,SAAS,CAAC,KAChCM,IAAI,CAACI,KAAK,CAACC,UAAU,CAACX,SAAS,CAAC;QAEpC,CAAC,CAAC;QAEF,IAAIA,SAAS,EAAE;UACb,MAAM4B,KAAK,GAAGtB,IAAI,CAACgB,GAAG,CAAC,OAAO,CAAC;UAC/BM,KAAK,CAACT,WAAW,CACfK,WAAC,CAACK,kBAAkB,CAAC,CAACD,KAAK,CAACrB,IAAI,EAAER,YAAY,CAACC,SAAS,CAAC,CAAC,CAC5D,CAAC;QACH;MACF;IACF;EACF,CAAC;EAED,OAAO;IACLQ,IAAI,EAAE,4BAA4B;IAElC4B,GAAGA,CAAA,EAAG;MACJ,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,mCAAmC,EAAE,UAAU,CAAC;MAE9D,IAAInD,IAAI,EAAE,IAAAoD,yBAAkB,EAAC,IAAI,CAACF,IAAI,EAAE,IAAAG,qBAAe,EAACrD,IAAI,CAAC,CAAC;IAChE,CAAC;IAEDsD,OAAO,EAAE;MACP,CAAC,gBAAgB,IACdnE,GAAG,CAACoE,KAAK,CAACC,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,CAAC,EAEvDrC,IAAqD,EACrD;QACA,IAAI,CAAC,IAAI,CAAC+B,IAAI,CAACO,GAAG,CAAC,uCAAuC,CAAC,EAAE;QAC7D,IAAItC,IAAI,CAACuC,gBAAgB,CAAC,CAAC,IAAI,CAACrB,WAAC,CAACsB,QAAQ,CAACxC,IAAI,CAACC,IAAI,CAACwC,MAAM,CAAC,EAAE;QAE9D,IAAI;UAAErC;QAAM,CAAC,GAAGJ,IAAI;QACpB,GAAG;UACDI,KAAK,CAACsC,MAAM,CAAC,SAAS,CAAC;QACzB,CAAC,QAAStC,KAAK,GAAGA,KAAK,CAACuC,MAAM;QAE9B,IAAAC,qCAAsB,EAAC5C,IAAI,EAAErB,SAAS,EAAE,IAAI,CAACoD,IAAI,CAAC;MACpD,CAAC;MAEDc,OAAO,EAAE;QACPC,IAAIA,CAAC9C,IAAI,EAAE+C,KAAK,EAAE;UAChB,IAAI,CAAC,IAAAC,gCAAQ,EAAChD,IAAI,CAAC,EAAE;UAIrBA,IAAI,CAACI,KAAK,CAACsC,MAAM,CAAC,SAAS,CAAC;UAC5B1C,IAAI,CAACI,KAAK,CAACsC,MAAM,CAAC,QAAQ,CAAC;UAC3B1C,IAAI,CAACI,KAAK,CAACsC,MAAM,CAAC,SAAS,CAAC;UAC5B1C,IAAI,CAACI,KAAK,CAACsC,MAAM,CAAC,YAAY,CAAC;UAC/B1C,IAAI,CAACI,KAAK,CAACsC,MAAM,CAAC,WAAW,CAAC;UAK9B,IAAI,CAAC5D,oBAAoB,EAAE;YACzBkB,IAAI,CAACiD,QAAQ,CAACnD,oBAAoB,EAAE;cAClCM,KAAK,EAAEJ,IAAI,CAACI;YACd,CAAC,CAAC;UACJ;UAEA,IAAI8C,UAAU,GAAG,IAAAC,qCAAa,EAAC,IAAI,CAACpB,IAAI,CAACqB,IAAI,EAAEnF,OAAO,CAAC;UAEvD,IAAIiF,UAAU,EAAEA,UAAU,GAAGhC,WAAC,CAACmC,aAAa,CAACH,UAAU,CAAC;UAExD,MAAMI,KAAK,GAAG,IAAAC,mBAAY,EAAC,IAAI,CAACxB,IAAI,CAAC;UAErC,MAAM;YAAEyB,IAAI;YAAEC;UAAQ,CAAC,GAAG,IAAAC,+DAAuC,EAC/D1D,IAAI,EACJ;YACE2D,UAAU,EAAE,SAAS;YACrB3E,iBAAiB;YACjBE,oBAAoB;YACpBT,MAAM;YACNC,UAAU;YACVF,iBAAiB;YACjBG,SAAS;YACTC,aAAa;YACbgF,aAAa,EAAEN,KAAK,CAACM,aAAa;YAClCC,iBAAiB,EAAEP,KAAK,CAACO,iBAAiB;YAC1CC,eAAe,EACb,OAAOf,KAAK,CAACgB,QAAQ,KAAK,QAAQ,IAClC,QAAQ,CAACC,IAAI,CAACjB,KAAK,CAACgB,QAAQ,CAAC,GACzBxF,kBAAkB,GAClBD,eAAe;YACrBa,6BAA6B;YAC7B4E,QAAQ,EAAE,IAAI,CAAChC,IAAI,CAACqB,IAAI,CAACW;UAC3B,CACF,CAAC;UAED,KAAK,MAAM,CAACE,MAAM,EAAEC,QAAQ,CAAC,IAAIV,IAAI,CAACS,MAAM,EAAE;YAC5C,MAAME,QAAQ,GAAGjD,WAAC,CAACkD,cAAc,CAAClD,WAAC,CAACmD,UAAU,CAAC,SAAS,CAAC,EAAE,CACzDnD,WAAC,CAACmC,aAAa,CAACY,MAAM,CAAC,CACxB,CAAC;YAEF,IAAIK,MAAmB;YACvB,IAAI,IAAAC,0CAAkB,EAACL,QAAQ,CAAC,EAAE;cAChC,IAAIrF,IAAI,IAAIqF,QAAQ,CAACM,IAAI,KAAK,UAAU,EAAE;gBACxC,MAAM,IAAIhF,KAAK,CAAC,mBAAmB,CAAC;cACtC;cAEA8E,MAAM,GAAGpD,WAAC,CAACuD,mBAAmB,CAACN,QAAQ,CAAC;YAC1C,CAAC,MAAM;cACL,MAAMO,IAAI,GACR,IAAAC,mCAAW,EAAC3E,IAAI,EAAEmE,QAAQ,EAAED,QAAQ,CAACU,OAAO,CAAC,IAAIT,QAAQ;cAE3D,IAAID,QAAQ,CAACM,IAAI,EAAE;gBACjB,MAAMK,GAAG,GAAGvB,KAAK,CAACwB,mBAAmB,CACnCZ,QAAQ,CAAChE,IAAI,EACbwE,IAAI,EACJR,QAAQ,CAACM,IAAI,EACbN,QAAQ,CAACa,UACX,CAAC;gBACD,IAAIF,GAAG,KAAK,KAAK,EAAE,SAAS,KACvBP,MAAM,GAAGO,GAAG;cACnB;cACAP,MAAM,WAANA,MAAM,GAANA,MAAM,GAAK3E,cAAQ,CAACqF,SAAS,CAACnF,GAAG;AAC/C,sBAAsBqE,QAAQ,CAAChE,IAAI,MAAMwE,IAAI;AAC7C,eAAe;YACH;YACAJ,MAAM,CAACW,GAAG,GAAGf,QAAQ,CAACe,GAAG;YAEzBxB,OAAO,CAACyB,IAAI,CAACZ,MAAM,CAAC;YACpBb,OAAO,CAACyB,IAAI,CACV,GAAG,IAAAC,oDAA4B,EAC7B3B,IAAI,EACJU,QAAQ,EACRlF,iBAAiB,EACjBsE,KAAK,CAACM,aACR,CACF,CAAC;UACH;UAEA,IAAAwB,+CAAuB,EAAC3B,OAAO,CAAC;UAChCzD,IAAI,CAACqF,gBAAgB,CAAC,MAAM,EAAE5B,OAAO,CAAC;UACtCzD,IAAI,CAACgB,GAAG,CAAC,MAAM,CAAC,CAACsE,OAAO,CAACtF,IAAI,IAAI;YAC/B,IAAI,CAACyD,OAAO,CAAC8B,QAAQ,CAACvF,IAAI,CAACC,IAAI,CAAC,EAAE;YAClC,IAAID,IAAI,CAACwF,qBAAqB,CAAC,CAAC,EAAE;cAChCxF,IAAI,CAACI,KAAK,CAACqF,mBAAmB,CAACzF,IAAI,CAAC;YACtC;UACF,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}