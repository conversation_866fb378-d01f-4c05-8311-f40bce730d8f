var _Object$keys = require("core-js/library/fn/object/keys.js");
var _Object$getOwnPropertySymbols = require("core-js/library/fn/object/get-own-property-symbols.js");
var _Object$getOwnPropertyDescriptor = require("core-js/library/fn/object/get-own-property-descriptor.js");
var defineProperty = require("./defineProperty.js");
function _objectSpread(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? Object(arguments[r]) : {},
      o = _Object$keys(t);
    "function" == typeof _Object$getOwnPropertySymbols && o.push.apply(o, _Object$getOwnPropertySymbols(t).filter(function (e) {
      return _Object$getOwnPropertyDescriptor(t, e).enumerable;
    })), o.forEach(function (r) {
      defineProperty(e, r, t[r]);
    });
  }
  return e;
}
module.exports = _objectSpread, module.exports.__esModule = true, module.exports["default"] = module.exports;