{"name": "@xtuc/long", "version": "4.2.1", "author": "<PERSON> <<EMAIL>>", "description": "A Long class for representing a 64-bit two's-complement integer value.", "module": "src/long.js", "main": "dist/long.cjs.js", "repository": {"type": "git", "url": "https://github.com/dcodeIO/long.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/long.js/issues"}, "keywords": ["math"], "dependencies": {}, "devDependencies": {"@babel/cli": "^7.0.0-beta.54", "@babel/core": "^7.0.0-beta.54", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.54", "webpack": "^3.10.0"}, "license": "Apache-2.0", "scripts": {"build": "webpack", "test": "node tests"}, "files": ["index.js", "LICENSE", "README.md", "src/long.js", "dist/long.js", "dist/long.js.map", "index.d.ts"], "types": "index.d.ts", "prepublish": "babel --plugins @babel/plugin-transform-modules-commonjs src/long.js -o dist/long.cjs.js"}