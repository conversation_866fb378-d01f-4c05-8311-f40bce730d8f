{"name": "@webassemblyjs/ast", "version": "1.7.11", "description": "AST utils for webassemblyjs", "keywords": ["webassembly", "javascript", "ast"], "main": "lib/index.js", "module": "esm/index.js", "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/helper-module-context": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/wast-parser": "1.7.11"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.11", "array.prototype.flatmap": "^1.2.1", "dump-exports": "^0.1.0", "mamacro": "^0.0.3"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909"}