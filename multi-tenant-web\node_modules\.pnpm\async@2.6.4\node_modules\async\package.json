{"name": "async", "description": "Higher-order functions and common patterns for asynchronous code", "version": "2.6.4", "main": "dist/async.js", "author": "<PERSON><PERSON>", "homepage": "https://caolan.github.io/async/", "repository": {"type": "git", "url": "https://github.com/caolan/async.git"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "keywords": ["async", "callback", "module", "utility"], "dependencies": {"lodash": "^4.17.14"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-core": "^6.26.3", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-istanbul": "^2.0.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "babelify": "^8.0.0", "benchmark": "^2.1.1", "bluebird": "^3.4.6", "browserify": "^16.2.2", "chai": "^4.1.2", "cheerio": "^0.22.0", "coveralls": "^3.0.1", "es6-promise": "^2.3.0", "eslint": "^2.13.1", "fs-extra": "^0.26.7", "gh-pages-deploy": "^0.5.0", "jsdoc": "^3.4.0", "karma": "^2.0.2", "karma-browserify": "^5.2.0", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha": "^5.2.0", "native-promise-only": "^0.8.0-a", "nyc": "^11.8.0", "rimraf": "^2.5.0", "rollup": "^0.36.3", "rollup-plugin-node-resolve": "^2.0.0", "rollup-plugin-npm": "^2.0.0", "rsvp": "^3.0.18", "semver": "^5.5.0", "uglify-js": "~2.7.3", "yargs": "^11.0.0"}, "scripts": {"coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "mocha-browser-test": "karma start", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "test": "npm run lint && npm run mocha-node-test"}, "license": "MIT", "gh-pages-deploy": {"staticpath": "docs"}, "nyc": {"exclude": ["mocha_test"]}}