{"version": 3, "names": ["_core", "require", "transformWithoutHelper", "loose", "path", "state", "pushComputedProps", "pushComputedPropsLoose", "pushComputedPropsSpec", "node", "build", "declar", "loop", "block", "body", "ensureBlock", "push", "t", "inherits", "replaceParent", "parentPath", "replaceWithMultiple", "remove", "buildForOfLoose", "template", "statement", "buildForOf", "statements", "scope", "parent", "left", "id", "intermediate", "isIdentifier", "isPattern", "isMemberExpression", "isVariableDeclaration", "generateUidIdentifier", "variableDeclaration", "kind", "variableDeclarator", "declarations", "identifier", "name", "buildCodeFrameError", "type", "iteratorKey", "isArray<PERSON>ey", "LOOP_OBJECT", "IS_ARRAY", "OBJECT", "right", "INDEX", "ID", "INTERMEDIATE", "isLabeledParent", "isLabeledStatement", "labeled", "labeledStatement", "label", "<PERSON><PERSON><PERSON>", "generateUid", "<PERSON><PERSON><PERSON><PERSON>", "memberExpression", "expressionStatement", "assignmentExpression", "ITERATOR_HAD_ERROR_KEY", "ITERATOR_COMPLETION", "ITERATOR_ERROR_KEY", "ITERATOR_KEY", "STEP_KEY", "tryBody"], "sources": ["../src/no-helper-implementation.ts"], "sourcesContent": ["if (process.env.BABEL_8_BREAKING && process.env.IS_PUBLISH) {\n  throw new Error(\n    \"Internal Babel error: This file should only be loaded in Babel 7\",\n  );\n}\n\nimport { template, types as t } from \"@babel/core\";\nimport type { PluginPass, NodePath } from \"@babel/core\";\n\n// This is the legacy implementation, which inlines all the code.\n// It must be kept for compatibility reasons.\n// TODO(Babel 8): Remove this file.\n\nexport default function transformWithoutHelper(\n  loose: boolean | void,\n  path: NodePath<t.ForOfStatement>,\n  state: PluginPass,\n) {\n  const pushComputedProps = loose\n    ? pushComputedPropsLoose\n    : pushComputedPropsSpec;\n\n  const { node } = path;\n  const build = pushComputedProps(path, state);\n  const declar = build.declar;\n  const loop = build.loop;\n  const block = loop.body as t.BlockStatement;\n\n  // ensure that it's a block so we can take all its statements\n  path.ensureBlock();\n\n  // add the value declaration to the new loop body\n  if (declar) {\n    block.body.push(declar);\n  }\n\n  // push the rest of the original loop body onto our new body\n  block.body.push(...(node.body as t.BlockStatement).body);\n\n  t.inherits(loop, node);\n  t.inherits(loop.body, node.body);\n\n  if (build.replaceParent) {\n    path.parentPath.replaceWithMultiple(build.node);\n    path.remove();\n  } else {\n    path.replaceWithMultiple(build.node);\n  }\n}\n\nconst buildForOfLoose = template.statement(`\n  for (var LOOP_OBJECT = OBJECT,\n          IS_ARRAY = Array.isArray(LOOP_OBJECT),\n          INDEX = 0,\n          LOOP_OBJECT = IS_ARRAY ? LOOP_OBJECT : LOOP_OBJECT[Symbol.iterator]();;) {\n    INTERMEDIATE;\n    if (IS_ARRAY) {\n      if (INDEX >= LOOP_OBJECT.length) break;\n      ID = LOOP_OBJECT[INDEX++];\n    } else {\n      INDEX = LOOP_OBJECT.next();\n      if (INDEX.done) break;\n      ID = INDEX.value;\n    }\n  }\n`);\n\nconst buildForOf = template.statements(`\n  var ITERATOR_COMPLETION = true;\n  var ITERATOR_HAD_ERROR_KEY = false;\n  var ITERATOR_ERROR_KEY = undefined;\n  try {\n    for (\n      var ITERATOR_KEY = OBJECT[Symbol.iterator](), STEP_KEY;\n      !(ITERATOR_COMPLETION = (STEP_KEY = ITERATOR_KEY.next()).done);\n      ITERATOR_COMPLETION = true\n    ) {}\n  } catch (err) {\n    ITERATOR_HAD_ERROR_KEY = true;\n    ITERATOR_ERROR_KEY = err;\n  } finally {\n    try {\n      if (!ITERATOR_COMPLETION && ITERATOR_KEY.return != null) {\n        ITERATOR_KEY.return();\n      }\n    } finally {\n      if (ITERATOR_HAD_ERROR_KEY) {\n        throw ITERATOR_ERROR_KEY;\n      }\n    }\n  }\n`);\n\nfunction pushComputedPropsLoose(\n  path: NodePath<t.ForOfStatement>,\n  state: PluginPass,\n) {\n  const { node, scope, parent } = path;\n  const { left } = node;\n  let declar, id, intermediate;\n\n  if (t.isIdentifier(left) || t.isPattern(left) || t.isMemberExpression(left)) {\n    // for (i of test), for ({ i } of test)\n    id = left;\n    intermediate = null;\n  } else if (t.isVariableDeclaration(left)) {\n    // for (let i of test)\n    id = scope.generateUidIdentifier(\"ref\");\n    declar = t.variableDeclaration(left.kind, [\n      t.variableDeclarator(left.declarations[0].id, t.identifier(id.name)),\n    ]);\n    intermediate = t.variableDeclaration(\"var\", [\n      t.variableDeclarator(t.identifier(id.name)),\n    ]);\n  } else {\n    throw state.buildCodeFrameError(\n      left,\n      `Unknown node type ${left.type} in ForStatement`,\n    );\n  }\n\n  const iteratorKey = scope.generateUidIdentifier(\"iterator\");\n  const isArrayKey = scope.generateUidIdentifier(\"isArray\");\n\n  const loop = buildForOfLoose({\n    LOOP_OBJECT: iteratorKey,\n    IS_ARRAY: isArrayKey,\n    OBJECT: node.right,\n    INDEX: scope.generateUidIdentifier(\"i\"),\n    ID: id,\n    INTERMEDIATE: intermediate,\n  }) as t.ForStatement;\n\n  //\n  const isLabeledParent = t.isLabeledStatement(parent);\n  let labeled;\n\n  if (isLabeledParent) {\n    labeled = t.labeledStatement(parent.label, loop);\n  }\n\n  return {\n    replaceParent: isLabeledParent,\n    declar: declar,\n    node: labeled || loop,\n    loop: loop,\n  };\n}\n\nfunction pushComputedPropsSpec(\n  path: NodePath<t.ForOfStatement>,\n  state: PluginPass,\n) {\n  const { node, scope, parent } = path;\n  const left = node.left;\n  let declar;\n\n  const stepKey = scope.generateUid(\"step\");\n  const stepValue = t.memberExpression(\n    t.identifier(stepKey),\n    t.identifier(\"value\"),\n  );\n\n  if (t.isIdentifier(left) || t.isPattern(left) || t.isMemberExpression(left)) {\n    // for (i of test), for ({ i } of test)\n    declar = t.expressionStatement(\n      t.assignmentExpression(\"=\", left, stepValue),\n    );\n  } else if (t.isVariableDeclaration(left)) {\n    // for (let i of test)\n    declar = t.variableDeclaration(left.kind, [\n      t.variableDeclarator(left.declarations[0].id, stepValue),\n    ]);\n  } else {\n    throw state.buildCodeFrameError(\n      left,\n      `Unknown node type ${left.type} in ForStatement`,\n    );\n  }\n\n  const template = buildForOf({\n    ITERATOR_HAD_ERROR_KEY: scope.generateUidIdentifier(\"didIteratorError\"),\n    ITERATOR_COMPLETION: scope.generateUidIdentifier(\n      \"iteratorNormalCompletion\",\n    ),\n    ITERATOR_ERROR_KEY: scope.generateUidIdentifier(\"iteratorError\"),\n    ITERATOR_KEY: scope.generateUidIdentifier(\"iterator\"),\n    STEP_KEY: t.identifier(stepKey),\n    OBJECT: node.right,\n  });\n\n  const isLabeledParent = t.isLabeledStatement(parent);\n\n  const tryBody = (template[3] as t.TryStatement).block.body;\n  const loop = tryBody[0] as t.ForStatement;\n\n  if (isLabeledParent) {\n    tryBody[0] = t.labeledStatement(parent.label, loop);\n  }\n\n  //\n\n  return {\n    replaceParent: isLabeledParent,\n    declar: declar,\n    loop: loop,\n    node: template,\n  };\n}\n"], "mappings": ";;;;;;AAMA,IAAAA,KAAA,GAAAC,OAAA;AAAmD;AAOpC,SAASC,sBAAsBA,CAC5CC,KAAqB,EACrBC,IAAgC,EAChCC,KAAiB,EACjB;EACA,MAAMC,iBAAiB,GAAGH,KAAK,GAC3BI,sBAAsB,GACtBC,qBAAqB;EAEzB,MAAM;IAAEC;EAAK,CAAC,GAAGL,IAAI;EACrB,MAAMM,KAAK,GAAGJ,iBAAiB,CAACF,IAAI,EAAEC,KAAK,CAAC;EAC5C,MAAMM,MAAM,GAAGD,KAAK,CAACC,MAAM;EAC3B,MAAMC,IAAI,GAAGF,KAAK,CAACE,IAAI;EACvB,MAAMC,KAAK,GAAGD,IAAI,CAACE,IAAwB;EAG3CV,IAAI,CAACW,WAAW,CAAC,CAAC;EAGlB,IAAIJ,MAAM,EAAE;IACVE,KAAK,CAACC,IAAI,CAACE,IAAI,CAACL,MAAM,CAAC;EACzB;EAGAE,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC,GAAIP,IAAI,CAACK,IAAI,CAAsBA,IAAI,CAAC;EAExDG,WAAC,CAACC,QAAQ,CAACN,IAAI,EAAEH,IAAI,CAAC;EACtBQ,WAAC,CAACC,QAAQ,CAACN,IAAI,CAACE,IAAI,EAAEL,IAAI,CAACK,IAAI,CAAC;EAEhC,IAAIJ,KAAK,CAACS,aAAa,EAAE;IACvBf,IAAI,CAACgB,UAAU,CAACC,mBAAmB,CAACX,KAAK,CAACD,IAAI,CAAC;IAC/CL,IAAI,CAACkB,MAAM,CAAC,CAAC;EACf,CAAC,MAAM;IACLlB,IAAI,CAACiB,mBAAmB,CAACX,KAAK,CAACD,IAAI,CAAC;EACtC;AACF;AAEA,MAAMc,eAAe,GAAGC,cAAQ,CAACC,SAAS,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEF,MAAMC,UAAU,GAAGF,cAAQ,CAACG,UAAU,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEF,SAASpB,sBAAsBA,CAC7BH,IAAgC,EAChCC,KAAiB,EACjB;EACA,MAAM;IAAEI,IAAI;IAAEmB,KAAK;IAAEC;EAAO,CAAC,GAAGzB,IAAI;EACpC,MAAM;IAAE0B;EAAK,CAAC,GAAGrB,IAAI;EACrB,IAAIE,MAAM,EAAEoB,EAAE,EAAEC,YAAY;EAE5B,IAAIf,WAAC,CAACgB,YAAY,CAACH,IAAI,CAAC,IAAIb,WAAC,CAACiB,SAAS,CAACJ,IAAI,CAAC,IAAIb,WAAC,CAACkB,kBAAkB,CAACL,IAAI,CAAC,EAAE;IAE3EC,EAAE,GAAGD,IAAI;IACTE,YAAY,GAAG,IAAI;EACrB,CAAC,MAAM,IAAIf,WAAC,CAACmB,qBAAqB,CAACN,IAAI,CAAC,EAAE;IAExCC,EAAE,GAAGH,KAAK,CAACS,qBAAqB,CAAC,KAAK,CAAC;IACvC1B,MAAM,GAAGM,WAAC,CAACqB,mBAAmB,CAACR,IAAI,CAACS,IAAI,EAAE,CACxCtB,WAAC,CAACuB,kBAAkB,CAACV,IAAI,CAACW,YAAY,CAAC,CAAC,CAAC,CAACV,EAAE,EAAEd,WAAC,CAACyB,UAAU,CAACX,EAAE,CAACY,IAAI,CAAC,CAAC,CACrE,CAAC;IACFX,YAAY,GAAGf,WAAC,CAACqB,mBAAmB,CAAC,KAAK,EAAE,CAC1CrB,WAAC,CAACuB,kBAAkB,CAACvB,WAAC,CAACyB,UAAU,CAACX,EAAE,CAACY,IAAI,CAAC,CAAC,CAC5C,CAAC;EACJ,CAAC,MAAM;IACL,MAAMtC,KAAK,CAACuC,mBAAmB,CAC7Bd,IAAI,EACJ,qBAAqBA,IAAI,CAACe,IAAI,kBAChC,CAAC;EACH;EAEA,MAAMC,WAAW,GAAGlB,KAAK,CAACS,qBAAqB,CAAC,UAAU,CAAC;EAC3D,MAAMU,UAAU,GAAGnB,KAAK,CAACS,qBAAqB,CAAC,SAAS,CAAC;EAEzD,MAAMzB,IAAI,GAAGW,eAAe,CAAC;IAC3ByB,WAAW,EAAEF,WAAW;IACxBG,QAAQ,EAAEF,UAAU;IACpBG,MAAM,EAAEzC,IAAI,CAAC0C,KAAK;IAClBC,KAAK,EAAExB,KAAK,CAACS,qBAAqB,CAAC,GAAG,CAAC;IACvCgB,EAAE,EAAEtB,EAAE;IACNuB,YAAY,EAAEtB;EAChB,CAAC,CAAmB;EAGpB,MAAMuB,eAAe,GAAGtC,WAAC,CAACuC,kBAAkB,CAAC3B,MAAM,CAAC;EACpD,IAAI4B,OAAO;EAEX,IAAIF,eAAe,EAAE;IACnBE,OAAO,GAAGxC,WAAC,CAACyC,gBAAgB,CAAC7B,MAAM,CAAC8B,KAAK,EAAE/C,IAAI,CAAC;EAClD;EAEA,OAAO;IACLO,aAAa,EAAEoC,eAAe;IAC9B5C,MAAM,EAAEA,MAAM;IACdF,IAAI,EAAEgD,OAAO,IAAI7C,IAAI;IACrBA,IAAI,EAAEA;EACR,CAAC;AACH;AAEA,SAASJ,qBAAqBA,CAC5BJ,IAAgC,EAChCC,KAAiB,EACjB;EACA,MAAM;IAAEI,IAAI;IAAEmB,KAAK;IAAEC;EAAO,CAAC,GAAGzB,IAAI;EACpC,MAAM0B,IAAI,GAAGrB,IAAI,CAACqB,IAAI;EACtB,IAAInB,MAAM;EAEV,MAAMiD,OAAO,GAAGhC,KAAK,CAACiC,WAAW,CAAC,MAAM,CAAC;EACzC,MAAMC,SAAS,GAAG7C,WAAC,CAAC8C,gBAAgB,CAClC9C,WAAC,CAACyB,UAAU,CAACkB,OAAO,CAAC,EACrB3C,WAAC,CAACyB,UAAU,CAAC,OAAO,CACtB,CAAC;EAED,IAAIzB,WAAC,CAACgB,YAAY,CAACH,IAAI,CAAC,IAAIb,WAAC,CAACiB,SAAS,CAACJ,IAAI,CAAC,IAAIb,WAAC,CAACkB,kBAAkB,CAACL,IAAI,CAAC,EAAE;IAE3EnB,MAAM,GAAGM,WAAC,CAAC+C,mBAAmB,CAC5B/C,WAAC,CAACgD,oBAAoB,CAAC,GAAG,EAAEnC,IAAI,EAAEgC,SAAS,CAC7C,CAAC;EACH,CAAC,MAAM,IAAI7C,WAAC,CAACmB,qBAAqB,CAACN,IAAI,CAAC,EAAE;IAExCnB,MAAM,GAAGM,WAAC,CAACqB,mBAAmB,CAACR,IAAI,CAACS,IAAI,EAAE,CACxCtB,WAAC,CAACuB,kBAAkB,CAACV,IAAI,CAACW,YAAY,CAAC,CAAC,CAAC,CAACV,EAAE,EAAE+B,SAAS,CAAC,CACzD,CAAC;EACJ,CAAC,MAAM;IACL,MAAMzD,KAAK,CAACuC,mBAAmB,CAC7Bd,IAAI,EACJ,qBAAqBA,IAAI,CAACe,IAAI,kBAChC,CAAC;EACH;EAEA,MAAMrB,QAAQ,GAAGE,UAAU,CAAC;IAC1BwC,sBAAsB,EAAEtC,KAAK,CAACS,qBAAqB,CAAC,kBAAkB,CAAC;IACvE8B,mBAAmB,EAAEvC,KAAK,CAACS,qBAAqB,CAC9C,0BACF,CAAC;IACD+B,kBAAkB,EAAExC,KAAK,CAACS,qBAAqB,CAAC,eAAe,CAAC;IAChEgC,YAAY,EAAEzC,KAAK,CAACS,qBAAqB,CAAC,UAAU,CAAC;IACrDiC,QAAQ,EAAErD,WAAC,CAACyB,UAAU,CAACkB,OAAO,CAAC;IAC/BV,MAAM,EAAEzC,IAAI,CAAC0C;EACf,CAAC,CAAC;EAEF,MAAMI,eAAe,GAAGtC,WAAC,CAACuC,kBAAkB,CAAC3B,MAAM,CAAC;EAEpD,MAAM0C,OAAO,GAAI/C,QAAQ,CAAC,CAAC,CAAC,CAAoBX,KAAK,CAACC,IAAI;EAC1D,MAAMF,IAAI,GAAG2D,OAAO,CAAC,CAAC,CAAmB;EAEzC,IAAIhB,eAAe,EAAE;IACnBgB,OAAO,CAAC,CAAC,CAAC,GAAGtD,WAAC,CAACyC,gBAAgB,CAAC7B,MAAM,CAAC8B,KAAK,EAAE/C,IAAI,CAAC;EACrD;EAIA,OAAO;IACLO,aAAa,EAAEoC,eAAe;IAC9B5C,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA,IAAI;IACVH,IAAI,EAAEe;EACR,CAAC;AACH", "ignoreList": []}