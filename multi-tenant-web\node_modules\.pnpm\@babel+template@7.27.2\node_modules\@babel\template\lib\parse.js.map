{"version": 3, "names": ["_t", "require", "_parser", "_codeFrame", "isCallExpression", "isExpressionStatement", "isFunction", "isIdentifier", "isJSXIdentifier", "isNewExpression", "isPlaceholder", "isStatement", "isStringLiteral", "removePropertiesDeep", "traverse", "PATTERN", "parseAndBuildMetadata", "formatter", "code", "opts", "placeholder<PERSON><PERSON><PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON>", "preserveComments", "syntacticPlaceholders", "ast", "parseWithCodeFrame", "parser", "validate", "state", "syntactic", "placeholders", "placeholder<PERSON><PERSON><PERSON>", "Set", "legacy", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "length", "node", "ancestors", "_state$placeholderWhi", "name", "hasSyntacticPlaceholders", "Error", "value", "test", "has", "slice", "parent", "key", "type", "expectedNode", "push", "resolve", "resolveAncestors", "isDuplicate", "add", "i", "index", "undefined", "parserOpts", "plugins", "allowAwaitOutsideFunction", "allowReturnOutsideFunction", "allowNewTargetOutsideFunction", "allowSuperOutsideMethod", "allowYieldOutsideFunction", "sourceType", "parse", "err", "loc", "message", "codeFrameColumns", "start"], "sources": ["../src/parse.ts"], "sourcesContent": ["import {\n  isCallExpression,\n  isExpressionStatement,\n  isFunction,\n  isIdentifier,\n  isJSXIdentifier,\n  isNewExpression,\n  isPlaceholder,\n  isStatement,\n  isStringLiteral,\n  removePropertiesDeep,\n  traverse,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { TraversalAncestors } from \"@babel/types\";\nimport { parse } from \"@babel/parser\";\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport type { TemplateOpts, ParserOpts } from \"./options.ts\";\nimport type { Formatter } from \"./formatters.ts\";\n\nexport type Metadata = {\n  ast: t.File;\n  placeholders: Array<Placeholder>;\n  placeholderNames: Set<string>;\n};\n\ntype PlaceholderType = \"string\" | \"param\" | \"statement\" | \"other\";\nexport type Placeholder = {\n  name: string;\n  resolve: (a: t.File) => { parent: t.Node; key: string; index?: number };\n  type: PlaceholderType;\n  isDuplicate: boolean;\n};\n\nconst PATTERN = /^[_$A-Z0-9]+$/;\n\nexport default function parseAndBuildMetadata<T>(\n  formatter: Formatter<T>,\n  code: string,\n  opts: TemplateOpts,\n): Metadata {\n  const {\n    placeholderWhitelist,\n    placeholderPattern,\n    preserveComments,\n    syntacticPlaceholders,\n  } = opts;\n\n  const ast = parseWithCodeFrame(code, opts.parser, syntacticPlaceholders);\n\n  removePropertiesDeep(ast, {\n    preserveComments,\n  });\n\n  formatter.validate(ast);\n\n  const state: MetadataState = {\n    syntactic: { placeholders: [], placeholderNames: new Set() },\n    legacy: { placeholders: [], placeholderNames: new Set() },\n    placeholderWhitelist,\n    placeholderPattern,\n    syntacticPlaceholders,\n  };\n\n  traverse(ast, placeholderVisitorHandler, state);\n\n  return {\n    ast,\n    ...(state.syntactic.placeholders.length ? state.syntactic : state.legacy),\n  };\n}\n\nfunction placeholderVisitorHandler(\n  node: t.Node,\n  ancestors: TraversalAncestors,\n  state: MetadataState,\n) {\n  let name: string;\n\n  let hasSyntacticPlaceholders = state.syntactic.placeholders.length > 0;\n\n  if (isPlaceholder(node)) {\n    if (state.syntacticPlaceholders === false) {\n      throw new Error(\n        \"%%foo%%-style placeholders can't be used when \" +\n          \"'.syntacticPlaceholders' is false.\",\n      );\n    }\n    name = node.name.name;\n    hasSyntacticPlaceholders = true;\n  } else if (hasSyntacticPlaceholders || state.syntacticPlaceholders) {\n    return;\n  } else if (isIdentifier(node) || isJSXIdentifier(node)) {\n    name = node.name;\n  } else if (isStringLiteral(node)) {\n    name = node.value;\n  } else {\n    return;\n  }\n\n  if (\n    hasSyntacticPlaceholders &&\n    (state.placeholderPattern != null || state.placeholderWhitelist != null)\n  ) {\n    // This check is also in options.js. We need it there to handle the default\n    // .syntacticPlaceholders behavior.\n    throw new Error(\n      \"'.placeholderWhitelist' and '.placeholderPattern' aren't compatible\" +\n        \" with '.syntacticPlaceholders: true'\",\n    );\n  }\n\n  if (\n    !hasSyntacticPlaceholders &&\n    (state.placeholderPattern === false ||\n      !(state.placeholderPattern || PATTERN).test(name)) &&\n    !state.placeholderWhitelist?.has(name)\n  ) {\n    return;\n  }\n\n  // Keep our own copy of the ancestors so we can use it in .resolve().\n  ancestors = ancestors.slice();\n\n  const { node: parent, key } = ancestors[ancestors.length - 1];\n\n  let type: PlaceholderType;\n  if (\n    isStringLiteral(node) ||\n    isPlaceholder(node, { expectedNode: \"StringLiteral\" })\n  ) {\n    type = \"string\";\n  } else if (\n    (isNewExpression(parent) && key === \"arguments\") ||\n    (isCallExpression(parent) && key === \"arguments\") ||\n    (isFunction(parent) && key === \"params\")\n  ) {\n    type = \"param\";\n  } else if (isExpressionStatement(parent) && !isPlaceholder(node)) {\n    type = \"statement\";\n    ancestors = ancestors.slice(0, -1);\n  } else if (isStatement(node) && isPlaceholder(node)) {\n    type = \"statement\";\n  } else {\n    type = \"other\";\n  }\n\n  const { placeholders, placeholderNames } = !hasSyntacticPlaceholders\n    ? state.legacy\n    : state.syntactic;\n\n  placeholders.push({\n    name,\n    type,\n    resolve: ast => resolveAncestors(ast, ancestors),\n    isDuplicate: placeholderNames.has(name),\n  });\n  placeholderNames.add(name);\n}\n\nfunction resolveAncestors(ast: t.File, ancestors: TraversalAncestors) {\n  let parent: t.Node = ast;\n  for (let i = 0; i < ancestors.length - 1; i++) {\n    const { key, index } = ancestors[i];\n\n    if (index === undefined) {\n      parent = (parent as any)[key];\n    } else {\n      parent = (parent as any)[key][index];\n    }\n  }\n\n  const { key, index } = ancestors[ancestors.length - 1];\n\n  return { parent, key, index };\n}\n\ntype MetadataState = {\n  syntactic: {\n    placeholders: Array<Placeholder>;\n    placeholderNames: Set<string>;\n  };\n  legacy: {\n    placeholders: Array<Placeholder>;\n    placeholderNames: Set<string>;\n  };\n  placeholderWhitelist?: Set<string>;\n  placeholderPattern?: RegExp | false;\n  syntacticPlaceholders?: boolean;\n};\n\nfunction parseWithCodeFrame(\n  code: string,\n  parserOpts: ParserOpts,\n  syntacticPlaceholders?: boolean,\n): t.File {\n  const plugins = (parserOpts.plugins || []).slice();\n  if (syntacticPlaceholders !== false) {\n    plugins.push(\"placeholders\");\n  }\n\n  parserOpts = {\n    allowAwaitOutsideFunction: true,\n    allowReturnOutsideFunction: true,\n    allowNewTargetOutsideFunction: true,\n    allowSuperOutsideMethod: true,\n    allowYieldOutsideFunction: true,\n    sourceType: \"module\",\n    ...parserOpts,\n    plugins,\n  };\n\n  try {\n    return parse(code, parserOpts);\n  } catch (err) {\n    const loc = err.loc;\n    if (loc) {\n      err.message += \"\\n\" + codeFrameColumns(code, { start: loc });\n      err.code = \"BABEL_TEMPLATE_PARSE_ERROR\";\n    }\n    throw err;\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAeA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAAqD;EAfnDG,gBAAgB;EAChBC,qBAAqB;EACrBC,UAAU;EACVC,YAAY;EACZC,eAAe;EACfC,eAAe;EACfC,aAAa;EACbC,WAAW;EACXC,eAAe;EACfC,oBAAoB;EACpBC;AAAQ,IAAAd,EAAA;AAuBV,MAAMe,OAAO,GAAG,eAAe;AAEhB,SAASC,qBAAqBA,CAC3CC,SAAuB,EACvBC,IAAY,EACZC,IAAkB,EACR;EACV,MAAM;IACJC,oBAAoB;IACpBC,kBAAkB;IAClBC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,IAAI;EAER,MAAMK,GAAG,GAAGC,kBAAkB,CAACP,IAAI,EAAEC,IAAI,CAACO,MAAM,EAAEH,qBAAqB,CAAC;EAExEV,oBAAoB,CAACW,GAAG,EAAE;IACxBF;EACF,CAAC,CAAC;EAEFL,SAAS,CAACU,QAAQ,CAACH,GAAG,CAAC;EAEvB,MAAMI,KAAoB,GAAG;IAC3BC,SAAS,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,gBAAgB,EAAE,IAAIC,GAAG,CAAC;IAAE,CAAC;IAC5DC,MAAM,EAAE;MAAEH,YAAY,EAAE,EAAE;MAAEC,gBAAgB,EAAE,IAAIC,GAAG,CAAC;IAAE,CAAC;IACzDZ,oBAAoB;IACpBC,kBAAkB;IAClBE;EACF,CAAC;EAEDT,QAAQ,CAACU,GAAG,EAAEU,yBAAyB,EAAEN,KAAK,CAAC;EAE/C,OAAAO,MAAA,CAAAC,MAAA;IACEZ;EAAG,GACCI,KAAK,CAACC,SAAS,CAACC,YAAY,CAACO,MAAM,GAAGT,KAAK,CAACC,SAAS,GAAGD,KAAK,CAACK,MAAM;AAE5E;AAEA,SAASC,yBAAyBA,CAChCI,IAAY,EACZC,SAA6B,EAC7BX,KAAoB,EACpB;EAAA,IAAAY,qBAAA;EACA,IAAIC,IAAY;EAEhB,IAAIC,wBAAwB,GAAGd,KAAK,CAACC,SAAS,CAACC,YAAY,CAACO,MAAM,GAAG,CAAC;EAEtE,IAAI3B,aAAa,CAAC4B,IAAI,CAAC,EAAE;IACvB,IAAIV,KAAK,CAACL,qBAAqB,KAAK,KAAK,EAAE;MACzC,MAAM,IAAIoB,KAAK,CACb,gDAAgD,GAC9C,oCACJ,CAAC;IACH;IACAF,IAAI,GAAGH,IAAI,CAACG,IAAI,CAACA,IAAI;IACrBC,wBAAwB,GAAG,IAAI;EACjC,CAAC,MAAM,IAAIA,wBAAwB,IAAId,KAAK,CAACL,qBAAqB,EAAE;IAClE;EACF,CAAC,MAAM,IAAIhB,YAAY,CAAC+B,IAAI,CAAC,IAAI9B,eAAe,CAAC8B,IAAI,CAAC,EAAE;IACtDG,IAAI,GAAGH,IAAI,CAACG,IAAI;EAClB,CAAC,MAAM,IAAI7B,eAAe,CAAC0B,IAAI,CAAC,EAAE;IAChCG,IAAI,GAAGH,IAAI,CAACM,KAAK;EACnB,CAAC,MAAM;IACL;EACF;EAEA,IACEF,wBAAwB,KACvBd,KAAK,CAACP,kBAAkB,IAAI,IAAI,IAAIO,KAAK,CAACR,oBAAoB,IAAI,IAAI,CAAC,EACxE;IAGA,MAAM,IAAIuB,KAAK,CACb,qEAAqE,GACnE,sCACJ,CAAC;EACH;EAEA,IACE,CAACD,wBAAwB,KACxBd,KAAK,CAACP,kBAAkB,KAAK,KAAK,IACjC,CAAC,CAACO,KAAK,CAACP,kBAAkB,IAAIN,OAAO,EAAE8B,IAAI,CAACJ,IAAI,CAAC,CAAC,IACpD,GAAAD,qBAAA,GAACZ,KAAK,CAACR,oBAAoB,aAA1BoB,qBAAA,CAA4BM,GAAG,CAACL,IAAI,CAAC,GACtC;IACA;EACF;EAGAF,SAAS,GAAGA,SAAS,CAACQ,KAAK,CAAC,CAAC;EAE7B,MAAM;IAAET,IAAI,EAAEU,MAAM;IAAEC;EAAI,CAAC,GAAGV,SAAS,CAACA,SAAS,CAACF,MAAM,GAAG,CAAC,CAAC;EAE7D,IAAIa,IAAqB;EACzB,IACEtC,eAAe,CAAC0B,IAAI,CAAC,IACrB5B,aAAa,CAAC4B,IAAI,EAAE;IAAEa,YAAY,EAAE;EAAgB,CAAC,CAAC,EACtD;IACAD,IAAI,GAAG,QAAQ;EACjB,CAAC,MAAM,IACJzC,eAAe,CAACuC,MAAM,CAAC,IAAIC,GAAG,KAAK,WAAW,IAC9C7C,gBAAgB,CAAC4C,MAAM,CAAC,IAAIC,GAAG,KAAK,WAAY,IAChD3C,UAAU,CAAC0C,MAAM,CAAC,IAAIC,GAAG,KAAK,QAAS,EACxC;IACAC,IAAI,GAAG,OAAO;EAChB,CAAC,MAAM,IAAI7C,qBAAqB,CAAC2C,MAAM,CAAC,IAAI,CAACtC,aAAa,CAAC4B,IAAI,CAAC,EAAE;IAChEY,IAAI,GAAG,WAAW;IAClBX,SAAS,GAAGA,SAAS,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpC,CAAC,MAAM,IAAIpC,WAAW,CAAC2B,IAAI,CAAC,IAAI5B,aAAa,CAAC4B,IAAI,CAAC,EAAE;IACnDY,IAAI,GAAG,WAAW;EACpB,CAAC,MAAM;IACLA,IAAI,GAAG,OAAO;EAChB;EAEA,MAAM;IAAEpB,YAAY;IAAEC;EAAiB,CAAC,GAAG,CAACW,wBAAwB,GAChEd,KAAK,CAACK,MAAM,GACZL,KAAK,CAACC,SAAS;EAEnBC,YAAY,CAACsB,IAAI,CAAC;IAChBX,IAAI;IACJS,IAAI;IACJG,OAAO,EAAE7B,GAAG,IAAI8B,gBAAgB,CAAC9B,GAAG,EAAEe,SAAS,CAAC;IAChDgB,WAAW,EAAExB,gBAAgB,CAACe,GAAG,CAACL,IAAI;EACxC,CAAC,CAAC;EACFV,gBAAgB,CAACyB,GAAG,CAACf,IAAI,CAAC;AAC5B;AAEA,SAASa,gBAAgBA,CAAC9B,GAAW,EAAEe,SAA6B,EAAE;EACpE,IAAIS,MAAc,GAAGxB,GAAG;EACxB,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,SAAS,CAACF,MAAM,GAAG,CAAC,EAAEoB,CAAC,EAAE,EAAE;IAC7C,MAAM;MAAER,GAAG;MAAES;IAAM,CAAC,GAAGnB,SAAS,CAACkB,CAAC,CAAC;IAEnC,IAAIC,KAAK,KAAKC,SAAS,EAAE;MACvBX,MAAM,GAAIA,MAAM,CAASC,GAAG,CAAC;IAC/B,CAAC,MAAM;MACLD,MAAM,GAAIA,MAAM,CAASC,GAAG,CAAC,CAACS,KAAK,CAAC;IACtC;EACF;EAEA,MAAM;IAAET,GAAG;IAAES;EAAM,CAAC,GAAGnB,SAAS,CAACA,SAAS,CAACF,MAAM,GAAG,CAAC,CAAC;EAEtD,OAAO;IAAEW,MAAM;IAAEC,GAAG;IAAES;EAAM,CAAC;AAC/B;AAgBA,SAASjC,kBAAkBA,CACzBP,IAAY,EACZ0C,UAAsB,EACtBrC,qBAA+B,EACvB;EACR,MAAMsC,OAAO,GAAG,CAACD,UAAU,CAACC,OAAO,IAAI,EAAE,EAAEd,KAAK,CAAC,CAAC;EAClD,IAAIxB,qBAAqB,KAAK,KAAK,EAAE;IACnCsC,OAAO,CAACT,IAAI,CAAC,cAAc,CAAC;EAC9B;EAEAQ,UAAU,GAAAzB,MAAA,CAAAC,MAAA;IACR0B,yBAAyB,EAAE,IAAI;IAC/BC,0BAA0B,EAAE,IAAI;IAChCC,6BAA6B,EAAE,IAAI;IACnCC,uBAAuB,EAAE,IAAI;IAC7BC,yBAAyB,EAAE,IAAI;IAC/BC,UAAU,EAAE;EAAQ,GACjBP,UAAU;IACbC;EAAO,EACR;EAED,IAAI;IACF,OAAO,IAAAO,aAAK,EAAClD,IAAI,EAAE0C,UAAU,CAAC;EAChC,CAAC,CAAC,OAAOS,GAAG,EAAE;IACZ,MAAMC,GAAG,GAAGD,GAAG,CAACC,GAAG;IACnB,IAAIA,GAAG,EAAE;MACPD,GAAG,CAACE,OAAO,IAAI,IAAI,GAAG,IAAAC,2BAAgB,EAACtD,IAAI,EAAE;QAAEuD,KAAK,EAAEH;MAAI,CAAC,CAAC;MAC5DD,GAAG,CAACnD,IAAI,GAAG,4BAA4B;IACzC;IACA,MAAMmD,GAAG;EACX;AACF", "ignoreList": []}