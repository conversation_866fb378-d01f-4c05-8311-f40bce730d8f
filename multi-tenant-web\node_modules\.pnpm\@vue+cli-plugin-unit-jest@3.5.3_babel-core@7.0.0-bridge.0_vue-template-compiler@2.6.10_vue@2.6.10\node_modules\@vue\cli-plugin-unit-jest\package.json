{"name": "@vue/cli-plugin-unit-jest", "version": "3.5.3", "description": "unit-jest plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-unit-jest"}, "keywords": ["vue", "cli", "unit-jest"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-unit-jest#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.5.1", "babel-jest": "^23.6.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "jest": "^23.6.0", "jest-serializer-vue": "^2.0.2", "jest-transform-stub": "^2.0.0", "vue-jest": "^3.0.3"}, "devDependencies": {"@vue/test-utils": "1.0.0-beta.29", "ts-jest": "^23.0.0"}, "gitHead": "582dc450150eca6c02cd1b30d588b0179ca248bc"}