"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _default = {
  "syntax-async-generators": require("@babel/plugin-syntax-async-generators"),
  "syntax-json-strings": require("@babel/plugin-syntax-json-strings"),
  "syntax-object-rest-spread": require("@babel/plugin-syntax-object-rest-spread"),
  "syntax-optional-catch-binding": require("@babel/plugin-syntax-optional-catch-binding"),
  "transform-async-to-generator": require("@babel/plugin-transform-async-to-generator"),
  "proposal-async-generator-functions": require("@babel/plugin-proposal-async-generator-functions"),
  "proposal-json-strings": require("@babel/plugin-proposal-json-strings"),
  "transform-arrow-functions": require("@babel/plugin-transform-arrow-functions"),
  "transform-block-scoped-functions": require("@babel/plugin-transform-block-scoped-functions"),
  "transform-block-scoping": require("@babel/plugin-transform-block-scoping"),
  "transform-classes": require("@babel/plugin-transform-classes"),
  "transform-computed-properties": require("@babel/plugin-transform-computed-properties"),
  "transform-destructuring": require("@babel/plugin-transform-destructuring"),
  "transform-dotall-regex": require("@babel/plugin-transform-dotall-regex"),
  "transform-duplicate-keys": require("@babel/plugin-transform-duplicate-keys"),
  "transform-for-of": require("@babel/plugin-transform-for-of"),
  "transform-function-name": require("@babel/plugin-transform-function-name"),
  "transform-literals": require("@babel/plugin-transform-literals"),
  "transform-modules-amd": require("@babel/plugin-transform-modules-amd"),
  "transform-modules-commonjs": require("@babel/plugin-transform-modules-commonjs"),
  "transform-modules-systemjs": require("@babel/plugin-transform-modules-systemjs"),
  "transform-modules-umd": require("@babel/plugin-transform-modules-umd"),
  "transform-named-capturing-groups-regex": require("@babel/plugin-transform-named-capturing-groups-regex"),
  "transform-object-super": require("@babel/plugin-transform-object-super"),
  "transform-parameters": require("@babel/plugin-transform-parameters"),
  "transform-shorthand-properties": require("@babel/plugin-transform-shorthand-properties"),
  "transform-spread": require("@babel/plugin-transform-spread"),
  "transform-sticky-regex": require("@babel/plugin-transform-sticky-regex"),
  "transform-template-literals": require("@babel/plugin-transform-template-literals"),
  "transform-typeof-symbol": require("@babel/plugin-transform-typeof-symbol"),
  "transform-unicode-regex": require("@babel/plugin-transform-unicode-regex"),
  "transform-exponentiation-operator": require("@babel/plugin-transform-exponentiation-operator"),
  "transform-new-target": require("@babel/plugin-transform-new-target"),
  "proposal-object-rest-spread": require("@babel/plugin-proposal-object-rest-spread"),
  "proposal-optional-catch-binding": require("@babel/plugin-proposal-optional-catch-binding"),
  "transform-regenerator": require("@babel/plugin-transform-regenerator"),
  "proposal-unicode-property-regex": require("@babel/plugin-proposal-unicode-property-regex")
};
exports.default = _default;