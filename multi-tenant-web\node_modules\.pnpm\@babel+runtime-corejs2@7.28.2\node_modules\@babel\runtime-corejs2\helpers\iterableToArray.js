var _Symbol = require("core-js/library/fn/symbol/index.js");
var _Symbol$iterator = require("core-js/library/fn/symbol/iterator.js");
var _Array$from = require("core-js/library/fn/array/from.js");
function _iterableToArray(r) {
  if ("undefined" != typeof _Symbol && null != r[_Symbol$iterator] || null != r["@@iterator"]) return _Array$from(r);
}
module.exports = _iterableToArray, module.exports.__esModule = true, module.exports["default"] = module.exports;