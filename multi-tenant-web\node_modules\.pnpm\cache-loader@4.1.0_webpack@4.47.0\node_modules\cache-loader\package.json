{"name": "cache-loader", "version": "4.1.0", "description": "Caches the result of following loaders on disk.", "license": "MIT", "repository": "webpack-contrib/cache-loader", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/cache-loader", "bugs": "https://github.com/webpack-contrib/cache-loader/issues", "main": "dist/cjs.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "files": ["dist"], "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"buffer-json": "^2.0.0", "find-cache-dir": "^3.0.0", "loader-utils": "^1.2.3", "mkdirp": "^0.5.1", "neo-async": "^2.6.1", "schema-utils": "^2.0.0"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "babel-loader": "^8.0.6", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^5.0.0", "del-cli": "^2.0.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.18.0", "file-loader": "^4.1.0", "husky": "^3.0.0", "jest": "^24.8.0", "jest-junit": "^6.4.0", "lint-staged": "^9.2.0", "memory-fs": "^0.4.1", "normalize-path": "^3.0.0", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^6.0.1", "uuid": "^3.3.2", "webpack": "^4.36.1", "webpack-cli": "^3.3.6"}, "keywords": ["webpack"]}