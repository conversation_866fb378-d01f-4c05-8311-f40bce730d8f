#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="E:\IdeaProject\RuoYi-Vue-Multi-Tenant-main\multi-tenant-web\node_modules\.pnpm\husky@1.3.1\node_modules\husky\lib\upgrader\node_modules;E:\IdeaProject\RuoYi-Vue-Multi-Tenant-main\multi-tenant-web\node_modules\.pnpm\husky@1.3.1\node_modules\husky\lib\node_modules;E:\IdeaProject\RuoYi-Vue-Multi-Tenant-main\multi-tenant-web\node_modules\.pnpm\husky@1.3.1\node_modules\husky\node_modules;E:\IdeaProject\RuoYi-Vue-Multi-Tenant-main\multi-tenant-web\node_modules\.pnpm\husky@1.3.1\node_modules;E:\IdeaProject\RuoYi-Vue-Multi-Tenant-main\multi-tenant-web\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/husky@1.3.1/node_modules/husky/lib/upgrader/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/husky@1.3.1/node_modules/husky/lib/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/husky@1.3.1/node_modules/husky/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/husky@1.3.1/node_modules:/mnt/e/IdeaProject/RuoYi-Vue-Multi-Tenant-main/multi-tenant-web/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../lib/upgrader/bin.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../lib/upgrader/bin.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../lib/upgrader/bin.js" $args
  } else {
    & "node$exe"  "$basedir/../../lib/upgrader/bin.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
