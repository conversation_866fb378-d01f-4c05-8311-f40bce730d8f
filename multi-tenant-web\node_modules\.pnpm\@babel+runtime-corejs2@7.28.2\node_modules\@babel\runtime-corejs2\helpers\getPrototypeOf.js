var _Object$setPrototypeOf = require("core-js/library/fn/object/set-prototype-of.js");
var _Object$getPrototypeOf = require("core-js/library/fn/object/get-prototype-of.js");
function _getPrototypeOf(t) {
  return module.exports = _getPrototypeOf = _Object$setPrototypeOf ? _Object$getPrototypeOf.bind() : function (t) {
    return t.__proto__ || _Object$getPrototypeOf(t);
  }, module.exports.__esModule = true, module.exports["default"] = module.exports, _getPrototypeOf(t);
}
module.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports["default"] = module.exports;