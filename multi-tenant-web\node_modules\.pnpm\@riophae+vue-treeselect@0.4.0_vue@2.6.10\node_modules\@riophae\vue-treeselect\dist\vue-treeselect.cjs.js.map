{"version": 3, "sources": ["webpack://VueTreeselect/webpack/bootstrap", "webpack://VueTreeselect/external \"@babel/runtime/helpers/slicedToArray\"", "webpack://VueTreeselect/external \"@babel/runtime/helpers/toConsumableArray\"", "webpack://VueTreeselect/external \"@babel/runtime/helpers/defineProperty\"", "webpack://VueTreeselect/external \"fuzzysearch\"", "webpack://VueTreeselect/external \"lodash/noop\"", "webpack://VueTreeselect/external \"lodash/debounce\"", "webpack://VueTreeselect/external \"watch-size\"", "webpack://VueTreeselect/external \"is-promise\"", "webpack://VueTreeselect/external \"lodash/once\"", "webpack://VueTreeselect/external \"lodash/identity\"", "webpack://VueTreeselect/external \"lodash/constant\"", "webpack://VueTreeselect/external \"@babel/runtime/helpers/typeof\"", "webpack://VueTreeselect/external \"lodash/last\"", "webpack://VueTreeselect/external \"babel-helper-vue-jsx-merge-props\"", "webpack://VueTreeselect/external \"vue\"", "webpack://VueTreeselect/./src/style.less?1f4a", "webpack://VueTreeselect/./src/utils/warning.js", "webpack://VueTreeselect/./src/utils/onLeftClick.js", "webpack://VueTreeselect/./src/utils/scrollIntoView.js", "webpack://VueTreeselect/./src/utils/removeFromArray.js", "webpack://VueTreeselect/./src/utils/watchSize.js", "webpack://VueTreeselect/./src/utils/setupResizeAndScrollEventListeners.js", "webpack://VueTreeselect/./src/utils/isNaN.js", "webpack://VueTreeselect/./src/utils/createMap.js", "webpack://VueTreeselect/./src/utils/deepExtend.js", "webpack://VueTreeselect/./src/utils/includes.js", "webpack://VueTreeselect/./src/utils/find.js", "webpack://VueTreeselect/./src/utils/quickDiff.js", "webpack://VueTreeselect/./src/utils/index.js", "webpack://VueTreeselect/./src/constants.js", "webpack://VueTreeselect/./src/mixins/treeselectMixin.js", "webpack://VueTreeselect/./src/components/HiddenFields.vue?7939", "webpack://VueTreeselect/./src/components/HiddenFields.vue?750c", "webpack://VueTreeselect/./node_modules/vue-loader/lib/runtime/componentNormalizer.js", "webpack://VueTreeselect/./src/components/HiddenFields.vue", "webpack://VueTreeselect/./src/components/Input.vue?1e32", "webpack://VueTreeselect/./src/components/Input.vue?449d", "webpack://VueTreeselect/./src/components/Input.vue", "webpack://VueTreeselect/./src/components/Placeholder.vue?e4f3", "webpack://VueTreeselect/./src/components/Placeholder.vue?238d", "webpack://VueTreeselect/./src/components/Placeholder.vue", "webpack://VueTreeselect/./src/components/SingleValue.vue?94ec", "webpack://VueTreeselect/./src/components/SingleValue.vue?5aaa", "webpack://VueTreeselect/./src/components/SingleValue.vue", "webpack://VueTreeselect/./src/components/icons/Delete.vue?2d39", "webpack://VueTreeselect/./src/components/icons/Delete.vue?0c8a", "webpack://VueTreeselect/./src/components/icons/Delete.vue?ada5", "webpack://VueTreeselect/./src/components/icons/Delete.vue", "webpack://VueTreeselect/./src/components/MultiValueItem.vue?c02e", "webpack://VueTreeselect/./src/components/MultiValueItem.vue?9a1f", "webpack://VueTreeselect/./src/components/MultiValueItem.vue", "webpack://VueTreeselect/./src/components/MultiValue.vue?a3a9", "webpack://VueTreeselect/./src/components/MultiValue.vue?6f61", "webpack://VueTreeselect/./src/components/MultiValue.vue", "webpack://VueTreeselect/./src/components/icons/Arrow.vue?2ad4", "webpack://VueTreeselect/./src/components/icons/Arrow.vue?eeef", "webpack://VueTreeselect/./src/components/icons/Arrow.vue?525a", "webpack://VueTreeselect/./src/components/icons/Arrow.vue", "webpack://VueTreeselect/./src/components/Control.vue?514d", "webpack://VueTreeselect/./src/components/Control.vue?57ab", "webpack://VueTreeselect/./src/components/Control.vue", "webpack://VueTreeselect/./src/components/Tip.vue?7443", "webpack://VueTreeselect/./src/components/Tip.vue?5960", "webpack://VueTreeselect/./src/components/Tip.vue", "webpack://VueTreeselect/./src/components/Option.vue?3086", "webpack://VueTreeselect/./src/components/Option.vue?0a09", "webpack://VueTreeselect/./src/components/Option.vue", "webpack://VueTreeselect/./src/components/Menu.vue?731f", "webpack://VueTreeselect/./src/components/Menu.vue?c349", "webpack://VueTreeselect/./src/components/Menu.vue", "webpack://VueTreeselect/./src/components/MenuPortal.vue?9292", "webpack://VueTreeselect/./src/components/MenuPortal.vue?ca92", "webpack://VueTreeselect/./src/components/MenuPortal.vue", "webpack://VueTreeselect/./src/components/Treeselect.vue?e969", "webpack://VueTreeselect/./src/components/Treeselect.vue?85a0", "webpack://VueTreeselect/./src/components/Treeselect.vue", "webpack://VueTreeselect/./src/index.js"], "names": [], "mappings": ";;;;;;;QAAA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;AClFA,iE;;;;;;ACAA,qE;;;;;;ACAA,kE;;;;;;ACAA,wC;;;;;;ACAA,wC;;;;;;ACAA,4C;;;;;;ACAA,uC;;;;;;ACAA,uC;;;;;;ACAA,wC;;;;;;ACAA,4C;;;;;;ACAA,4C;;;;;;ACAA,0D;;;;;;ACAA,wC;;;;;;ACAA,6D;;;;;;ACAA,gC;;;;;;ACAA,uC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACA0E;AAC5C;AACvB,IAAI,eAAO,2CAA2C,cAAI;AACjE;AACA;;AAEA;;AAEA,+CAA+C,2BAAkB;AACjE;AACA,E;;ACVO;AACP;AACA;AACA,4FAA4F,aAAa;AACzG;AACA;;AAEA;AACA;AACA;AACA,C;;ACVO;AACP;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA,C;;;;;;;;;;;;ACVO;AACP;AACA;AACA,C;;ACH0D;AACN;AACpD;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI,eAAe;AACnB;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;;AAEA;AACA;AACA;;AAEA,iDAAiD,6BAAgC;AACjF;AACA;AACA;AACA,C;;AClEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEO;AACP;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA,C;;AC1CO,SAAS,WAAK;AACrB;AACA,C;;;;;;;;;;;;;;;;;;;;;;;;;;ACFO;AACP;AACA,E;;;;;;ACFoD;;AAEpD;AACA,uBAAuB,gBAAO;AAC9B;AACA;;AAEA;AACA;AACA,8BAA8B;AAC9B;AACA,GAAG;AACH;AACA;AACA;;AAEO;AACP;AACA;;AAEA,sCAAsC,SAAS;AAC/C;AACA;AACA;;AAEA;AACA,C;;;;;;;;AC1BO;AACP;AACA,C;;ACFO;AACP,mCAAmC,SAAS;AAC5C;AACA;;AAEA;AACA,C;;ACNO;AACP;;AAEA,iBAAiB,iBAAiB;AAClC;AACA;;AAEA;AACA,C;;ACRoC;AACQ;AACM;AACZ;AACE;AACkD;AAC1D;AACQ;AACV;AACA;AACQ;AACA;AACE;AACE;AACZ;AACQ;AACR;AACsB;;;ACjB7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACA;AACA,qB;;AChC2D;AACQ;AACN;;AAEpE,0CAA0C,gCAAgC,oCAAoC,oDAAoD,8DAA8D,gEAAgE,EAAE,EAAE,gCAAgC,EAAE,aAAa;;AAEnV,gCAAgC,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,+CAA+C,CAAC,wBAAe,2BAA2B,EAAE,EAAE,EAAE,6CAA6C,2EAA2E,EAAE,OAAO,yCAAyC,kFAAkF,EAAE,EAAE,EAAE,EAAE,eAAe;;AAE9d;AAC8I;AACgH;;AAEpS;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,oCAAoC,WAAK;AACzC;AACA;;AAEA;AACA,4BAA4B,8BAAW,qBAAqB,QAAQ;AACpE;;AAEA;AACA;AACA;;AAEA;AACe;AACf;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,eAAe,kBAAQ;AACvB,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,eAAe,kBAAQ;AACvB,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,eAAe,kBAAQ;AACvB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,eAAe,YAAY;AAC3B;AACA,gCAAgC,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB;AAC9F,eAAe,QAAQ;AACvB;AACA,KAAK;AACL;AACA;AACA;AACA,eAAe,cAAc;AAC7B;AACA,gCAAgC,cAAc,EAAE,KAAK,EAAE,KAAK;AAC5D,eAAe,QAAQ;AACvB;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,eAAe,eAAe;AAC9B;AACA,gCAAgC,GAAG,EAAE,eAAe,EAAE,aAAa,EAAE,sBAAsB;AAC3F,eAAe,QAAQ;AACvB;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,iBAAiB,SAAS;AAC1B,yBAAyB,SAAS;AAClC;AACA,yBAAyB,SAAS;AAClC,OAAO;AACP;AACA;AACA;AACA;AACA,kBAAkB,SAAS;AAC3B,OAAO;AACP,oBAAoB,SAAS;AAC7B;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA,0FAA0F,GAAG;AAC7F;AACA,OAAO,mCAAmC,eAAe;AACzD;AACA;;AAEA;AACA;AACA,SAAS;AACT,OAAO,mCAAmC,aAAa;AACvD;AACA;;AAEA;AACA;AACA,SAAS;AACT,OAAO,mCAAmC,sBAAsB;AAChE;;AAEA;AACA;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB,gBAAgB,QAAQ;AACxB;AACA,WAAW;AACX,SAAS;;AAET;AACA;;AAEA,+BAA+B,KAAK;AACpC;AACA;AACA,SAAS;AACT,OAAO,+BAA+B,KAAK;AAC3C;AACA;AACA,SAAS;AACT;;AAEA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,oCAAoC;AACpC,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,yDAAyD;AACzD,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,uBAAuB,SAAS;AAChC;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA,KAAK;AACL;AACA;AACA,uBAAuB,SAAS;AAChC;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA,MAAM,eAAO;AACb;AACA,OAAO;AACP;AACA,OAAO;;AAEP;AACA,QAAQ,eAAO;AACf;AACA,SAAS;AACT;AACA,SAAS;AACT;;AAEA;AACA,QAAQ,eAAO;AACf;AACA,SAAS;AACT;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA,UAAU,eAAO;AACjB;AACA,WAAW;AACX;AACA,WAAW;AACX,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA,8BAA8B,SAAS;AACvC;AACA,uDAAuD,cAAc;AACrE;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,MAAM,eAAO;AACb;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,oBAAoB,IAAI;AACxB;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;;AAEA;;AAEA,0FAA0F,GAAG;AAC7F;AACA,OAAO,mCAAmC,eAAe;AACzD;AACA;;AAEA;;AAEA;AACA;AACA,WAAW;AACX,SAAS;AACT,OAAO,mCAAmC,aAAa;AACvD,kBAAkB,SAAS;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,mCAAmC,sBAAsB;AAChE,mBAAmB,SAAS;;AAE5B;AACA;;AAEA;AACA,SAAS;;AAET;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,uBAAuB,SAAS;AAChC;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA,mCAAmC;AACnC;AACA,SAAS;;AAET;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA,uDAAuD,2BAAkB;AACzE;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA,OAAO;AACP,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL,qBAAqB,WAAW;AAChC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,gFAAgF,EAAE,wBAAe,eAAe,YAAY,MAAM,wBAAe,eAAe,eAAe,MAAM,wBAAe,eAAe,aAAa,MAAM,wBAAe,eAAe,gBAAgB;AACpR;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA,WAAW;AACX;;AAEA;AACA;AACA;AACA,6DAA6D,eAAe;AAC5E,WAAW;AACX;AACA,6DAA6D,gBAAgB;AAC7E,WAAW;;AAEX,kCAAkC,cAAc;AAChD,6DAA6D,YAAY;AACzE,8EAA8E,aAAa;AAC3F;AACA;;AAEA,gGAAgG,cAAc;AAC9G;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,gBAAgB,YAAY;AAC5B;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;;AAEA;;AAEA,oEAAoE;AACpE;AACA,OAAO;;AAEP;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,uBAAuB,cAAc;AACrC;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,iBAAiB,cAAO;AACxB;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA,4BAA4B,SAAS;AACrC;AACA;AACA,OAAO;AACP;AACA,4BAA4B,SAAS;;AAErC;AACA;AACA,qCAAqC,SAAS;AAC9C,SAAS;AACT;AACA,6CAA6C,OAAO;;AAEpD;AACA;AACA;AACA,mDAAmD,aAAa;AAChE;AACA,aAAa;AACb;AACA,SAAS;AACT;;AAEA;AACA,KAAK;AACL;AACA,6BAA6B,SAAS;AACtC,KAAK;AACL;AACA;;AAEA;AACA;AACA,OAAO;AACP,oBAAoB,uBAAc;AAClC;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,wCAAwC,cAAc;AACtD;AACA;AACA;AACA;AACA;;AAEA;AACA,iCAAiC,QAAQ,wBAAe,GAAG;AAC3D,SAAS,IAAI;;AAEb;;AAEA,kEAAkE,SAAS;;AAE3E;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;;AAEA,qEAAqE;AACrE;AACA,WAAW;;AAEX;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA,8DAA8D,EAAE,wBAAe,eAAe,YAAY,MAAM,wBAAe,eAAe,eAAe,MAAM,wBAAe,eAAe,aAAa,MAAM,wBAAe,eAAe,gBAAgB;;AAElQ;;AAEA;AACA;AACA,WAAW;;AAEX;AACA,YAAY,eAAO;AACnB;AACA,aAAa;AACb;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;;AAEA;AACA,gCAAgC,eAAe;AAC/C,SAAS;AACT;AACA,gCAAgC,gBAAgB;AAChD,SAAS;;AAET;AACA,2BAA2B,YAAY;AACvC,uCAAuC,aAAa;AACpD;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb,0DAA0D;AAC1D;AACA;AACA;;AAEA;AACA,OAAO;;AAEP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA,KAAK;AACL;AACA;;AAEA;AACA,gBAAgB,iBAAiB;AACjC;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,qBAAqB;AACrC;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,qBAAqB,cAAI;AACzB;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;;AAEP,UAAU,6BAAS;AACnB;AACA;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;;AAEA,MAAM,eAAO;AACb;AACA,OAAO;AACP;AACA,OAAO;AACP,KAAK;AACL;AACA,MAAM,eAAO;AACb;AACA,OAAO;AACP;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6FAA6F,SAAS;;AAEtG;AACA;AACA,OAAO;AACP;AACA;;AAEA;;AAEA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA,WAAW;AACX;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA,4CAA4C,cAAc;AAC1D,wEAAwE;AACxE;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA,WAAW;AACX;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA,4CAA4C,cAAc;AAC1D,4DAA4D;AAC5D;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA,MAAM,eAAe;AACrB;AACA,KAAK;AACL;AACA;AACA;AACA,sBAAsB,cAAO;AAC7B;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,CAAC,E;;AC99CgC;;AAEjC;AACA;AACA,wBAAwB,WAAK;AAC7B;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP,KAAK;AACL;AACA,CAAC,E;;AC/BuN,CAAgB,wHAAG,EAAC,C;;ACA5O;;AAEA;AACA;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,qBAAqB;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AC5FA,IAAI,mBAAM;AACsD;AACL;;;AAG3D;AAC0F;AAC1F,gBAAgB,kBAAU;AAC1B,EAAE,+CAAM;AACR,EAAE,mBAAM;AACR;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAYf;AACD;AACe,kE;;;;;;ACjC+C;AACJ;AACsB;AAChF,oCAAoC,SAAS,QAAQ,SAAS,MAAM,SAAS,OAAO,SAAS,aAAa,SAAS,WAAW,SAAS,cAAc,SAAS;AAC/I;AACf;AACA;AACA;AACA;AACA,kBAAkB,eAAe;AACjC;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA,6BAA6B,kBAAQ,yBAAyB,oBAAoB;AAClF;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA,mCAAmC,QAAQ;AAC3C;AACA;AACA;;AAEA;AACA,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,QAAQ,UAAU;AAClB;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,SAAS;AACT;;AAEA;AACA,QAAQ,UAAU;AAClB;AACA;AACA;AACA,SAAS;AACT;;AAEA,sBAAsB,mDAAc;AACpC;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA,iCAAiC,eAAe;AAChD,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,CAAC,E;;ACpRgN,CAAgB,0GAAG,EAAC,C;;ACArO,IAAI,YAAM,EAAE,qBAAe;AAC8B;AACL;;;AAGpD;AAC0F;AAC1F,IAAI,eAAS,GAAG,kBAAU;AAC1B,EAAE,wCAAM;AACR,EAAE,YAAM;AACR,EAAE,qBAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,kBAYf;AACD,eAAS;AACM,yDAAS,Q;;ACjCT;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC,E;;ACfsN,CAAgB,sHAAG,EAAC,C;;ACA3O,IAAI,kBAAM,EAAE,2BAAe;AACoC;AACL;;;AAG1D;AAC0F;AAC1F,IAAI,qBAAS,GAAG,kBAAU;AAC1B,EAAE,8CAAM;AACR,EAAE,kBAAM;AACR,EAAE,2BAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,wBAYf;AACD,qBAAS;AACM,qEAAS,Q;;ACjCI;AACY;AACzB;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,sCAAsC,WAAW,KAAK,KAAK;AAChE;AACA,KAAK;AACL;AACA,CAAC,E;;AC1BsN,CAAgB,sHAAG,EAAC,C;;ACA3O,IAAI,kBAAM,EAAE,2BAAe;AACoC;AACL;;;AAG1D;AAC0F;AAC1F,IAAI,qBAAS,GAAG,kBAAU;AAC1B,EAAE,8CAAM;AACR,EAAE,kBAAM;AACR,EAAE,2BAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,wBAYf;AACD,qBAAS;AACM,qEAAS,Q;;ACjCxB,IAAI,0CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,IAAI,mDAAe;AACnB,0CAAM;;;;;;ACvBS;AACf;AACA,CAAC,E;;ACF0N,CAAgB,uGAAG,EAAC,C;;ACA1J;AAC3B;AACL;;;AAGrD;AAC6F;AAC7F,IAAI,gBAAS,GAAG,kBAAU;AAC1B,EAAE,oCAAM;AACR,EAAE,0CAAM;AACR,EAAE,mDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,mBAiBf;AACD,gBAAS;AACM,2DAAS,Q;;ACtCe;AACC;AACzB;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,qBAAqB,WAAW;AAChC;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK,KAAK,MAAU;AACpB;AACA,CAAC,E;;AC5CyN,CAAgB,4HAAG,EAAC,C;;ACA9O,IAAI,qBAAM,EAAE,8BAAe;AACuC;AACL;;;AAG7D;AAC0F;AAC1F,IAAI,wBAAS,GAAG,kBAAU;AAC1B,EAAE,iDAAM;AACR,EAAE,qBAAM;AACR,EAAE,8BAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,2BAYf;AACD,wBAAS;AACM,2EAAS,Q;;ACjCsC;AAChB;AAClB;AACY;AACzB;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,cAAc;AAC/B;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,mDAAc;AACpE;AACA,KAAK,wFAAwF,WAAW;AACxG;AACA,KAAK,KAAK,KAAK;AACf;AACA;AACA,KAAK;AACL;AACA,CAAC,E;;ACpDqN,CAAgB,oHAAG,EAAC,C;;ACA1O,IAAI,iBAAM,EAAE,0BAAe;AACmC;AACL;;;AAGzD;AAC0F;AAC1F,IAAI,oBAAS,GAAG,kBAAU;AAC1B,EAAE,6CAAM;AACR,EAAE,iBAAM;AACR,EAAE,0BAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,uBAYf;AACD,oBAAS;AACM,mEAAS,Q;;ACjCxB,IAAI,yCAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,IAAI,kDAAe;AACnB,yCAAM;;;;;;ACvBS;AACf;AACA,CAAC,E;;ACFyN,CAAgB,qGAAG,EAAC,C;;ACA1J;AAC3B;AACL;;;AAGpD;AAC6F;AAC7F,IAAI,eAAS,GAAG,kBAAU;AAC1B,EAAE,mCAAM;AACR,EAAE,yCAAM;AACR,EAAE,kDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,kBAiBf;AACD,eAAS;AACM,yDAAS,Q;;ACtC0B;AACV;AACF;AACE;AACF;AACvB;AACf;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO,KAAK,MAAU;AACtB;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAK,KAAS;AACrB;AACA,OAAO;AACP,KAAK;AACL,wBAAwB,WAAW;AACnC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,UAAU,6BAAS;AACnB;AACA,OAAO;AACP;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL,4BAA4B,WAAW;AACvC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;AACA,2CAA2C,WAAW,GAAG,UAAU;AACnE;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,CAAC,E;;ACzGkN,CAAgB,8GAAG,EAAC,C;;ACAvO,IAAI,cAAM,EAAE,uBAAe;AACgC;AACL;;;AAGtD;AAC0F;AAC1F,IAAI,iBAAS,GAAG,kBAAU;AAC1B,EAAE,0CAAM;AACR,EAAE,cAAM;AACR,EAAE,uBAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,oBAYf;AACD,iBAAS;AACM,6DAAS,Q;;ACjCT;AACf;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,CAAC,E;;AC3B8M,CAAgB,sGAAG,EAAC,C;;ACAnO,IAAI,UAAM,EAAE,mBAAe;AAC4B;AACL;;;AAGlD;AAC0F;AAC1F,IAAI,aAAS,GAAG,kBAAU;AAC1B,EAAE,sCAAM;AACR,EAAE,UAAM;AACR,EAAE,mBAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,gBAYf;AACD,aAAS;AACM,qDAAS,Q;;ACjC4C;AACH;AAC1B;AACf;AACc;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,uCAAuC,KAAS;AACzD;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,OAAO;AACrE,oEAAoE,aAAa;AACjF,gEAAgE,SAAS;AACzE;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,eAAe,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,eAAe,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,eAAe,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,4BAA4B,WAAW;AACvC;AACA;AACA;AACA,KAAK;AACL,qCAAqC,WAAW;AAChD;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL,4BAA4B,WAAW;AACvC;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;;AAEA,wBAAwB,wBAAe;AACvC;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACe,yEAAM,E;;ACnQ6L,CAAgB,4GAAG,EAAC,C;;ACAtO,IAAI,aAAM,EAAE,sBAAe;AAC+B;AACL;;;AAGrD;AAC0F;AAC1F,IAAI,gBAAS,GAAG,kBAAU;AAC1B,EAAE,yCAAM;AACR,EAAE,aAAM;AACR,EAAE,sBAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,mBAYf;AACD,gBAAS;AACM,sEAAS,Q;;ACjCmB;AAC8B;AAC3C;AACN;AACxB;AACA;AACA;AACA;AACA;AACA;AACe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,iBAAiB,iBAAM;AACvB;AACA;AACA,WAAW;AACX;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,eAAe,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,eAAe,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,eAAe,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,eAAe,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,eAAe,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,eAAe,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,WAAW;AACrE,0DAA0D,WAAW;;AAErE;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,gBAAgB,SAAS;AACzB;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,gBAAgB,kCAAkC;AAClD;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC,E;;ACzR+M,CAAgB,wGAAG,EAAC,C;;ACApO,IAAI,WAAM,EAAE,oBAAe;AAC6B;AACL;;;AAGnD;AAC0F;AAC1F,IAAI,cAAS,GAAG,kBAAU;AAC1B,EAAE,uCAAM;AACR,EAAE,WAAM;AACR,EAAE,oBAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,iBAYf;AACD,cAAS;AACM,uDAAS,Q;;;;;;ACjC4C;;AAEpE,SAAS,yCAAO,0BAA0B,gCAAgC,oCAAoC,oDAAoD,8DAA8D,gEAAgE,EAAE,EAAE,gCAAgC,EAAE,aAAa;;AAEnV,SAAS,8CAAa,UAAU,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,CAAC,yCAAO,uCAAuC,CAAC,wBAAe,2BAA2B,EAAE,EAAE,EAAE,6CAA6C,2EAA2E,EAAE,OAAO,CAAC,yCAAO,iCAAiC,kFAAkF,EAAE,EAAE,EAAE,EAAE,eAAe;;AAE9e;AACyD;AACrD;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,gBAAgB,kCAAkC;AAClD;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,gBAAgB,SAAS;AACzB;;AAEA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,IAAI;AAC1B;AACA,OAAO;AACP;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,KAAK,IAAI;AACd;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACe;AACf;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,8BAA8B,sBAAG,CAAC,8CAAa;AAC/C;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC,E;;AC3JqN,CAAgB,oHAAG,EAAC,C;;ACA1O,IAAI,iBAAM,EAAE,0BAAe;AACmC;AACL;;;AAGzD;AAC0F;AAC1F,IAAI,oBAAS,GAAG,kBAAU;AAC1B,EAAE,6CAAM;AACR,EAAE,iBAAM;AACR,EAAE,0BAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,uBAYf;AACD,oBAAS;AACM,mEAAS,Q;;ACjCgC;AACd;AACV;AACN;AACY;AACvB;AACf;AACA,WAAW,eAAe;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,KAAK,KAAK,YAAY,KAAK,OAAO;AAClC;AACA,KAAK,yBAAyB,UAAU;AACxC;AACA,KAAK,MAAM,IAAI;AACf;AACA,KAAK;AACL;AACA,CAAC,E;;ACvCqN,CAAgB,oHAAG,EAAC,C;;ACA1O,IAAI,iBAAM,EAAE,0BAAe;AACmC;AACL;;;AAGzD;AAC0F;AAC1F,IAAI,oBAAS,GAAG,kBAAU;AAC1B,EAAE,6CAAM;AACR,EAAE,iBAAM;AACR,EAAE,0BAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,uBAYf;AACD,oBAAS;AACM,mEAAS,Q;;;;;ACjCxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiD;AACM;AACjC;AACP,mFAAU,EAAC;AACa;AAC8C;AAC9E,cAAc,OAAW,C", "file": "vue-treeselect.cjs.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 16);\n", "module.exports = require(\"@babel/runtime/helpers/slicedToArray\");", "module.exports = require(\"@babel/runtime/helpers/toConsumableArray\");", "module.exports = require(\"@babel/runtime/helpers/defineProperty\");", "module.exports = require(\"fuzzysearch\");", "module.exports = require(\"lodash/noop\");", "module.exports = require(\"lodash/debounce\");", "module.exports = require(\"watch-size\");", "module.exports = require(\"is-promise\");", "module.exports = require(\"lodash/once\");", "module.exports = require(\"lodash/identity\");", "module.exports = require(\"lodash/constant\");", "module.exports = require(\"@babel/runtime/helpers/typeof\");", "module.exports = require(\"lodash/last\");", "module.exports = require(\"babel-helper-vue-jsx-merge-props\");", "module.exports = require(\"vue\");", "// extracted by mini-css-extract-plugin", "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport { noop } from './noop';\nexport var warning = process.env.NODE_ENV === 'production' ? noop : function warning(checker, complainer) {\n  if (!checker()) {\n    var _console;\n\n    var message = ['[Vue-Treeselect Warning]'].concat(complainer());\n\n    (_console = console).error.apply(_console, _toConsumableArray(message));\n  }\n};", "export function onLeftClick(mouseDownHandler) {\n  return function onMouseDown(evt) {\n    if (evt.type === 'mousedown' && evt.button === 0) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      mouseDownHandler.call.apply(mouseDownHandler, [this, evt].concat(args));\n    }\n  };\n}", "export function scrollIntoView($scrollingEl, $focusedEl) {\n  var scrollingReact = $scrollingEl.getBoundingClientRect();\n  var focusedRect = $focusedEl.getBoundingClientRect();\n  var overScroll = $focusedEl.offsetHeight / 3;\n\n  if (focusedRect.bottom + overScroll > scrollingReact.bottom) {\n    $scrollingEl.scrollTop = Math.min($focusedEl.offsetTop + $focusedEl.clientHeight - $scrollingEl.offsetHeight + overScroll, $scrollingEl.scrollHeight);\n  } else if (focusedRect.top - overScroll < scrollingReact.top) {\n    $scrollingEl.scrollTop = Math.max($focusedEl.offsetTop - overScroll, 0);\n  }\n}", "export function removeFromArray(arr, elem) {\n  var idx = arr.indexOf(elem);\n  if (idx !== -1) arr.splice(idx, 1);\n}", "import watchSizeForBrowsersOtherThanIE9 from 'watch-size';\nimport { removeFromArray } from './removeFromArray';\nvar intervalId;\nvar registered = [];\nvar INTERVAL_DURATION = 100;\n\nfunction run() {\n  intervalId = setInterval(function () {\n    registered.forEach(test);\n  }, INTERVAL_DURATION);\n}\n\nfunction stop() {\n  clearInterval(intervalId);\n  intervalId = null;\n}\n\nfunction test(item) {\n  var $el = item.$el,\n      listener = item.listener,\n      lastWidth = item.lastWidth,\n      lastHeight = item.lastHeight;\n  var width = $el.offsetWidth;\n  var height = $el.offsetHeight;\n\n  if (lastWidth !== width || lastHeight !== height) {\n    item.lastWidth = width;\n    item.lastHeight = height;\n    listener({\n      width: width,\n      height: height\n    });\n  }\n}\n\nfunction watchSizeForIE9($el, listener) {\n  var item = {\n    $el: $el,\n    listener: listener,\n    lastWidth: null,\n    lastHeight: null\n  };\n\n  var unwatch = function unwatch() {\n    removeFromArray(registered, item);\n    if (!registered.length) stop();\n  };\n\n  registered.push(item);\n  test(item);\n  run();\n  return unwatch;\n}\n\nexport function watchSize($el, listener) {\n  var isIE9 = document.documentMode === 9;\n  var locked = true;\n\n  var wrappedListener = function wrappedListener() {\n    return locked || listener.apply(void 0, arguments);\n  };\n\n  var implementation = isIE9 ? watchSizeForIE9 : watchSizeForBrowsersOtherThanIE9;\n  var removeSizeWatcher = implementation($el, wrappedListener);\n  locked = false;\n  return removeSizeWatcher;\n}", "function findScrollParents($el) {\n  var $scrollParents = [];\n  var $parent = $el.parentNode;\n\n  while ($parent && $parent.nodeName !== 'BODY' && $parent.nodeType === document.ELEMENT_NODE) {\n    if (isScrollElment($parent)) $scrollParents.push($parent);\n    $parent = $parent.parentNode;\n  }\n\n  $scrollParents.push(window);\n  return $scrollParents;\n}\n\nfunction isScrollElment($el) {\n  var _getComputedStyle = getComputedStyle($el),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /(auto|scroll|overlay)/.test(overflow + overflowY + overflowX);\n}\n\nexport function setupResizeAndScrollEventListeners($el, listener) {\n  var $scrollParents = findScrollParents($el);\n  window.addEventListener('resize', listener, {\n    passive: true\n  });\n  $scrollParents.forEach(function (scrollParent) {\n    scrollParent.addEventListener('scroll', listener, {\n      passive: true\n    });\n  });\n  return function removeEventListeners() {\n    window.removeEventListener('resize', listener, {\n      passive: true\n    });\n    $scrollParents.forEach(function ($scrollParent) {\n      $scrollParent.removeEventListener('scroll', listener, {\n        passive: true\n      });\n    });\n  };\n}", "export function isNaN(x) {\n  return x !== x;\n}", "export var createMap = function createMap() {\n  return Object.create(null);\n};", "import _typeof from \"@babel/runtime/helpers/typeof\";\n\nfunction isPlainObject(value) {\n  if (value == null || _typeof(value) !== 'object') return false;\n  return Object.getPrototypeOf(value) === Object.prototype;\n}\n\nfunction copy(obj, key, value) {\n  if (isPlainObject(value)) {\n    obj[key] || (obj[key] = {});\n    deepExtend(obj[key], value);\n  } else {\n    obj[key] = value;\n  }\n}\n\nexport function deepExtend(target, source) {\n  if (isPlainObject(source)) {\n    var keys = Object.keys(source);\n\n    for (var i = 0, len = keys.length; i < len; i++) {\n      copy(target, keys[i], source[keys[i]]);\n    }\n  }\n\n  return target;\n}", "export function includes(arrOrStr, elem) {\n  return arrOrStr.indexOf(elem) !== -1;\n}", "export function find(arr, predicate, ctx) {\n  for (var i = 0, len = arr.length; i < len; i++) {\n    if (predicate.call(ctx, arr[i], i, arr)) return arr[i];\n  }\n\n  return undefined;\n}", "export function quickDiff(arrA, arrB) {\n  if (arrA.length !== arrB.length) return true;\n\n  for (var i = 0; i < arrA.length; i++) {\n    if (arrA[i] !== arrB[i]) return true;\n  }\n\n  return false;\n}", "export { warning } from './warning';\nexport { onLeftClick } from './onLeftClick';\nexport { scrollIntoView } from './scrollIntoView';\nexport { debounce } from './debounce';\nexport { watchSize } from './watchSize';\nexport { setupResizeAndScrollEventListeners } from './setupResizeAndScrollEventListeners';\nexport { isNaN } from './isNaN';\nexport { isPromise } from './isPromise';\nexport { once } from './once';\nexport { noop } from './noop';\nexport { identity } from './identity';\nexport { constant } from './constant';\nexport { createMap } from './createMap';\nexport { deepExtend } from './deepExtend';\nexport { last } from './last';\nexport { includes } from './includes';\nexport { find } from './find';\nexport { removeFromArray } from './removeFromArray';\nexport { quickDiff } from './quickDiff';", "export var NO_PARENT_NODE = null;\nexport var UNCHECKED = 0;\nexport var INDETERMINATE = 1;\nexport var CHECKED = 2;\nexport var ALL_CHILDREN = 'ALL_CHILDREN';\nexport var ALL_DESCENDANTS = 'ALL_DESCENDANTS';\nexport var LEAF_CHILDREN = 'LEAF_CHILDREN';\nexport var LEAF_DESCENDANTS = 'LEAF_DESCENDANTS';\nexport var LOAD_ROOT_OPTIONS = 'LOAD_ROOT_OPTIONS';\nexport var LOAD_CHILDREN_OPTIONS = 'LOAD_CHILDREN_OPTIONS';\nexport var ASYNC_SEARCH = 'ASYNC_SEARCH';\nexport var ALL = 'ALL';\nexport var BRANCH_PRIORITY = 'BRANCH_PRIORITY';\nexport var LEAF_PRIORITY = 'LEAF_PRIORITY';\nexport var ALL_WITH_INDETERMINATE = 'ALL_WITH_INDETERMINATE';\nexport var ORDER_SELECTED = 'ORDER_SELECTED';\nexport var LEVEL = 'LEVEL';\nexport var INDEX = 'INDEX';\nexport var KEY_CODES = {\n  BACKSPACE: 8,\n  ENTER: 13,\n  ESCAPE: 27,\n  END: 35,\n  HOME: 36,\n  ARROW_LEFT: 37,\n  ARROW_UP: 38,\n  ARROW_RIGHT: 39,\n  ARROW_DOWN: 40,\n  DELETE: 46\n};\nexport var INPUT_DEBOUNCE_DELAY = process.env.NODE_ENV === 'testing' ? 10 : 200;\nexport var MIN_INPUT_WIDTH = 5;\nexport var MENU_BUFFER = 40;", "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport fuzzysearch from 'fuzzysearch';\nimport { warning, onLeftClick, scrollIntoView, isNaN, isPromise, once, identity, constant, createMap, quickDiff, last as getLast, includes, find, removeFromArray } from '../utils';\nimport { NO_PARENT_NODE, UNCHECKED, INDETERMINATE, CHECKED, LOAD_ROOT_OPTIONS, LOAD_CHILDREN_OPTIONS, ASYNC_SEARCH, ALL, BRANCH_PRIORITY, LEAF_PRIORITY, ALL_WITH_INDETERMINATE, ALL_CHILDREN, ALL_DESCENDANTS, LEAF_CHILDREN, LEAF_DESCENDANTS, ORDER_SELECTED, LEVEL, INDEX } from '../constants';\n\nfunction sortValueByIndex(a, b) {\n  var i = 0;\n\n  do {\n    if (a.level < i) return -1;\n    if (b.level < i) return 1;\n    if (a.index[i] !== b.index[i]) return a.index[i] - b.index[i];\n    i++;\n  } while (true);\n}\n\nfunction sortValueByLevel(a, b) {\n  return a.level === b.level ? sortValueByIndex(a, b) : a.level - b.level;\n}\n\nfunction createAsyncOptionsStates() {\n  return {\n    isLoaded: false,\n    isLoading: false,\n    loadingError: ''\n  };\n}\n\nfunction stringifyOptionPropValue(value) {\n  if (typeof value === 'string') return value;\n  if (typeof value === 'number' && !isNaN(value)) return value + '';\n  return '';\n}\n\nfunction match(enableFuzzyMatch, needle, haystack) {\n  return enableFuzzyMatch ? fuzzysearch(needle, haystack) : includes(haystack, needle);\n}\n\nfunction getErrorMessage(err) {\n  return err.message || String(err);\n}\n\nvar instanceId = 0;\nexport default {\n  provide: function provide() {\n    return {\n      instance: this\n    };\n  },\n  props: {\n    allowClearingDisabled: {\n      type: Boolean,\n      default: false\n    },\n    allowSelectingDisabledDescendants: {\n      type: Boolean,\n      default: false\n    },\n    alwaysOpen: {\n      type: Boolean,\n      default: false\n    },\n    appendToBody: {\n      type: Boolean,\n      default: false\n    },\n    async: {\n      type: Boolean,\n      default: false\n    },\n    autoFocus: {\n      type: Boolean,\n      default: false\n    },\n    autoLoadRootOptions: {\n      type: Boolean,\n      default: true\n    },\n    autoDeselectAncestors: {\n      type: Boolean,\n      default: false\n    },\n    autoDeselectDescendants: {\n      type: Boolean,\n      default: false\n    },\n    autoSelectAncestors: {\n      type: Boolean,\n      default: false\n    },\n    autoSelectDescendants: {\n      type: Boolean,\n      default: false\n    },\n    backspaceRemoves: {\n      type: Boolean,\n      default: true\n    },\n    beforeClearAll: {\n      type: Function,\n      default: constant(true)\n    },\n    branchNodesFirst: {\n      type: Boolean,\n      default: false\n    },\n    cacheOptions: {\n      type: Boolean,\n      default: true\n    },\n    clearable: {\n      type: Boolean,\n      default: true\n    },\n    clearAllText: {\n      type: String,\n      default: 'Clear all'\n    },\n    clearOnSelect: {\n      type: Boolean,\n      default: false\n    },\n    clearValueText: {\n      type: String,\n      default: 'Clear value'\n    },\n    closeOnSelect: {\n      type: Boolean,\n      default: true\n    },\n    defaultExpandLevel: {\n      type: Number,\n      default: 0\n    },\n    defaultOptions: {\n      default: false\n    },\n    deleteRemoves: {\n      type: Boolean,\n      default: true\n    },\n    delimiter: {\n      type: String,\n      default: ','\n    },\n    flattenSearchResults: {\n      type: Boolean,\n      default: false\n    },\n    disableBranchNodes: {\n      type: Boolean,\n      default: false\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    disableFuzzyMatching: {\n      type: Boolean,\n      default: false\n    },\n    flat: {\n      type: Boolean,\n      default: false\n    },\n    instanceId: {\n      default: function _default() {\n        return \"\".concat(instanceId++, \"$$\");\n      },\n      type: [String, Number]\n    },\n    joinValues: {\n      type: Boolean,\n      default: false\n    },\n    limit: {\n      type: Number,\n      default: Infinity\n    },\n    limitText: {\n      type: Function,\n      default: function limitTextDefault(count) {\n        return \"and \".concat(count, \" more\");\n      }\n    },\n    loadingText: {\n      type: String,\n      default: 'Loading...'\n    },\n    loadOptions: {\n      type: Function\n    },\n    matchKeys: {\n      type: Array,\n      default: constant(['label'])\n    },\n    maxHeight: {\n      type: Number,\n      default: 300\n    },\n    multiple: {\n      type: Boolean,\n      default: false\n    },\n    name: {\n      type: String\n    },\n    noChildrenText: {\n      type: String,\n      default: 'No sub-options.'\n    },\n    noOptionsText: {\n      type: String,\n      default: 'No options available.'\n    },\n    noResultsText: {\n      type: String,\n      default: 'No results found...'\n    },\n    normalizer: {\n      type: Function,\n      default: identity\n    },\n    openDirection: {\n      type: String,\n      default: 'auto',\n      validator: function validator(value) {\n        var acceptableValues = ['auto', 'top', 'bottom', 'above', 'below'];\n        return includes(acceptableValues, value);\n      }\n    },\n    openOnClick: {\n      type: Boolean,\n      default: true\n    },\n    openOnFocus: {\n      type: Boolean,\n      default: false\n    },\n    options: {\n      type: Array\n    },\n    placeholder: {\n      type: String,\n      default: 'Select...'\n    },\n    required: {\n      type: Boolean,\n      default: false\n    },\n    retryText: {\n      type: String,\n      default: 'Retry?'\n    },\n    retryTitle: {\n      type: String,\n      default: 'Click to retry'\n    },\n    searchable: {\n      type: Boolean,\n      default: true\n    },\n    searchNested: {\n      type: Boolean,\n      default: false\n    },\n    searchPromptText: {\n      type: String,\n      default: 'Type to search...'\n    },\n    showCount: {\n      type: Boolean,\n      default: false\n    },\n    showCountOf: {\n      type: String,\n      default: ALL_CHILDREN,\n      validator: function validator(value) {\n        var acceptableValues = [ALL_CHILDREN, ALL_DESCENDANTS, LEAF_CHILDREN, LEAF_DESCENDANTS];\n        return includes(acceptableValues, value);\n      }\n    },\n    showCountOnSearch: null,\n    sortValueBy: {\n      type: String,\n      default: ORDER_SELECTED,\n      validator: function validator(value) {\n        var acceptableValues = [ORDER_SELECTED, LEVEL, INDEX];\n        return includes(acceptableValues, value);\n      }\n    },\n    tabIndex: {\n      type: Number,\n      default: 0\n    },\n    value: null,\n    valueConsistsOf: {\n      type: String,\n      default: BRANCH_PRIORITY,\n      validator: function validator(value) {\n        var acceptableValues = [ALL, BRANCH_PRIORITY, LEAF_PRIORITY, ALL_WITH_INDETERMINATE];\n        return includes(acceptableValues, value);\n      }\n    },\n    valueFormat: {\n      type: String,\n      default: 'id'\n    },\n    zIndex: {\n      type: [Number, String],\n      default: 999\n    }\n  },\n  data: function data() {\n    return {\n      trigger: {\n        isFocused: false,\n        searchQuery: ''\n      },\n      menu: {\n        isOpen: false,\n        current: null,\n        lastScrollPosition: 0,\n        placement: 'bottom'\n      },\n      forest: {\n        normalizedOptions: [],\n        nodeMap: createMap(),\n        checkedStateMap: createMap(),\n        selectedNodeIds: this.extractCheckedNodeIdsFromValue(),\n        selectedNodeMap: createMap()\n      },\n      rootOptionsStates: createAsyncOptionsStates(),\n      localSearch: {\n        active: false,\n        noResults: true,\n        countMap: createMap()\n      },\n      remoteSearch: createMap()\n    };\n  },\n  computed: {\n    selectedNodes: function selectedNodes() {\n      return this.forest.selectedNodeIds.map(this.getNode);\n    },\n    internalValue: function internalValue() {\n      var _this = this;\n\n      var internalValue;\n\n      if (this.single || this.flat || this.disableBranchNodes || this.valueConsistsOf === ALL) {\n        internalValue = this.forest.selectedNodeIds.slice();\n      } else if (this.valueConsistsOf === BRANCH_PRIORITY) {\n        internalValue = this.forest.selectedNodeIds.filter(function (id) {\n          var node = _this.getNode(id);\n\n          if (node.isRootNode) return true;\n          return !_this.isSelected(node.parentNode);\n        });\n      } else if (this.valueConsistsOf === LEAF_PRIORITY) {\n        internalValue = this.forest.selectedNodeIds.filter(function (id) {\n          var node = _this.getNode(id);\n\n          if (node.isLeaf) return true;\n          return node.children.length === 0;\n        });\n      } else if (this.valueConsistsOf === ALL_WITH_INDETERMINATE) {\n        var _internalValue;\n\n        var indeterminateNodeIds = [];\n        internalValue = this.forest.selectedNodeIds.slice();\n        this.selectedNodes.forEach(function (selectedNode) {\n          selectedNode.ancestors.forEach(function (ancestor) {\n            if (includes(indeterminateNodeIds, ancestor.id)) return;\n            if (includes(internalValue, ancestor.id)) return;\n            indeterminateNodeIds.push(ancestor.id);\n          });\n        });\n\n        (_internalValue = internalValue).push.apply(_internalValue, indeterminateNodeIds);\n      }\n\n      if (this.sortValueBy === LEVEL) {\n        internalValue.sort(function (a, b) {\n          return sortValueByLevel(_this.getNode(a), _this.getNode(b));\n        });\n      } else if (this.sortValueBy === INDEX) {\n        internalValue.sort(function (a, b) {\n          return sortValueByIndex(_this.getNode(a), _this.getNode(b));\n        });\n      }\n\n      return internalValue;\n    },\n    hasValue: function hasValue() {\n      return this.internalValue.length > 0;\n    },\n    single: function single() {\n      return !this.multiple;\n    },\n    visibleOptionIds: function visibleOptionIds() {\n      var _this2 = this;\n\n      var visibleOptionIds = [];\n      this.traverseAllNodesByIndex(function (node) {\n        if (!_this2.localSearch.active || _this2.shouldOptionBeIncludedInSearchResult(node)) {\n          visibleOptionIds.push(node.id);\n        }\n\n        if (node.isBranch && !_this2.shouldExpand(node)) {\n          return false;\n        }\n      });\n      return visibleOptionIds;\n    },\n    hasVisibleOptions: function hasVisibleOptions() {\n      return this.visibleOptionIds.length !== 0;\n    },\n    showCountOnSearchComputed: function showCountOnSearchComputed() {\n      return typeof this.showCountOnSearch === 'boolean' ? this.showCountOnSearch : this.showCount;\n    },\n    hasBranchNodes: function hasBranchNodes() {\n      return this.forest.normalizedOptions.some(function (rootNode) {\n        return rootNode.isBranch;\n      });\n    },\n    shouldFlattenOptions: function shouldFlattenOptions() {\n      return this.localSearch.active && this.flattenSearchResults;\n    }\n  },\n  watch: {\n    alwaysOpen: function alwaysOpen(newValue) {\n      if (newValue) this.openMenu();else this.closeMenu();\n    },\n    branchNodesFirst: function branchNodesFirst() {\n      this.initialize();\n    },\n    disabled: function disabled(newValue) {\n      if (newValue && this.menu.isOpen) this.closeMenu();else if (!newValue && !this.menu.isOpen && this.alwaysOpen) this.openMenu();\n    },\n    flat: function flat() {\n      this.initialize();\n    },\n    internalValue: function internalValue(newValue, oldValue) {\n      var hasChanged = quickDiff(newValue, oldValue);\n      if (hasChanged) this.$emit('input', this.getValue(), this.getInstanceId());\n    },\n    matchKeys: function matchKeys() {\n      this.initialize();\n    },\n    multiple: function multiple(newValue) {\n      if (newValue) this.buildForestState();\n    },\n    options: {\n      handler: function handler() {\n        if (this.async) return;\n        this.initialize();\n        this.rootOptionsStates.isLoaded = Array.isArray(this.options);\n      },\n      deep: true,\n      immediate: true\n    },\n    'trigger.searchQuery': function triggerSearchQuery() {\n      if (this.async) {\n        this.handleRemoteSearch();\n      } else {\n        this.handleLocalSearch();\n      }\n\n      this.$emit('search-change', this.trigger.searchQuery, this.getInstanceId());\n    },\n    value: function value() {\n      var nodeIdsFromValue = this.extractCheckedNodeIdsFromValue();\n      var hasChanged = quickDiff(nodeIdsFromValue, this.internalValue);\n      if (hasChanged) this.fixSelectedNodeIds(nodeIdsFromValue);\n    }\n  },\n  methods: {\n    verifyProps: function verifyProps() {\n      var _this3 = this;\n\n      warning(function () {\n        return _this3.async ? _this3.searchable : true;\n      }, function () {\n        return 'For async search mode, the value of \"searchable\" prop must be true.';\n      });\n\n      if (this.options == null && !this.loadOptions) {\n        warning(function () {\n          return false;\n        }, function () {\n          return 'Are you meant to dynamically load options? You need to use \"loadOptions\" prop.';\n        });\n      }\n\n      if (this.flat) {\n        warning(function () {\n          return _this3.multiple;\n        }, function () {\n          return 'You are using flat mode. But you forgot to add \"multiple=true\"?';\n        });\n      }\n\n      if (!this.flat) {\n        var propNames = ['autoSelectAncestors', 'autoSelectDescendants', 'autoDeselectAncestors', 'autoDeselectDescendants'];\n        propNames.forEach(function (propName) {\n          warning(function () {\n            return !_this3[propName];\n          }, function () {\n            return \"\\\"\".concat(propName, \"\\\" only applies to flat mode.\");\n          });\n        });\n      }\n    },\n    resetFlags: function resetFlags() {\n      this._blurOnSelect = false;\n    },\n    initialize: function initialize() {\n      var options = this.async ? this.getRemoteSearchEntry().options : this.options;\n\n      if (Array.isArray(options)) {\n        var prevNodeMap = this.forest.nodeMap;\n        this.forest.nodeMap = createMap();\n        this.keepDataOfSelectedNodes(prevNodeMap);\n        this.forest.normalizedOptions = this.normalize(NO_PARENT_NODE, options, prevNodeMap);\n        this.fixSelectedNodeIds(this.internalValue);\n      } else {\n        this.forest.normalizedOptions = [];\n      }\n    },\n    getInstanceId: function getInstanceId() {\n      return this.instanceId == null ? this.id : this.instanceId;\n    },\n    getValue: function getValue() {\n      var _this4 = this;\n\n      if (this.valueFormat === 'id') {\n        return this.multiple ? this.internalValue.slice() : this.internalValue[0];\n      }\n\n      var rawNodes = this.internalValue.map(function (id) {\n        return _this4.getNode(id).raw;\n      });\n      return this.multiple ? rawNodes : rawNodes[0];\n    },\n    getNode: function getNode(nodeId) {\n      warning(function () {\n        return nodeId != null;\n      }, function () {\n        return \"Invalid node id: \".concat(nodeId);\n      });\n      if (nodeId == null) return null;\n      return nodeId in this.forest.nodeMap ? this.forest.nodeMap[nodeId] : this.createFallbackNode(nodeId);\n    },\n    createFallbackNode: function createFallbackNode(id) {\n      var raw = this.extractNodeFromValue(id);\n      var label = this.enhancedNormalizer(raw).label || \"\".concat(id, \" (unknown)\");\n      var fallbackNode = {\n        id: id,\n        label: label,\n        ancestors: [],\n        parentNode: NO_PARENT_NODE,\n        isFallbackNode: true,\n        isRootNode: true,\n        isLeaf: true,\n        isBranch: false,\n        isDisabled: false,\n        isNew: false,\n        index: [-1],\n        level: 0,\n        raw: raw\n      };\n      return this.$set(this.forest.nodeMap, id, fallbackNode);\n    },\n    extractCheckedNodeIdsFromValue: function extractCheckedNodeIdsFromValue() {\n      var _this5 = this;\n\n      if (this.value == null) return [];\n\n      if (this.valueFormat === 'id') {\n        return this.multiple ? this.value.slice() : [this.value];\n      }\n\n      return (this.multiple ? this.value : [this.value]).map(function (node) {\n        return _this5.enhancedNormalizer(node);\n      }).map(function (node) {\n        return node.id;\n      });\n    },\n    extractNodeFromValue: function extractNodeFromValue(id) {\n      var _this6 = this;\n\n      var defaultNode = {\n        id: id\n      };\n\n      if (this.valueFormat === 'id') {\n        return defaultNode;\n      }\n\n      var valueArray = this.multiple ? Array.isArray(this.value) ? this.value : [] : this.value ? [this.value] : [];\n      var matched = find(valueArray, function (node) {\n        return node && _this6.enhancedNormalizer(node).id === id;\n      });\n      return matched || defaultNode;\n    },\n    fixSelectedNodeIds: function fixSelectedNodeIds(nodeIdListOfPrevValue) {\n      var _this7 = this;\n\n      var nextSelectedNodeIds = [];\n\n      if (this.single || this.flat || this.disableBranchNodes || this.valueConsistsOf === ALL) {\n        nextSelectedNodeIds = nodeIdListOfPrevValue;\n      } else if (this.valueConsistsOf === BRANCH_PRIORITY) {\n        nodeIdListOfPrevValue.forEach(function (nodeId) {\n          nextSelectedNodeIds.push(nodeId);\n\n          var node = _this7.getNode(nodeId);\n\n          if (node.isBranch) _this7.traverseDescendantsBFS(node, function (descendant) {\n            nextSelectedNodeIds.push(descendant.id);\n          });\n        });\n      } else if (this.valueConsistsOf === LEAF_PRIORITY) {\n        var map = createMap();\n        var queue = nodeIdListOfPrevValue.slice();\n\n        while (queue.length) {\n          var nodeId = queue.shift();\n          var node = this.getNode(nodeId);\n          nextSelectedNodeIds.push(nodeId);\n          if (node.isRootNode) continue;\n          if (!(node.parentNode.id in map)) map[node.parentNode.id] = node.parentNode.children.length;\n          if (--map[node.parentNode.id] === 0) queue.push(node.parentNode.id);\n        }\n      } else if (this.valueConsistsOf === ALL_WITH_INDETERMINATE) {\n        var _map = createMap();\n\n        var _queue = nodeIdListOfPrevValue.filter(function (nodeId) {\n          var node = _this7.getNode(nodeId);\n\n          return node.isLeaf || node.children.length === 0;\n        });\n\n        while (_queue.length) {\n          var _nodeId = _queue.shift();\n\n          var _node = this.getNode(_nodeId);\n\n          nextSelectedNodeIds.push(_nodeId);\n          if (_node.isRootNode) continue;\n          if (!(_node.parentNode.id in _map)) _map[_node.parentNode.id] = _node.parentNode.children.length;\n          if (--_map[_node.parentNode.id] === 0) _queue.push(_node.parentNode.id);\n        }\n      }\n\n      var hasChanged = quickDiff(this.forest.selectedNodeIds, nextSelectedNodeIds);\n      if (hasChanged) this.forest.selectedNodeIds = nextSelectedNodeIds;\n      this.buildForestState();\n    },\n    keepDataOfSelectedNodes: function keepDataOfSelectedNodes(prevNodeMap) {\n      var _this8 = this;\n\n      this.forest.selectedNodeIds.forEach(function (id) {\n        if (!prevNodeMap[id]) return;\n\n        var node = _objectSpread({}, prevNodeMap[id], {\n          isFallbackNode: true\n        });\n\n        _this8.$set(_this8.forest.nodeMap, id, node);\n      });\n    },\n    isSelected: function isSelected(node) {\n      return this.forest.selectedNodeMap[node.id] === true;\n    },\n    traverseDescendantsBFS: function traverseDescendantsBFS(parentNode, callback) {\n      if (!parentNode.isBranch) return;\n      var queue = parentNode.children.slice();\n\n      while (queue.length) {\n        var currNode = queue[0];\n        if (currNode.isBranch) queue.push.apply(queue, _toConsumableArray(currNode.children));\n        callback(currNode);\n        queue.shift();\n      }\n    },\n    traverseDescendantsDFS: function traverseDescendantsDFS(parentNode, callback) {\n      var _this9 = this;\n\n      if (!parentNode.isBranch) return;\n      parentNode.children.forEach(function (child) {\n        _this9.traverseDescendantsDFS(child, callback);\n\n        callback(child);\n      });\n    },\n    traverseAllNodesDFS: function traverseAllNodesDFS(callback) {\n      var _this10 = this;\n\n      this.forest.normalizedOptions.forEach(function (rootNode) {\n        _this10.traverseDescendantsDFS(rootNode, callback);\n\n        callback(rootNode);\n      });\n    },\n    traverseAllNodesByIndex: function traverseAllNodesByIndex(callback) {\n      var walk = function walk(parentNode) {\n        parentNode.children.forEach(function (child) {\n          if (callback(child) !== false && child.isBranch) {\n            walk(child);\n          }\n        });\n      };\n\n      walk({\n        children: this.forest.normalizedOptions\n      });\n    },\n    toggleClickOutsideEvent: function toggleClickOutsideEvent(enabled) {\n      if (enabled) {\n        document.addEventListener('mousedown', this.handleClickOutside, false);\n      } else {\n        document.removeEventListener('mousedown', this.handleClickOutside, false);\n      }\n    },\n    getValueContainer: function getValueContainer() {\n      return this.$refs.control.$refs['value-container'];\n    },\n    getInput: function getInput() {\n      return this.getValueContainer().$refs.input;\n    },\n    focusInput: function focusInput() {\n      this.getInput().focus();\n    },\n    blurInput: function blurInput() {\n      this.getInput().blur();\n    },\n    handleMouseDown: onLeftClick(function handleMouseDown(evt) {\n      evt.preventDefault();\n      evt.stopPropagation();\n      if (this.disabled) return;\n      var isClickedOnValueContainer = this.getValueContainer().$el.contains(evt.target);\n\n      if (isClickedOnValueContainer && !this.menu.isOpen && (this.openOnClick || this.trigger.isFocused)) {\n        this.openMenu();\n      }\n\n      if (this._blurOnSelect) {\n        this.blurInput();\n      } else {\n        this.focusInput();\n      }\n\n      this.resetFlags();\n    }),\n    handleClickOutside: function handleClickOutside(evt) {\n      if (this.$refs.wrapper && !this.$refs.wrapper.contains(evt.target)) {\n        this.blurInput();\n        this.closeMenu();\n      }\n    },\n    handleLocalSearch: function handleLocalSearch() {\n      var _this11 = this;\n\n      var searchQuery = this.trigger.searchQuery;\n\n      var done = function done() {\n        return _this11.resetHighlightedOptionWhenNecessary(true);\n      };\n\n      if (!searchQuery) {\n        this.localSearch.active = false;\n        return done();\n      }\n\n      this.localSearch.active = true;\n      this.localSearch.noResults = true;\n      this.traverseAllNodesDFS(function (node) {\n        if (node.isBranch) {\n          var _this11$$set;\n\n          node.isExpandedOnSearch = false;\n          node.showAllChildrenOnSearch = false;\n          node.isMatched = false;\n          node.hasMatchedDescendants = false;\n\n          _this11.$set(_this11.localSearch.countMap, node.id, (_this11$$set = {}, _defineProperty(_this11$$set, ALL_CHILDREN, 0), _defineProperty(_this11$$set, ALL_DESCENDANTS, 0), _defineProperty(_this11$$set, LEAF_CHILDREN, 0), _defineProperty(_this11$$set, LEAF_DESCENDANTS, 0), _this11$$set));\n        }\n      });\n      var lowerCasedSearchQuery = searchQuery.trim().toLocaleLowerCase();\n      var splitSearchQuery = lowerCasedSearchQuery.replace(/\\s+/g, ' ').split(' ');\n      this.traverseAllNodesDFS(function (node) {\n        if (_this11.searchNested && splitSearchQuery.length > 1) {\n          node.isMatched = splitSearchQuery.every(function (filterValue) {\n            return match(false, filterValue, node.nestedSearchLabel);\n          });\n        } else {\n          node.isMatched = _this11.matchKeys.some(function (matchKey) {\n            return match(!_this11.disableFuzzyMatching, lowerCasedSearchQuery, node.lowerCased[matchKey]);\n          });\n        }\n\n        if (node.isMatched) {\n          _this11.localSearch.noResults = false;\n          node.ancestors.forEach(function (ancestor) {\n            return _this11.localSearch.countMap[ancestor.id][ALL_DESCENDANTS]++;\n          });\n          if (node.isLeaf) node.ancestors.forEach(function (ancestor) {\n            return _this11.localSearch.countMap[ancestor.id][LEAF_DESCENDANTS]++;\n          });\n\n          if (node.parentNode !== NO_PARENT_NODE) {\n            _this11.localSearch.countMap[node.parentNode.id][ALL_CHILDREN] += 1;\n            if (node.isLeaf) _this11.localSearch.countMap[node.parentNode.id][LEAF_CHILDREN] += 1;\n          }\n        }\n\n        if ((node.isMatched || node.isBranch && node.isExpandedOnSearch) && node.parentNode !== NO_PARENT_NODE) {\n          node.parentNode.isExpandedOnSearch = true;\n          node.parentNode.hasMatchedDescendants = true;\n        }\n      });\n      done();\n    },\n    handleRemoteSearch: function handleRemoteSearch() {\n      var _this12 = this;\n\n      var searchQuery = this.trigger.searchQuery;\n      var entry = this.getRemoteSearchEntry();\n\n      var done = function done() {\n        _this12.initialize();\n\n        _this12.resetHighlightedOptionWhenNecessary(true);\n      };\n\n      if ((searchQuery === '' || this.cacheOptions) && entry.isLoaded) {\n        return done();\n      }\n\n      this.callLoadOptionsProp({\n        action: ASYNC_SEARCH,\n        args: {\n          searchQuery: searchQuery\n        },\n        isPending: function isPending() {\n          return entry.isLoading;\n        },\n        start: function start() {\n          entry.isLoading = true;\n          entry.isLoaded = false;\n          entry.loadingError = '';\n        },\n        succeed: function succeed(options) {\n          entry.isLoaded = true;\n          entry.options = options;\n          if (_this12.trigger.searchQuery === searchQuery) done();\n        },\n        fail: function fail(err) {\n          entry.loadingError = getErrorMessage(err);\n        },\n        end: function end() {\n          entry.isLoading = false;\n        }\n      });\n    },\n    getRemoteSearchEntry: function getRemoteSearchEntry() {\n      var _this13 = this;\n\n      var searchQuery = this.trigger.searchQuery;\n\n      var entry = this.remoteSearch[searchQuery] || _objectSpread({}, createAsyncOptionsStates(), {\n        options: []\n      });\n\n      this.$watch(function () {\n        return entry.options;\n      }, function () {\n        if (_this13.trigger.searchQuery === searchQuery) _this13.initialize();\n      }, {\n        deep: true\n      });\n\n      if (searchQuery === '') {\n        if (Array.isArray(this.defaultOptions)) {\n          entry.options = this.defaultOptions;\n          entry.isLoaded = true;\n          return entry;\n        } else if (this.defaultOptions !== true) {\n          entry.isLoaded = true;\n          return entry;\n        }\n      }\n\n      if (!this.remoteSearch[searchQuery]) {\n        this.$set(this.remoteSearch, searchQuery, entry);\n      }\n\n      return entry;\n    },\n    shouldExpand: function shouldExpand(node) {\n      return this.localSearch.active ? node.isExpandedOnSearch : node.isExpanded;\n    },\n    shouldOptionBeIncludedInSearchResult: function shouldOptionBeIncludedInSearchResult(node) {\n      if (node.isMatched) return true;\n      if (node.isBranch && node.hasMatchedDescendants && !this.flattenSearchResults) return true;\n      if (!node.isRootNode && node.parentNode.showAllChildrenOnSearch) return true;\n      return false;\n    },\n    shouldShowOptionInMenu: function shouldShowOptionInMenu(node) {\n      if (this.localSearch.active && !this.shouldOptionBeIncludedInSearchResult(node)) {\n        return false;\n      }\n\n      return true;\n    },\n    getControl: function getControl() {\n      return this.$refs.control.$el;\n    },\n    getMenu: function getMenu() {\n      var ref = this.appendToBody ? this.$refs.portal.portalTarget : this;\n      var $menu = ref.$refs.menu.$refs.menu;\n      return $menu && $menu.nodeName !== '#comment' ? $menu : null;\n    },\n    setCurrentHighlightedOption: function setCurrentHighlightedOption(node) {\n      var _this14 = this;\n\n      var scroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      var prev = this.menu.current;\n\n      if (prev != null && prev in this.forest.nodeMap) {\n        this.forest.nodeMap[prev].isHighlighted = false;\n      }\n\n      this.menu.current = node.id;\n      node.isHighlighted = true;\n\n      if (this.menu.isOpen && scroll) {\n        var scrollToOption = function scrollToOption() {\n          var $menu = _this14.getMenu();\n\n          var $option = $menu.querySelector(\".vue-treeselect__option[data-id=\\\"\".concat(node.id, \"\\\"]\"));\n          if ($option) scrollIntoView($menu, $option);\n        };\n\n        if (this.getMenu()) {\n          scrollToOption();\n        } else {\n          this.$nextTick(scrollToOption);\n        }\n      }\n    },\n    resetHighlightedOptionWhenNecessary: function resetHighlightedOptionWhenNecessary() {\n      var forceReset = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      var current = this.menu.current;\n\n      if (forceReset || current == null || !(current in this.forest.nodeMap) || !this.shouldShowOptionInMenu(this.getNode(current))) {\n        this.highlightFirstOption();\n      }\n    },\n    highlightFirstOption: function highlightFirstOption() {\n      if (!this.hasVisibleOptions) return;\n      var first = this.visibleOptionIds[0];\n      this.setCurrentHighlightedOption(this.getNode(first));\n    },\n    highlightPrevOption: function highlightPrevOption() {\n      if (!this.hasVisibleOptions) return;\n      var prev = this.visibleOptionIds.indexOf(this.menu.current) - 1;\n      if (prev === -1) return this.highlightLastOption();\n      this.setCurrentHighlightedOption(this.getNode(this.visibleOptionIds[prev]));\n    },\n    highlightNextOption: function highlightNextOption() {\n      if (!this.hasVisibleOptions) return;\n      var next = this.visibleOptionIds.indexOf(this.menu.current) + 1;\n      if (next === this.visibleOptionIds.length) return this.highlightFirstOption();\n      this.setCurrentHighlightedOption(this.getNode(this.visibleOptionIds[next]));\n    },\n    highlightLastOption: function highlightLastOption() {\n      if (!this.hasVisibleOptions) return;\n      var last = getLast(this.visibleOptionIds);\n      this.setCurrentHighlightedOption(this.getNode(last));\n    },\n    resetSearchQuery: function resetSearchQuery() {\n      this.trigger.searchQuery = '';\n    },\n    closeMenu: function closeMenu() {\n      if (!this.menu.isOpen || !this.disabled && this.alwaysOpen) return;\n      this.saveMenuScrollPosition();\n      this.menu.isOpen = false;\n      this.toggleClickOutsideEvent(false);\n      this.resetSearchQuery();\n      this.$emit('close', this.getValue(), this.getInstanceId());\n    },\n    openMenu: function openMenu() {\n      if (this.disabled || this.menu.isOpen) return;\n      this.menu.isOpen = true;\n      this.$nextTick(this.resetHighlightedOptionWhenNecessary);\n      this.$nextTick(this.restoreMenuScrollPosition);\n      if (!this.options && !this.async) this.loadRootOptions();\n      this.toggleClickOutsideEvent(true);\n      this.$emit('open', this.getInstanceId());\n    },\n    toggleMenu: function toggleMenu() {\n      if (this.menu.isOpen) {\n        this.closeMenu();\n      } else {\n        this.openMenu();\n      }\n    },\n    toggleExpanded: function toggleExpanded(node) {\n      var nextState;\n\n      if (this.localSearch.active) {\n        nextState = node.isExpandedOnSearch = !node.isExpandedOnSearch;\n        if (nextState) node.showAllChildrenOnSearch = true;\n      } else {\n        nextState = node.isExpanded = !node.isExpanded;\n      }\n\n      if (nextState && !node.childrenStates.isLoaded) {\n        this.loadChildrenOptions(node);\n      }\n    },\n    buildForestState: function buildForestState() {\n      var _this15 = this;\n\n      var selectedNodeMap = createMap();\n      this.forest.selectedNodeIds.forEach(function (selectedNodeId) {\n        selectedNodeMap[selectedNodeId] = true;\n      });\n      this.forest.selectedNodeMap = selectedNodeMap;\n      var checkedStateMap = createMap();\n\n      if (this.multiple) {\n        this.traverseAllNodesByIndex(function (node) {\n          checkedStateMap[node.id] = UNCHECKED;\n        });\n        this.selectedNodes.forEach(function (selectedNode) {\n          checkedStateMap[selectedNode.id] = CHECKED;\n\n          if (!_this15.flat && !_this15.disableBranchNodes) {\n            selectedNode.ancestors.forEach(function (ancestorNode) {\n              if (!_this15.isSelected(ancestorNode)) {\n                checkedStateMap[ancestorNode.id] = INDETERMINATE;\n              }\n            });\n          }\n        });\n      }\n\n      this.forest.checkedStateMap = checkedStateMap;\n    },\n    enhancedNormalizer: function enhancedNormalizer(raw) {\n      return _objectSpread({}, raw, {}, this.normalizer(raw, this.getInstanceId()));\n    },\n    normalize: function normalize(parentNode, nodes, prevNodeMap) {\n      var _this16 = this;\n\n      var normalizedOptions = nodes.map(function (node) {\n        return [_this16.enhancedNormalizer(node), node];\n      }).map(function (_ref, index) {\n        var _ref2 = _slicedToArray(_ref, 2),\n            node = _ref2[0],\n            raw = _ref2[1];\n\n        _this16.checkDuplication(node);\n\n        _this16.verifyNodeShape(node);\n\n        var id = node.id,\n            label = node.label,\n            children = node.children,\n            isDefaultExpanded = node.isDefaultExpanded;\n        var isRootNode = parentNode === NO_PARENT_NODE;\n        var level = isRootNode ? 0 : parentNode.level + 1;\n        var isBranch = Array.isArray(children) || children === null;\n        var isLeaf = !isBranch;\n        var isDisabled = !!node.isDisabled || !_this16.flat && !isRootNode && parentNode.isDisabled;\n        var isNew = !!node.isNew;\n\n        var lowerCased = _this16.matchKeys.reduce(function (prev, key) {\n          return _objectSpread({}, prev, _defineProperty({}, key, stringifyOptionPropValue(node[key]).toLocaleLowerCase()));\n        }, {});\n\n        var nestedSearchLabel = isRootNode ? lowerCased.label : parentNode.nestedSearchLabel + ' ' + lowerCased.label;\n\n        var normalized = _this16.$set(_this16.forest.nodeMap, id, createMap());\n\n        _this16.$set(normalized, 'id', id);\n\n        _this16.$set(normalized, 'label', label);\n\n        _this16.$set(normalized, 'level', level);\n\n        _this16.$set(normalized, 'ancestors', isRootNode ? [] : [parentNode].concat(parentNode.ancestors));\n\n        _this16.$set(normalized, 'index', (isRootNode ? [] : parentNode.index).concat(index));\n\n        _this16.$set(normalized, 'parentNode', parentNode);\n\n        _this16.$set(normalized, 'lowerCased', lowerCased);\n\n        _this16.$set(normalized, 'nestedSearchLabel', nestedSearchLabel);\n\n        _this16.$set(normalized, 'isDisabled', isDisabled);\n\n        _this16.$set(normalized, 'isNew', isNew);\n\n        _this16.$set(normalized, 'isMatched', false);\n\n        _this16.$set(normalized, 'isHighlighted', false);\n\n        _this16.$set(normalized, 'isBranch', isBranch);\n\n        _this16.$set(normalized, 'isLeaf', isLeaf);\n\n        _this16.$set(normalized, 'isRootNode', isRootNode);\n\n        _this16.$set(normalized, 'raw', raw);\n\n        if (isBranch) {\n          var _this16$$set;\n\n          var isLoaded = Array.isArray(children);\n\n          _this16.$set(normalized, 'childrenStates', _objectSpread({}, createAsyncOptionsStates(), {\n            isLoaded: isLoaded\n          }));\n\n          _this16.$set(normalized, 'isExpanded', typeof isDefaultExpanded === 'boolean' ? isDefaultExpanded : level < _this16.defaultExpandLevel);\n\n          _this16.$set(normalized, 'hasMatchedDescendants', false);\n\n          _this16.$set(normalized, 'hasDisabledDescendants', false);\n\n          _this16.$set(normalized, 'isExpandedOnSearch', false);\n\n          _this16.$set(normalized, 'showAllChildrenOnSearch', false);\n\n          _this16.$set(normalized, 'count', (_this16$$set = {}, _defineProperty(_this16$$set, ALL_CHILDREN, 0), _defineProperty(_this16$$set, ALL_DESCENDANTS, 0), _defineProperty(_this16$$set, LEAF_CHILDREN, 0), _defineProperty(_this16$$set, LEAF_DESCENDANTS, 0), _this16$$set));\n\n          _this16.$set(normalized, 'children', isLoaded ? _this16.normalize(normalized, children, prevNodeMap) : []);\n\n          if (isDefaultExpanded === true) normalized.ancestors.forEach(function (ancestor) {\n            ancestor.isExpanded = true;\n          });\n\n          if (!isLoaded && typeof _this16.loadOptions !== 'function') {\n            warning(function () {\n              return false;\n            }, function () {\n              return 'Unloaded branch node detected. \"loadOptions\" prop is required to load its children.';\n            });\n          } else if (!isLoaded && normalized.isExpanded) {\n            _this16.loadChildrenOptions(normalized);\n          }\n        }\n\n        normalized.ancestors.forEach(function (ancestor) {\n          return ancestor.count[ALL_DESCENDANTS]++;\n        });\n        if (isLeaf) normalized.ancestors.forEach(function (ancestor) {\n          return ancestor.count[LEAF_DESCENDANTS]++;\n        });\n\n        if (!isRootNode) {\n          parentNode.count[ALL_CHILDREN] += 1;\n          if (isLeaf) parentNode.count[LEAF_CHILDREN] += 1;\n          if (isDisabled) parentNode.hasDisabledDescendants = true;\n        }\n\n        if (prevNodeMap && prevNodeMap[id]) {\n          var prev = prevNodeMap[id];\n          normalized.isMatched = prev.isMatched;\n          normalized.showAllChildrenOnSearch = prev.showAllChildrenOnSearch;\n          normalized.isHighlighted = prev.isHighlighted;\n\n          if (prev.isBranch && normalized.isBranch) {\n            normalized.isExpanded = prev.isExpanded;\n            normalized.isExpandedOnSearch = prev.isExpandedOnSearch;\n\n            if (prev.childrenStates.isLoaded && !normalized.childrenStates.isLoaded) {\n              normalized.isExpanded = false;\n            } else {\n              normalized.childrenStates = _objectSpread({}, prev.childrenStates);\n            }\n          }\n        }\n\n        return normalized;\n      });\n\n      if (this.branchNodesFirst) {\n        var branchNodes = normalizedOptions.filter(function (option) {\n          return option.isBranch;\n        });\n        var leafNodes = normalizedOptions.filter(function (option) {\n          return option.isLeaf;\n        });\n        normalizedOptions = branchNodes.concat(leafNodes);\n      }\n\n      return normalizedOptions;\n    },\n    loadRootOptions: function loadRootOptions() {\n      var _this17 = this;\n\n      this.callLoadOptionsProp({\n        action: LOAD_ROOT_OPTIONS,\n        isPending: function isPending() {\n          return _this17.rootOptionsStates.isLoading;\n        },\n        start: function start() {\n          _this17.rootOptionsStates.isLoading = true;\n          _this17.rootOptionsStates.loadingError = '';\n        },\n        succeed: function succeed() {\n          _this17.rootOptionsStates.isLoaded = true;\n\n          _this17.$nextTick(function () {\n            _this17.resetHighlightedOptionWhenNecessary(true);\n          });\n        },\n        fail: function fail(err) {\n          _this17.rootOptionsStates.loadingError = getErrorMessage(err);\n        },\n        end: function end() {\n          _this17.rootOptionsStates.isLoading = false;\n        }\n      });\n    },\n    loadChildrenOptions: function loadChildrenOptions(parentNode) {\n      var _this18 = this;\n\n      var id = parentNode.id,\n          raw = parentNode.raw;\n      this.callLoadOptionsProp({\n        action: LOAD_CHILDREN_OPTIONS,\n        args: {\n          parentNode: raw\n        },\n        isPending: function isPending() {\n          return _this18.getNode(id).childrenStates.isLoading;\n        },\n        start: function start() {\n          _this18.getNode(id).childrenStates.isLoading = true;\n          _this18.getNode(id).childrenStates.loadingError = '';\n        },\n        succeed: function succeed() {\n          _this18.getNode(id).childrenStates.isLoaded = true;\n        },\n        fail: function fail(err) {\n          _this18.getNode(id).childrenStates.loadingError = getErrorMessage(err);\n        },\n        end: function end() {\n          _this18.getNode(id).childrenStates.isLoading = false;\n        }\n      });\n    },\n    callLoadOptionsProp: function callLoadOptionsProp(_ref3) {\n      var action = _ref3.action,\n          args = _ref3.args,\n          isPending = _ref3.isPending,\n          start = _ref3.start,\n          succeed = _ref3.succeed,\n          fail = _ref3.fail,\n          end = _ref3.end;\n\n      if (!this.loadOptions || isPending()) {\n        return;\n      }\n\n      start();\n      var callback = once(function (err, result) {\n        if (err) {\n          fail(err);\n        } else {\n          succeed(result);\n        }\n\n        end();\n      });\n      var result = this.loadOptions(_objectSpread({\n        id: this.getInstanceId(),\n        instanceId: this.getInstanceId(),\n        action: action\n      }, args, {\n        callback: callback\n      }));\n\n      if (isPromise(result)) {\n        result.then(function () {\n          callback();\n        }, function (err) {\n          callback(err);\n        }).catch(function (err) {\n          console.error(err);\n        });\n      }\n    },\n    checkDuplication: function checkDuplication(node) {\n      var _this19 = this;\n\n      warning(function () {\n        return !(node.id in _this19.forest.nodeMap && !_this19.forest.nodeMap[node.id].isFallbackNode);\n      }, function () {\n        return \"Detected duplicate presence of node id \".concat(JSON.stringify(node.id), \". \") + \"Their labels are \\\"\".concat(_this19.forest.nodeMap[node.id].label, \"\\\" and \\\"\").concat(node.label, \"\\\" respectively.\");\n      });\n    },\n    verifyNodeShape: function verifyNodeShape(node) {\n      warning(function () {\n        return !(node.children === undefined && node.isBranch === true);\n      }, function () {\n        return 'Are you meant to declare an unloaded branch node? ' + '`isBranch: true` is no longer supported, please use `children: null` instead.';\n      });\n    },\n    select: function select(node) {\n      if (this.disabled || node.isDisabled) {\n        return;\n      }\n\n      if (this.single) {\n        this.clear();\n      }\n\n      var nextState = this.multiple && !this.flat ? this.forest.checkedStateMap[node.id] === UNCHECKED : !this.isSelected(node);\n\n      if (nextState) {\n        this._selectNode(node);\n      } else {\n        this._deselectNode(node);\n      }\n\n      this.buildForestState();\n\n      if (nextState) {\n        this.$emit('select', node.raw, this.getInstanceId());\n      } else {\n        this.$emit('deselect', node.raw, this.getInstanceId());\n      }\n\n      if (this.localSearch.active && nextState && (this.single || this.clearOnSelect)) {\n        this.resetSearchQuery();\n      }\n\n      if (this.single && this.closeOnSelect) {\n        this.closeMenu();\n\n        if (this.searchable) {\n          this._blurOnSelect = true;\n        }\n      }\n    },\n    clear: function clear() {\n      var _this20 = this;\n\n      if (this.hasValue) {\n        if (this.single || this.allowClearingDisabled) {\n          this.forest.selectedNodeIds = [];\n        } else {\n            this.forest.selectedNodeIds = this.forest.selectedNodeIds.filter(function (nodeId) {\n              return _this20.getNode(nodeId).isDisabled;\n            });\n          }\n\n        this.buildForestState();\n      }\n    },\n    _selectNode: function _selectNode(node) {\n      var _this21 = this;\n\n      if (this.single || this.disableBranchNodes) {\n        return this.addValue(node);\n      }\n\n      if (this.flat) {\n        this.addValue(node);\n\n        if (this.autoSelectAncestors) {\n          node.ancestors.forEach(function (ancestor) {\n            if (!_this21.isSelected(ancestor) && !ancestor.isDisabled) _this21.addValue(ancestor);\n          });\n        } else if (this.autoSelectDescendants) {\n          this.traverseDescendantsBFS(node, function (descendant) {\n            if (!_this21.isSelected(descendant) && !descendant.isDisabled) _this21.addValue(descendant);\n          });\n        }\n\n        return;\n      }\n\n      var isFullyChecked = node.isLeaf || !node.hasDisabledDescendants || this.allowSelectingDisabledDescendants;\n\n      if (isFullyChecked) {\n        this.addValue(node);\n      }\n\n      if (node.isBranch) {\n        this.traverseDescendantsBFS(node, function (descendant) {\n          if (!descendant.isDisabled || _this21.allowSelectingDisabledDescendants) {\n            _this21.addValue(descendant);\n          }\n        });\n      }\n\n      if (isFullyChecked) {\n        var curr = node;\n\n        while ((curr = curr.parentNode) !== NO_PARENT_NODE) {\n          if (curr.children.every(this.isSelected)) this.addValue(curr);else break;\n        }\n      }\n    },\n    _deselectNode: function _deselectNode(node) {\n      var _this22 = this;\n\n      if (this.disableBranchNodes) {\n        return this.removeValue(node);\n      }\n\n      if (this.flat) {\n        this.removeValue(node);\n\n        if (this.autoDeselectAncestors) {\n          node.ancestors.forEach(function (ancestor) {\n            if (_this22.isSelected(ancestor) && !ancestor.isDisabled) _this22.removeValue(ancestor);\n          });\n        } else if (this.autoDeselectDescendants) {\n          this.traverseDescendantsBFS(node, function (descendant) {\n            if (_this22.isSelected(descendant) && !descendant.isDisabled) _this22.removeValue(descendant);\n          });\n        }\n\n        return;\n      }\n\n      var hasUncheckedSomeDescendants = false;\n\n      if (node.isBranch) {\n        this.traverseDescendantsDFS(node, function (descendant) {\n          if (!descendant.isDisabled || _this22.allowSelectingDisabledDescendants) {\n            _this22.removeValue(descendant);\n\n            hasUncheckedSomeDescendants = true;\n          }\n        });\n      }\n\n      if (node.isLeaf || hasUncheckedSomeDescendants || node.children.length === 0) {\n        this.removeValue(node);\n        var curr = node;\n\n        while ((curr = curr.parentNode) !== NO_PARENT_NODE) {\n          if (this.isSelected(curr)) this.removeValue(curr);else break;\n        }\n      }\n    },\n    addValue: function addValue(node) {\n      this.forest.selectedNodeIds.push(node.id);\n      this.forest.selectedNodeMap[node.id] = true;\n    },\n    removeValue: function removeValue(node) {\n      removeFromArray(this.forest.selectedNodeIds, node.id);\n      delete this.forest.selectedNodeMap[node.id];\n    },\n    removeLastValue: function removeLastValue() {\n      if (!this.hasValue) return;\n      if (this.single) return this.clear();\n      var lastValue = getLast(this.internalValue);\n      var lastSelectedNode = this.getNode(lastValue);\n      this.select(lastSelectedNode);\n    },\n    saveMenuScrollPosition: function saveMenuScrollPosition() {\n      var $menu = this.getMenu();\n      if ($menu) this.menu.lastScrollPosition = $menu.scrollTop;\n    },\n    restoreMenuScrollPosition: function restoreMenuScrollPosition() {\n      var $menu = this.getMenu();\n      if ($menu) $menu.scrollTop = this.menu.lastScrollPosition;\n    }\n  },\n  created: function created() {\n    this.verifyProps();\n    this.resetFlags();\n  },\n  mounted: function mounted() {\n    if (this.autoFocus) this.focusInput();\n    if (!this.options && !this.async && this.autoLoadRootOptions) this.loadRootOptions();\n    if (this.alwaysOpen) this.openMenu();\n    if (this.async && this.defaultOptions) this.handleRemoteSearch();\n  },\n  destroyed: function destroyed() {\n    this.toggleClickOutsideEvent(false);\n  }\n};", "import { isNaN } from '../utils';\n\nfunction stringifyValue(value) {\n  if (typeof value === 'string') return value;\n  if (value != null && !isNaN(value)) return JSON.stringify(value);\n  return '';\n}\n\nexport default {\n  name: 'vue-treeselect--hidden-fields',\n  inject: ['instance'],\n  functional: true,\n  render: function render(_, context) {\n    var h = arguments[0];\n    var instance = context.injections.instance;\n    if (!instance.name || instance.disabled || !instance.hasValue) return null;\n    var stringifiedValues = instance.internalValue.map(stringifyValue);\n    if (instance.multiple && instance.joinValues) stringifiedValues = [stringifiedValues.join(instance.delimiter)];\n    return stringifiedValues.map(function (stringifiedValue, i) {\n      return h(\"input\", {\n        attrs: {\n          type: \"hidden\",\n          name: instance.name\n        },\n        domProps: {\n          \"value\": stringifiedValue\n        },\n        key: 'hidden-field-' + i\n      });\n    });\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HiddenFields.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HiddenFields.vue?vue&type=script&lang=js&\"", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "var render, staticRenderFns\nimport script from \"./HiddenFields.vue?vue&type=script&lang=js&\"\nexport * from \"./HiddenFields.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('4d48089a')) {\n      api.createRecord('4d48089a', component.options)\n    } else {\n      api.reload('4d48089a', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/HiddenFields.vue\"\nexport default component.exports", "import _mergeJSXProps from \"babel-helper-vue-jsx-merge-props\";\nimport { debounce, deepExtend, includes } from '../utils';\nimport { MIN_INPUT_WIDTH, KEY_CODES, INPUT_DEBOUNCE_DELAY } from '../constants';\nvar keysThatRequireMenuBeingOpen = [KEY_CODES.ENTER, KEY_CODES.END, KEY_CODES.HOME, KEY_CODES.ARROW_LEFT, KEY_CODES.ARROW_UP, KEY_CODES.ARROW_RIGHT, KEY_CODES.ARROW_DOWN];\nexport default {\n  name: 'vue-treeselect--input',\n  inject: ['instance'],\n  data: function data() {\n    return {\n      inputWidth: MIN_INPUT_WIDTH,\n      value: ''\n    };\n  },\n  computed: {\n    needAutoSize: function needAutoSize() {\n      var instance = this.instance;\n      return instance.searchable && !instance.disabled && instance.multiple;\n    },\n    inputStyle: function inputStyle() {\n      return {\n        width: this.needAutoSize ? \"\".concat(this.inputWidth, \"px\") : null\n      };\n    }\n  },\n  watch: {\n    'instance.trigger.searchQuery': function instanceTriggerSearchQuery(newValue) {\n      this.value = newValue;\n    },\n    value: function value() {\n      if (this.needAutoSize) this.$nextTick(this.updateInputWidth);\n    }\n  },\n  created: function created() {\n    this.debouncedCallback = debounce(this.updateSearchQuery, INPUT_DEBOUNCE_DELAY, {\n      leading: true,\n      trailing: true\n    });\n  },\n  methods: {\n    clear: function clear() {\n      this.onInput({\n        target: {\n          value: ''\n        }\n      });\n    },\n    focus: function focus() {\n      var instance = this.instance;\n\n      if (!instance.disabled) {\n        this.$refs.input && this.$refs.input.focus();\n      }\n    },\n    blur: function blur() {\n      this.$refs.input && this.$refs.input.blur();\n    },\n    onFocus: function onFocus() {\n      var instance = this.instance;\n      instance.trigger.isFocused = true;\n      if (instance.openOnFocus) instance.openMenu();\n    },\n    onBlur: function onBlur() {\n      var instance = this.instance;\n      var menu = instance.getMenu();\n\n      if (menu && document.activeElement === menu) {\n        return this.focus();\n      }\n\n      instance.trigger.isFocused = false;\n      instance.closeMenu();\n    },\n    onInput: function onInput(evt) {\n      var value = evt.target.value;\n      this.value = value;\n\n      if (value) {\n        this.debouncedCallback();\n      } else {\n        this.debouncedCallback.cancel();\n        this.updateSearchQuery();\n      }\n    },\n    onKeyDown: function onKeyDown(evt) {\n      var instance = this.instance;\n      var key = 'which' in evt ? evt.which : evt.keyCode;\n      if (evt.ctrlKey || evt.shiftKey || evt.altKey || evt.metaKey) return;\n\n      if (!instance.menu.isOpen && includes(keysThatRequireMenuBeingOpen, key)) {\n        evt.preventDefault();\n        return instance.openMenu();\n      }\n\n      switch (key) {\n        case KEY_CODES.BACKSPACE:\n          {\n            if (instance.backspaceRemoves && !this.value.length) {\n              instance.removeLastValue();\n            }\n\n            break;\n          }\n\n        case KEY_CODES.ENTER:\n          {\n            evt.preventDefault();\n            if (instance.menu.current === null) return;\n            var current = instance.getNode(instance.menu.current);\n            if (current.isBranch && instance.disableBranchNodes) return;\n            instance.select(current);\n            break;\n          }\n\n        case KEY_CODES.ESCAPE:\n          {\n            if (this.value.length) {\n              this.clear();\n            } else if (instance.menu.isOpen) {\n              instance.closeMenu();\n            }\n\n            break;\n          }\n\n        case KEY_CODES.END:\n          {\n            evt.preventDefault();\n            instance.highlightLastOption();\n            break;\n          }\n\n        case KEY_CODES.HOME:\n          {\n            evt.preventDefault();\n            instance.highlightFirstOption();\n            break;\n          }\n\n        case KEY_CODES.ARROW_LEFT:\n          {\n            var _current = instance.getNode(instance.menu.current);\n\n            if (_current.isBranch && instance.shouldExpand(_current)) {\n              evt.preventDefault();\n              instance.toggleExpanded(_current);\n            } else if (!_current.isRootNode && (_current.isLeaf || _current.isBranch && !instance.shouldExpand(_current))) {\n              evt.preventDefault();\n              instance.setCurrentHighlightedOption(_current.parentNode);\n            }\n\n            break;\n          }\n\n        case KEY_CODES.ARROW_UP:\n          {\n            evt.preventDefault();\n            instance.highlightPrevOption();\n            break;\n          }\n\n        case KEY_CODES.ARROW_RIGHT:\n          {\n            var _current2 = instance.getNode(instance.menu.current);\n\n            if (_current2.isBranch && !instance.shouldExpand(_current2)) {\n              evt.preventDefault();\n              instance.toggleExpanded(_current2);\n            }\n\n            break;\n          }\n\n        case KEY_CODES.ARROW_DOWN:\n          {\n            evt.preventDefault();\n            instance.highlightNextOption();\n            break;\n          }\n\n        case KEY_CODES.DELETE:\n          {\n            if (instance.deleteRemoves && !this.value.length) {\n              instance.removeLastValue();\n            }\n\n            break;\n          }\n\n        default:\n          {\n            instance.openMenu();\n          }\n      }\n    },\n    onMouseDown: function onMouseDown(evt) {\n      if (this.value.length) {\n        evt.stopPropagation();\n      }\n    },\n    renderInputContainer: function renderInputContainer() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      var props = {};\n      var children = [];\n\n      if (instance.searchable && !instance.disabled) {\n        children.push(this.renderInput());\n        if (this.needAutoSize) children.push(this.renderSizer());\n      }\n\n      if (!instance.searchable) {\n        deepExtend(props, {\n          on: {\n            focus: this.onFocus,\n            blur: this.onBlur,\n            keydown: this.onKeyDown\n          },\n          ref: 'input'\n        });\n      }\n\n      if (!instance.searchable && !instance.disabled) {\n        deepExtend(props, {\n          attrs: {\n            tabIndex: instance.tabIndex\n          }\n        });\n      }\n\n      return h(\"div\", _mergeJSXProps([{\n        \"class\": \"vue-treeselect__input-container\"\n      }, props]), [children]);\n    },\n    renderInput: function renderInput() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      return h(\"input\", {\n        ref: \"input\",\n        \"class\": \"vue-treeselect__input\",\n        attrs: {\n          type: \"text\",\n          autocomplete: \"off\",\n          tabIndex: instance.tabIndex,\n          required: instance.required && !instance.hasValue\n        },\n        domProps: {\n          \"value\": this.value\n        },\n        style: this.inputStyle,\n        on: {\n          \"focus\": this.onFocus,\n          \"input\": this.onInput,\n          \"blur\": this.onBlur,\n          \"keydown\": this.onKeyDown,\n          \"mousedown\": this.onMouseDown\n        }\n      });\n    },\n    renderSizer: function renderSizer() {\n      var h = this.$createElement;\n      return h(\"div\", {\n        ref: \"sizer\",\n        \"class\": \"vue-treeselect__sizer\"\n      }, [this.value]);\n    },\n    updateInputWidth: function updateInputWidth() {\n      this.inputWidth = Math.max(MIN_INPUT_WIDTH, this.$refs.sizer.scrollWidth + 15);\n    },\n    updateSearchQuery: function updateSearchQuery() {\n      var instance = this.instance;\n      instance.trigger.searchQuery = this.value;\n    }\n  },\n  render: function render() {\n    return this.renderInputContainer();\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Input.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Input.vue?vue&type=script&lang=js&\"\nexport * from \"./Input.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('54844eca')) {\n      api.createRecord('54844eca', component.options)\n    } else {\n      api.reload('54844eca', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/Input.vue\"\nexport default component.exports", "export default {\n  name: 'vue-treeselect--placeholder',\n  inject: ['instance'],\n  render: function render() {\n    var h = arguments[0];\n    var instance = this.instance;\n    var placeholderClass = {\n      'vue-treeselect__placeholder': true,\n      'vue-treeselect-helper-zoom-effect-off': true,\n      'vue-treeselect-helper-hide': instance.hasValue || instance.trigger.searchQuery\n    };\n    return h(\"div\", {\n      \"class\": placeholderClass\n    }, [instance.placeholder]);\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Placeholder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Placeholder.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Placeholder.vue?vue&type=script&lang=js&\"\nexport * from \"./Placeholder.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('5a99d1f3')) {\n      api.createRecord('5a99d1f3', component.options)\n    } else {\n      api.reload('5a99d1f3', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/Placeholder.vue\"\nexport default component.exports", "import Input from './Input';\nimport Placeholder from './Placeholder';\nexport default {\n  name: 'vue-treeselect--single-value',\n  inject: ['instance'],\n  methods: {\n    renderSingleValueLabel: function renderSingleValueLabel() {\n      var instance = this.instance;\n      var node = instance.selectedNodes[0];\n      var customValueLabelRenderer = instance.$scopedSlots['value-label'];\n      return customValueLabelRenderer ? customValueLabelRenderer({\n        node: node\n      }) : node.label;\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    var instance = this.instance,\n        renderValueContainer = this.$parent.renderValueContainer;\n    var shouldShowValue = instance.hasValue && !instance.trigger.searchQuery;\n    return renderValueContainer([shouldShowValue && h(\"div\", {\n      \"class\": \"vue-treeselect__single-value\"\n    }, [this.renderSingleValueLabel()]), h(Placeholder), h(Input, {\n      ref: \"input\"\n    })]);\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SingleValue.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SingleValue.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./SingleValue.vue?vue&type=script&lang=js&\"\nexport * from \"./SingleValue.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('03d19b89')) {\n      api.createRecord('03d19b89', component.options)\n    } else {\n      api.reload('03d19b89', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/SingleValue.vue\"\nexport default component.exports", "var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"svg\",\n    {\n      attrs: {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 348.333 348.333\"\n      }\n    },\n    [\n      _c(\"path\", {\n        attrs: {\n          d:\n            \"M336.559 68.611L231.016 174.165l105.543 105.549c15.699 15.705 15.699 41.145 0 56.85-7.844 7.844-18.128 11.769-28.407 11.769-10.296 0-20.581-3.919-28.419-11.769L174.167 231.003 68.609 336.563c-7.843 7.844-18.128 11.769-28.416 11.769-10.285 0-20.563-3.919-28.413-11.769-15.699-15.698-15.699-41.139 0-56.85l105.54-105.549L11.774 68.611c-15.699-15.699-15.699-41.145 0-56.844 15.696-15.687 41.127-15.687 56.829 0l105.563 105.554L279.721 11.767c15.705-15.687 41.139-15.687 56.832 0 15.705 15.699 15.705 41.145.006 56.844z\"\n        }\n      })\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "export default {\n  name: 'vue-treeselect--x'\n};", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Delete.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Delete.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Delete.vue?vue&type=template&id=364b6320&\"\nimport script from \"./Delete.vue?vue&type=script&lang=js&\"\nexport * from \"./Delete.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('364b6320')) {\n      api.createRecord('364b6320', component.options)\n    } else {\n      api.reload('364b6320', component.options)\n    }\n    module.hot.accept(\"./Delete.vue?vue&type=template&id=364b6320&\", function () {\n      api.rerender('364b6320', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/icons/Delete.vue\"\nexport default component.exports", "import { onLeftClick } from '../utils';\nimport DeleteIcon from './icons/Delete';\nexport default {\n  name: 'vue-treeselect--multi-value-item',\n  inject: ['instance'],\n  props: {\n    node: {\n      type: Object,\n      required: true\n    }\n  },\n  methods: {\n    handleMouseDown: onLeftClick(function handleMouseDown() {\n      var instance = this.instance,\n          node = this.node;\n      instance.select(node);\n    })\n  },\n  render: function render() {\n    var h = arguments[0];\n    var instance = this.instance,\n        node = this.node;\n    var itemClass = {\n      'vue-treeselect__multi-value-item': true,\n      'vue-treeselect__multi-value-item-disabled': node.isDisabled,\n      'vue-treeselect__multi-value-item-new': node.isNew\n    };\n    var customValueLabelRenderer = instance.$scopedSlots['value-label'];\n    var labelRenderer = customValueLabelRenderer ? customValueLabelRenderer({\n      node: node\n    }) : node.label;\n    return h(\"div\", {\n      \"class\": \"vue-treeselect__multi-value-item-container\"\n    }, [h(\"div\", {\n      \"class\": itemClass,\n      on: {\n        \"mousedown\": this.handleMouseDown\n      }\n    }, [h(\"span\", {\n      \"class\": \"vue-treeselect__multi-value-label\"\n    }, [labelRenderer]), h(\"span\", {\n      \"class\": \"vue-treeselect__icon vue-treeselect__value-remove\"\n    }, [h(DeleteIcon)])])]);\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MultiValueItem.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MultiValueItem.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./MultiValueItem.vue?vue&type=script&lang=js&\"\nexport * from \"./MultiValueItem.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6dd6c8ca')) {\n      api.createRecord('6dd6c8ca', component.options)\n    } else {\n      api.reload('6dd6c8ca', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/MultiValueItem.vue\"\nexport default component.exports", "import _mergeJSXProps from \"babel-helper-vue-jsx-merge-props\";\nimport MultiValueItem from './MultiValueItem';\nimport Input from './Input';\nimport Placeholder from './Placeholder';\nexport default {\n  name: 'vue-treeselect--multi-value',\n  inject: ['instance'],\n  methods: {\n    renderMultiValueItems: function renderMultiValueItems() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      return instance.internalValue.slice(0, instance.limit).map(instance.getNode).map(function (node) {\n        return h(MultiValueItem, {\n          key: \"multi-value-item-\".concat(node.id),\n          attrs: {\n            node: node\n          }\n        });\n      });\n    },\n    renderExceedLimitTip: function renderExceedLimitTip() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      var count = instance.internalValue.length - instance.limit;\n      if (count <= 0) return null;\n      return h(\"div\", {\n        \"class\": \"vue-treeselect__limit-tip vue-treeselect-helper-zoom-effect-off\",\n        key: \"exceed-limit-tip\"\n      }, [h(\"span\", {\n        \"class\": \"vue-treeselect__limit-tip-text\"\n      }, [instance.limitText(count)])]);\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    var renderValueContainer = this.$parent.renderValueContainer;\n    var transitionGroupProps = {\n      props: {\n        tag: 'div',\n        name: 'vue-treeselect__multi-value-item--transition',\n        appear: true\n      }\n    };\n    return renderValueContainer(h(\"transition-group\", _mergeJSXProps([{\n      \"class\": \"vue-treeselect__multi-value\"\n    }, transitionGroupProps]), [this.renderMultiValueItems(), this.renderExceedLimitTip(), h(Placeholder, {\n      key: \"placeholder\"\n    }), h(Input, {\n      ref: \"input\",\n      key: \"input\"\n    })]));\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MultiValue.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MultiValue.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./MultiValue.vue?vue&type=script&lang=js&\"\nexport * from \"./MultiValue.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('14fca5e8')) {\n      api.createRecord('14fca5e8', component.options)\n    } else {\n      api.reload('14fca5e8', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/MultiValue.vue\"\nexport default component.exports", "var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"svg\",\n    {\n      attrs: {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 292.362 292.362\"\n      }\n    },\n    [\n      _c(\"path\", {\n        attrs: {\n          d:\n            \"M286.935 69.377c-3.614-3.617-7.898-5.424-12.848-5.424H18.274c-4.952 0-9.233 1.807-12.85 5.424C1.807 72.998 0 77.279 0 82.228c0 4.948 1.807 9.229 5.424 12.847l127.907 127.907c3.621 3.617 7.902 5.428 12.85 5.428s9.233-1.811 12.847-5.428L286.935 95.074c3.613-3.617 5.427-7.898 5.427-12.847 0-4.948-1.814-9.229-5.427-12.85z\"\n        }\n      })\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "export default {\n  name: 'vue-treeselect--arrow'\n};", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Arrow.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Arrow.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Arrow.vue?vue&type=template&id=11186cd4&\"\nimport script from \"./Arrow.vue?vue&type=script&lang=js&\"\nexport * from \"./Arrow.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('11186cd4')) {\n      api.createRecord('11186cd4', component.options)\n    } else {\n      api.reload('11186cd4', component.options)\n    }\n    module.hot.accept(\"./Arrow.vue?vue&type=template&id=11186cd4&\", function () {\n      api.rerender('11186cd4', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/icons/Arrow.vue\"\nexport default component.exports", "import { onLeftClick, isPromise } from '../utils';\nimport SingleValue from './SingleValue';\nimport MultiValue from './MultiValue';\nimport DeleteIcon from './icons/Delete';\nimport ArrowIcon from './icons/Arrow';\nexport default {\n  name: 'vue-treeselect--control',\n  inject: ['instance'],\n  computed: {\n    shouldShowX: function shouldShowX() {\n      var instance = this.instance;\n      return instance.clearable && !instance.disabled && instance.hasValue && (this.hasUndisabledValue || instance.allowClearingDisabled);\n    },\n    shouldShowArrow: function shouldShowArrow() {\n      var instance = this.instance;\n      if (!instance.alwaysOpen) return true;\n      return !instance.menu.isOpen;\n    },\n    hasUndisabledValue: function hasUndisabledValue() {\n      var instance = this.instance;\n      return instance.hasValue && instance.internalValue.some(function (id) {\n        return !instance.getNode(id).isDisabled;\n      });\n    }\n  },\n  methods: {\n    renderX: function renderX() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      var title = instance.multiple ? instance.clearAllText : instance.clearValueText;\n      if (!this.shouldShowX) return null;\n      return h(\"div\", {\n        \"class\": \"vue-treeselect__x-container\",\n        attrs: {\n          title: title\n        },\n        on: {\n          \"mousedown\": this.handleMouseDownOnX\n        }\n      }, [h(DeleteIcon, {\n        \"class\": \"vue-treeselect__x\"\n      })]);\n    },\n    renderArrow: function renderArrow() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      var arrowClass = {\n        'vue-treeselect__control-arrow': true,\n        'vue-treeselect__control-arrow--rotated': instance.menu.isOpen\n      };\n      if (!this.shouldShowArrow) return null;\n      return h(\"div\", {\n        \"class\": \"vue-treeselect__control-arrow-container\",\n        on: {\n          \"mousedown\": this.handleMouseDownOnArrow\n        }\n      }, [h(ArrowIcon, {\n        \"class\": arrowClass\n      })]);\n    },\n    handleMouseDownOnX: onLeftClick(function handleMouseDownOnX(evt) {\n      evt.stopPropagation();\n      evt.preventDefault();\n      var instance = this.instance;\n      var result = instance.beforeClearAll();\n\n      var handler = function handler(shouldClear) {\n        if (shouldClear) instance.clear();\n      };\n\n      if (isPromise(result)) {\n        result.then(handler);\n      } else {\n        setTimeout(function () {\n          return handler(result);\n        }, 0);\n      }\n    }),\n    handleMouseDownOnArrow: onLeftClick(function handleMouseDownOnArrow(evt) {\n      evt.preventDefault();\n      evt.stopPropagation();\n      var instance = this.instance;\n      instance.focusInput();\n      instance.toggleMenu();\n    }),\n    renderValueContainer: function renderValueContainer(children) {\n      var h = this.$createElement;\n      return h(\"div\", {\n        \"class\": \"vue-treeselect__value-container\"\n      }, [children]);\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    var instance = this.instance;\n    var ValueContainer = instance.single ? SingleValue : MultiValue;\n    return h(\"div\", {\n      \"class\": \"vue-treeselect__control\",\n      on: {\n        \"mousedown\": instance.handleMouseDown\n      }\n    }, [h(ValueContainer, {\n      ref: \"value-container\"\n    }), this.renderX(), this.renderArrow()]);\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Control.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Control.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Control.vue?vue&type=script&lang=js&\"\nexport * from \"./Control.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('2fa0a0dd')) {\n      api.createRecord('2fa0a0dd', component.options)\n    } else {\n      api.reload('2fa0a0dd', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/Control.vue\"\nexport default component.exports", "export default {\n  name: 'vue-treeselect--tip',\n  functional: true,\n  props: {\n    type: {\n      type: String,\n      required: true\n    },\n    icon: {\n      type: String,\n      required: true\n    }\n  },\n  render: function render(_, context) {\n    var h = arguments[0];\n    var props = context.props,\n        children = context.children;\n    return h(\"div\", {\n      \"class\": \"vue-treeselect__tip vue-treeselect__\".concat(props.type, \"-tip\")\n    }, [h(\"div\", {\n      \"class\": \"vue-treeselect__icon-container\"\n    }, [h(\"span\", {\n      \"class\": \"vue-treeselect__icon-\".concat(props.icon)\n    })]), h(\"span\", {\n      \"class\": \"vue-treeselect__tip-text vue-treeselect__\".concat(props.type, \"-tip-text\")\n    }, [children])]);\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Tip.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Tip.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Tip.vue?vue&type=script&lang=js&\"\nexport * from \"./Tip.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('9f31bdca')) {\n      api.createRecord('9f31bdca', component.options)\n    } else {\n      api.reload('9f31bdca', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/Tip.vue\"\nexport default component.exports", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport { UNCHECKED, INDETERMINATE, CHECKED } from '../constants';\nimport { onLeftClick } from '../utils';\nimport Tip from './Tip';\nimport ArrowIcon from './icons/Arrow';\nvar arrowPlaceholder, checkMark, minusMark;\nvar Option = {\n  name: 'vue-treeselect--option',\n  inject: ['instance'],\n  props: {\n    node: {\n      type: Object,\n      required: true\n    }\n  },\n  computed: {\n    shouldExpand: function shouldExpand() {\n      var instance = this.instance,\n          node = this.node;\n      return node.isBranch && instance.shouldExpand(node);\n    },\n    shouldShow: function shouldShow() {\n      var instance = this.instance,\n          node = this.node;\n      return instance.shouldShowOptionInMenu(node);\n    }\n  },\n  methods: {\n    renderOption: function renderOption() {\n      var h = this.$createElement;\n      var instance = this.instance,\n          node = this.node;\n      var optionClass = {\n        'vue-treeselect__option': true,\n        'vue-treeselect__option--disabled': node.isDisabled,\n        'vue-treeselect__option--selected': instance.isSelected(node),\n        'vue-treeselect__option--highlight': node.isHighlighted,\n        'vue-treeselect__option--matched': instance.localSearch.active && node.isMatched,\n        'vue-treeselect__option--hide': !this.shouldShow\n      };\n      return h(\"div\", {\n        \"class\": optionClass,\n        on: {\n          \"mouseenter\": this.handleMouseEnterOption\n        },\n        attrs: {\n          \"data-id\": node.id\n        }\n      }, [this.renderArrow(), this.renderLabelContainer([this.renderCheckboxContainer([this.renderCheckbox()]), this.renderLabel()])]);\n    },\n    renderSubOptionsList: function renderSubOptionsList() {\n      var h = this.$createElement;\n      if (!this.shouldExpand) return null;\n      return h(\"div\", {\n        \"class\": \"vue-treeselect__list\"\n      }, [this.renderSubOptions(), this.renderNoChildrenTip(), this.renderLoadingChildrenTip(), this.renderLoadingChildrenErrorTip()]);\n    },\n    renderArrow: function renderArrow() {\n      var h = this.$createElement;\n      var instance = this.instance,\n          node = this.node;\n      if (instance.shouldFlattenOptions && this.shouldShow) return null;\n\n      if (node.isBranch) {\n        var transitionProps = {\n          props: {\n            name: 'vue-treeselect__option-arrow--prepare',\n            appear: true\n          }\n        };\n        var arrowClass = {\n          'vue-treeselect__option-arrow': true,\n          'vue-treeselect__option-arrow--rotated': this.shouldExpand\n        };\n        return h(\"div\", {\n          \"class\": \"vue-treeselect__option-arrow-container\",\n          on: {\n            \"mousedown\": this.handleMouseDownOnArrow\n          }\n        }, [h(\"transition\", transitionProps, [h(ArrowIcon, {\n          \"class\": arrowClass\n        })])]);\n      }\n\n      if (instance.hasBranchNodes) {\n        if (!arrowPlaceholder) arrowPlaceholder = h(\"div\", {\n          \"class\": \"vue-treeselect__option-arrow-placeholder\"\n        }, [\"\\xA0\"]);\n        return arrowPlaceholder;\n      }\n\n      return null;\n    },\n    renderLabelContainer: function renderLabelContainer(children) {\n      var h = this.$createElement;\n      return h(\"div\", {\n        \"class\": \"vue-treeselect__label-container\",\n        on: {\n          \"mousedown\": this.handleMouseDownOnLabelContainer\n        }\n      }, [children]);\n    },\n    renderCheckboxContainer: function renderCheckboxContainer(children) {\n      var h = this.$createElement;\n      var instance = this.instance,\n          node = this.node;\n      if (instance.single) return null;\n      if (instance.disableBranchNodes && node.isBranch) return null;\n      return h(\"div\", {\n        \"class\": \"vue-treeselect__checkbox-container\"\n      }, [children]);\n    },\n    renderCheckbox: function renderCheckbox() {\n      var h = this.$createElement;\n      var instance = this.instance,\n          node = this.node;\n      var checkedState = instance.forest.checkedStateMap[node.id];\n      var checkboxClass = {\n        'vue-treeselect__checkbox': true,\n        'vue-treeselect__checkbox--checked': checkedState === CHECKED,\n        'vue-treeselect__checkbox--indeterminate': checkedState === INDETERMINATE,\n        'vue-treeselect__checkbox--unchecked': checkedState === UNCHECKED,\n        'vue-treeselect__checkbox--disabled': node.isDisabled\n      };\n      if (!checkMark) checkMark = h(\"span\", {\n        \"class\": \"vue-treeselect__check-mark\"\n      });\n      if (!minusMark) minusMark = h(\"span\", {\n        \"class\": \"vue-treeselect__minus-mark\"\n      });\n      return h(\"span\", {\n        \"class\": checkboxClass\n      }, [checkMark, minusMark]);\n    },\n    renderLabel: function renderLabel() {\n      var h = this.$createElement;\n      var instance = this.instance,\n          node = this.node;\n      var shouldShowCount = node.isBranch && (instance.localSearch.active ? instance.showCountOnSearchComputed : instance.showCount);\n      var count = shouldShowCount ? instance.localSearch.active ? instance.localSearch.countMap[node.id][instance.showCountOf] : node.count[instance.showCountOf] : NaN;\n      var labelClassName = 'vue-treeselect__label';\n      var countClassName = 'vue-treeselect__count';\n      var customLabelRenderer = instance.$scopedSlots['option-label'];\n      if (customLabelRenderer) return customLabelRenderer({\n        node: node,\n        shouldShowCount: shouldShowCount,\n        count: count,\n        labelClassName: labelClassName,\n        countClassName: countClassName\n      });\n      return h(\"label\", {\n        \"class\": labelClassName\n      }, [node.label, shouldShowCount && h(\"span\", {\n        \"class\": countClassName\n      }, [\"(\", count, \")\"])]);\n    },\n    renderSubOptions: function renderSubOptions() {\n      var h = this.$createElement;\n      var node = this.node;\n      if (!node.childrenStates.isLoaded) return null;\n      return node.children.map(function (childNode) {\n        return h(Option, {\n          attrs: {\n            node: childNode\n          },\n          key: childNode.id\n        });\n      });\n    },\n    renderNoChildrenTip: function renderNoChildrenTip() {\n      var h = this.$createElement;\n      var instance = this.instance,\n          node = this.node;\n      if (!node.childrenStates.isLoaded || node.children.length) return null;\n      return h(Tip, {\n        attrs: {\n          type: \"no-children\",\n          icon: \"warning\"\n        }\n      }, [instance.noChildrenText]);\n    },\n    renderLoadingChildrenTip: function renderLoadingChildrenTip() {\n      var h = this.$createElement;\n      var instance = this.instance,\n          node = this.node;\n      if (!node.childrenStates.isLoading) return null;\n      return h(Tip, {\n        attrs: {\n          type: \"loading\",\n          icon: \"loader\"\n        }\n      }, [instance.loadingText]);\n    },\n    renderLoadingChildrenErrorTip: function renderLoadingChildrenErrorTip() {\n      var h = this.$createElement;\n      var instance = this.instance,\n          node = this.node;\n      if (!node.childrenStates.loadingError) return null;\n      return h(Tip, {\n        attrs: {\n          type: \"error\",\n          icon: \"error\"\n        }\n      }, [node.childrenStates.loadingError, h(\"a\", {\n        \"class\": \"vue-treeselect__retry\",\n        attrs: {\n          title: instance.retryTitle\n        },\n        on: {\n          \"mousedown\": this.handleMouseDownOnRetry\n        }\n      }, [instance.retryText])]);\n    },\n    handleMouseEnterOption: function handleMouseEnterOption(evt) {\n      var instance = this.instance,\n          node = this.node;\n      if (evt.target !== evt.currentTarget) return;\n      instance.setCurrentHighlightedOption(node, false);\n    },\n    handleMouseDownOnArrow: onLeftClick(function handleMouseDownOnOptionArrow() {\n      var instance = this.instance,\n          node = this.node;\n      instance.toggleExpanded(node);\n    }),\n    handleMouseDownOnLabelContainer: onLeftClick(function handleMouseDownOnLabelContainer() {\n      var instance = this.instance,\n          node = this.node;\n\n      if (node.isBranch && instance.disableBranchNodes) {\n        instance.toggleExpanded(node);\n      } else {\n        instance.select(node);\n      }\n    }),\n    handleMouseDownOnRetry: onLeftClick(function handleMouseDownOnRetry() {\n      var instance = this.instance,\n          node = this.node;\n      instance.loadChildrenOptions(node);\n    })\n  },\n  render: function render() {\n    var h = arguments[0];\n    var node = this.node;\n    var indentLevel = this.instance.shouldFlattenOptions ? 0 : node.level;\n\n    var listItemClass = _defineProperty({\n      'vue-treeselect__list-item': true\n    }, \"vue-treeselect__indent-level-\".concat(indentLevel), true);\n\n    var transitionProps = {\n      props: {\n        name: 'vue-treeselect__list--transition'\n      }\n    };\n    return h(\"div\", {\n      \"class\": listItemClass\n    }, [this.renderOption(), node.isBranch && h(\"transition\", transitionProps, [this.renderSubOptionsList()])]);\n  }\n};\nexport default Option;", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Option.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Option.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Option.vue?vue&type=script&lang=js&\"\nexport * from \"./Option.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('3dddec25')) {\n      api.createRecord('3dddec25', component.options)\n    } else {\n      api.reload('3dddec25', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/Option.vue\"\nexport default component.exports", "import { MENU_BUFFER } from '../constants';\nimport { watchSize, setupResizeAndScrollEventListeners } from '../utils';\nimport Option from './Option';\nimport Tip from './Tip';\nvar directionMap = {\n  top: 'top',\n  bottom: 'bottom',\n  above: 'top',\n  below: 'bottom'\n};\nexport default {\n  name: 'vue-treeselect--menu',\n  inject: ['instance'],\n  computed: {\n    menuStyle: function menuStyle() {\n      var instance = this.instance;\n      return {\n        maxHeight: instance.maxHeight + 'px'\n      };\n    },\n    menuContainerStyle: function menuContainerStyle() {\n      var instance = this.instance;\n      return {\n        zIndex: instance.appendToBody ? null : instance.zIndex\n      };\n    }\n  },\n  watch: {\n    'instance.menu.isOpen': function instanceMenuIsOpen(newValue) {\n      if (newValue) {\n        this.$nextTick(this.onMenuOpen);\n      } else {\n        this.onMenuClose();\n      }\n    }\n  },\n  created: function created() {\n    this.menuSizeWatcher = null;\n    this.menuResizeAndScrollEventListeners = null;\n  },\n  mounted: function mounted() {\n    var instance = this.instance;\n    if (instance.menu.isOpen) this.$nextTick(this.onMenuOpen);\n  },\n  destroyed: function destroyed() {\n    this.onMenuClose();\n  },\n  methods: {\n    renderMenu: function renderMenu() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      if (!instance.menu.isOpen) return null;\n      return h(\"div\", {\n        ref: \"menu\",\n        \"class\": \"vue-treeselect__menu\",\n        on: {\n          \"mousedown\": instance.handleMouseDown\n        },\n        style: this.menuStyle\n      }, [this.renderBeforeList(), instance.async ? this.renderAsyncSearchMenuInner() : instance.localSearch.active ? this.renderLocalSearchMenuInner() : this.renderNormalMenuInner(), this.renderAfterList()]);\n    },\n    renderBeforeList: function renderBeforeList() {\n      var instance = this.instance;\n      var beforeListRenderer = instance.$scopedSlots['before-list'];\n      return beforeListRenderer ? beforeListRenderer() : null;\n    },\n    renderAfterList: function renderAfterList() {\n      var instance = this.instance;\n      var afterListRenderer = instance.$scopedSlots['after-list'];\n      return afterListRenderer ? afterListRenderer() : null;\n    },\n    renderNormalMenuInner: function renderNormalMenuInner() {\n      var instance = this.instance;\n\n      if (instance.rootOptionsStates.isLoading) {\n        return this.renderLoadingOptionsTip();\n      } else if (instance.rootOptionsStates.loadingError) {\n        return this.renderLoadingRootOptionsErrorTip();\n      } else if (instance.rootOptionsStates.isLoaded && instance.forest.normalizedOptions.length === 0) {\n        return this.renderNoAvailableOptionsTip();\n      } else {\n        return this.renderOptionList();\n      }\n    },\n    renderLocalSearchMenuInner: function renderLocalSearchMenuInner() {\n      var instance = this.instance;\n\n      if (instance.rootOptionsStates.isLoading) {\n        return this.renderLoadingOptionsTip();\n      } else if (instance.rootOptionsStates.loadingError) {\n        return this.renderLoadingRootOptionsErrorTip();\n      } else if (instance.rootOptionsStates.isLoaded && instance.forest.normalizedOptions.length === 0) {\n        return this.renderNoAvailableOptionsTip();\n      } else if (instance.localSearch.noResults) {\n        return this.renderNoResultsTip();\n      } else {\n        return this.renderOptionList();\n      }\n    },\n    renderAsyncSearchMenuInner: function renderAsyncSearchMenuInner() {\n      var instance = this.instance;\n      var entry = instance.getRemoteSearchEntry();\n      var shouldShowSearchPromptTip = instance.trigger.searchQuery === '' && !instance.defaultOptions;\n      var shouldShowNoResultsTip = shouldShowSearchPromptTip ? false : entry.isLoaded && entry.options.length === 0;\n\n      if (shouldShowSearchPromptTip) {\n        return this.renderSearchPromptTip();\n      } else if (entry.isLoading) {\n        return this.renderLoadingOptionsTip();\n      } else if (entry.loadingError) {\n        return this.renderAsyncSearchLoadingErrorTip();\n      } else if (shouldShowNoResultsTip) {\n        return this.renderNoResultsTip();\n      } else {\n        return this.renderOptionList();\n      }\n    },\n    renderOptionList: function renderOptionList() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      return h(\"div\", {\n        \"class\": \"vue-treeselect__list\"\n      }, [instance.forest.normalizedOptions.map(function (rootNode) {\n        return h(Option, {\n          attrs: {\n            node: rootNode\n          },\n          key: rootNode.id\n        });\n      })]);\n    },\n    renderSearchPromptTip: function renderSearchPromptTip() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      return h(Tip, {\n        attrs: {\n          type: \"search-prompt\",\n          icon: \"warning\"\n        }\n      }, [instance.searchPromptText]);\n    },\n    renderLoadingOptionsTip: function renderLoadingOptionsTip() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      return h(Tip, {\n        attrs: {\n          type: \"loading\",\n          icon: \"loader\"\n        }\n      }, [instance.loadingText]);\n    },\n    renderLoadingRootOptionsErrorTip: function renderLoadingRootOptionsErrorTip() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      return h(Tip, {\n        attrs: {\n          type: \"error\",\n          icon: \"error\"\n        }\n      }, [instance.rootOptionsStates.loadingError, h(\"a\", {\n        \"class\": \"vue-treeselect__retry\",\n        on: {\n          \"click\": instance.loadRootOptions\n        },\n        attrs: {\n          title: instance.retryTitle\n        }\n      }, [instance.retryText])]);\n    },\n    renderAsyncSearchLoadingErrorTip: function renderAsyncSearchLoadingErrorTip() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      var entry = instance.getRemoteSearchEntry();\n      return h(Tip, {\n        attrs: {\n          type: \"error\",\n          icon: \"error\"\n        }\n      }, [entry.loadingError, h(\"a\", {\n        \"class\": \"vue-treeselect__retry\",\n        on: {\n          \"click\": instance.handleRemoteSearch\n        },\n        attrs: {\n          title: instance.retryTitle\n        }\n      }, [instance.retryText])]);\n    },\n    renderNoAvailableOptionsTip: function renderNoAvailableOptionsTip() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      return h(Tip, {\n        attrs: {\n          type: \"no-options\",\n          icon: \"warning\"\n        }\n      }, [instance.noOptionsText]);\n    },\n    renderNoResultsTip: function renderNoResultsTip() {\n      var h = this.$createElement;\n      var instance = this.instance;\n      return h(Tip, {\n        attrs: {\n          type: \"no-results\",\n          icon: \"warning\"\n        }\n      }, [instance.noResultsText]);\n    },\n    onMenuOpen: function onMenuOpen() {\n      this.adjustMenuOpenDirection();\n      this.setupMenuSizeWatcher();\n      this.setupMenuResizeAndScrollEventListeners();\n    },\n    onMenuClose: function onMenuClose() {\n      this.removeMenuSizeWatcher();\n      this.removeMenuResizeAndScrollEventListeners();\n    },\n    adjustMenuOpenDirection: function adjustMenuOpenDirection() {\n      var instance = this.instance;\n      if (!instance.menu.isOpen) return;\n      var $menu = instance.getMenu();\n      var $control = instance.getControl();\n      var menuRect = $menu.getBoundingClientRect();\n      var controlRect = $control.getBoundingClientRect();\n      var menuHeight = menuRect.height;\n      var viewportHeight = window.innerHeight;\n      var spaceAbove = controlRect.top;\n      var spaceBelow = window.innerHeight - controlRect.bottom;\n      var isControlInViewport = controlRect.top >= 0 && controlRect.top <= viewportHeight || controlRect.top < 0 && controlRect.bottom > 0;\n      var hasEnoughSpaceBelow = spaceBelow > menuHeight + MENU_BUFFER;\n      var hasEnoughSpaceAbove = spaceAbove > menuHeight + MENU_BUFFER;\n\n      if (!isControlInViewport) {\n        instance.closeMenu();\n      } else if (instance.openDirection !== 'auto') {\n        instance.menu.placement = directionMap[instance.openDirection];\n      } else if (hasEnoughSpaceBelow || !hasEnoughSpaceAbove) {\n        instance.menu.placement = 'bottom';\n      } else {\n        instance.menu.placement = 'top';\n      }\n    },\n    setupMenuSizeWatcher: function setupMenuSizeWatcher() {\n      var instance = this.instance;\n      var $menu = instance.getMenu();\n      if (this.menuSizeWatcher) return;\n      this.menuSizeWatcher = {\n        remove: watchSize($menu, this.adjustMenuOpenDirection)\n      };\n    },\n    setupMenuResizeAndScrollEventListeners: function setupMenuResizeAndScrollEventListeners() {\n      var instance = this.instance;\n      var $control = instance.getControl();\n      if (this.menuResizeAndScrollEventListeners) return;\n      this.menuResizeAndScrollEventListeners = {\n        remove: setupResizeAndScrollEventListeners($control, this.adjustMenuOpenDirection)\n      };\n    },\n    removeMenuSizeWatcher: function removeMenuSizeWatcher() {\n      if (!this.menuSizeWatcher) return;\n      this.menuSizeWatcher.remove();\n      this.menuSizeWatcher = null;\n    },\n    removeMenuResizeAndScrollEventListeners: function removeMenuResizeAndScrollEventListeners() {\n      if (!this.menuResizeAndScrollEventListeners) return;\n      this.menuResizeAndScrollEventListeners.remove();\n      this.menuResizeAndScrollEventListeners = null;\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    return h(\"div\", {\n      ref: \"menu-container\",\n      \"class\": \"vue-treeselect__menu-container\",\n      style: this.menuContainerStyle\n    }, [h(\"transition\", {\n      attrs: {\n        name: \"vue-treeselect__menu--transition\"\n      }\n    }, [this.renderMenu()])]);\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Menu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Menu.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Menu.vue?vue&type=script&lang=js&\"\nexport * from \"./Menu.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('9bcc0be2')) {\n      api.createRecord('9bcc0be2', component.options)\n    } else {\n      api.reload('9bcc0be2', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/Menu.vue\"\nexport default component.exports", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport Vue from 'vue';\nimport { watchSize, setupResizeAndScrollEventListeners, find } from '../utils';\nimport Menu from './Menu';\nvar PortalTarget = {\n  name: 'vue-treeselect--portal-target',\n  inject: ['instance'],\n  watch: {\n    'instance.menu.isOpen': function instanceMenuIsOpen(newValue) {\n      if (newValue) {\n        this.setupHandlers();\n      } else {\n        this.removeHandlers();\n      }\n    },\n    'instance.menu.placement': function instanceMenuPlacement() {\n      this.updateMenuContainerOffset();\n    }\n  },\n  created: function created() {\n    this.controlResizeAndScrollEventListeners = null;\n    this.controlSizeWatcher = null;\n  },\n  mounted: function mounted() {\n    var instance = this.instance;\n    if (instance.menu.isOpen) this.setupHandlers();\n  },\n  methods: {\n    setupHandlers: function setupHandlers() {\n      this.updateWidth();\n      this.updateMenuContainerOffset();\n      this.setupControlResizeAndScrollEventListeners();\n      this.setupControlSizeWatcher();\n    },\n    removeHandlers: function removeHandlers() {\n      this.removeControlResizeAndScrollEventListeners();\n      this.removeControlSizeWatcher();\n    },\n    setupControlResizeAndScrollEventListeners: function setupControlResizeAndScrollEventListeners() {\n      var instance = this.instance;\n      var $control = instance.getControl();\n      if (this.controlResizeAndScrollEventListeners) return;\n      this.controlResizeAndScrollEventListeners = {\n        remove: setupResizeAndScrollEventListeners($control, this.updateMenuContainerOffset)\n      };\n    },\n    setupControlSizeWatcher: function setupControlSizeWatcher() {\n      var _this = this;\n\n      var instance = this.instance;\n      var $control = instance.getControl();\n      if (this.controlSizeWatcher) return;\n      this.controlSizeWatcher = {\n        remove: watchSize($control, function () {\n          _this.updateWidth();\n\n          _this.updateMenuContainerOffset();\n        })\n      };\n    },\n    removeControlResizeAndScrollEventListeners: function removeControlResizeAndScrollEventListeners() {\n      if (!this.controlResizeAndScrollEventListeners) return;\n      this.controlResizeAndScrollEventListeners.remove();\n      this.controlResizeAndScrollEventListeners = null;\n    },\n    removeControlSizeWatcher: function removeControlSizeWatcher() {\n      if (!this.controlSizeWatcher) return;\n      this.controlSizeWatcher.remove();\n      this.controlSizeWatcher = null;\n    },\n    updateWidth: function updateWidth() {\n      var instance = this.instance;\n      var $portalTarget = this.$el;\n      var $control = instance.getControl();\n      var controlRect = $control.getBoundingClientRect();\n      $portalTarget.style.width = controlRect.width + 'px';\n    },\n    updateMenuContainerOffset: function updateMenuContainerOffset() {\n      var instance = this.instance;\n      var $control = instance.getControl();\n      var $portalTarget = this.$el;\n      var controlRect = $control.getBoundingClientRect();\n      var portalTargetRect = $portalTarget.getBoundingClientRect();\n      var offsetY = instance.menu.placement === 'bottom' ? controlRect.height : 0;\n      var left = Math.round(controlRect.left - portalTargetRect.left) + 'px';\n      var top = Math.round(controlRect.top - portalTargetRect.top + offsetY) + 'px';\n      var menuContainerStyle = this.$refs.menu.$refs['menu-container'].style;\n      var transformVariations = ['transform', 'webkitTransform', 'MozTransform', 'msTransform'];\n      var transform = find(transformVariations, function (t) {\n        return t in document.body.style;\n      });\n      menuContainerStyle[transform] = \"translate(\".concat(left, \", \").concat(top, \")\");\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    var instance = this.instance;\n    var portalTargetClass = ['vue-treeselect__portal-target', instance.wrapperClass];\n    var portalTargetStyle = {\n      zIndex: instance.zIndex\n    };\n    return h(\"div\", {\n      \"class\": portalTargetClass,\n      style: portalTargetStyle,\n      attrs: {\n        \"data-instance-id\": instance.getInstanceId()\n      }\n    }, [h(Menu, {\n      ref: \"menu\"\n    })]);\n  },\n  destroyed: function destroyed() {\n    this.removeHandlers();\n  }\n};\nvar placeholder;\nexport default {\n  name: 'vue-treeselect--menu-portal',\n  created: function created() {\n    this.portalTarget = null;\n  },\n  mounted: function mounted() {\n    this.setup();\n  },\n  destroyed: function destroyed() {\n    this.teardown();\n  },\n  methods: {\n    setup: function setup() {\n      var el = document.createElement('div');\n      document.body.appendChild(el);\n      this.portalTarget = new Vue(_objectSpread({\n        el: el,\n        parent: this\n      }, PortalTarget));\n    },\n    teardown: function teardown() {\n      document.body.removeChild(this.portalTarget.$el);\n      this.portalTarget.$el.innerHTML = '';\n      this.portalTarget.$destroy();\n      this.portalTarget = null;\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    if (!placeholder) placeholder = h(\"div\", {\n      \"class\": \"vue-treeselect__menu-placeholder\"\n    });\n    return placeholder;\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MenuPortal.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MenuPortal.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./MenuPortal.vue?vue&type=script&lang=js&\"\nexport * from \"./MenuPortal.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('4802d94a')) {\n      api.createRecord('4802d94a', component.options)\n    } else {\n      api.reload('4802d94a', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/MenuPortal.vue\"\nexport default component.exports", "import treeselectMixin from '../mixins/treeselectMixin';\nimport HiddenFields from './HiddenFields';\nimport Control from './Control';\nimport Menu from './Menu';\nimport MenuPortal from './MenuPortal';\nexport default {\n  name: 'vue-treeselect',\n  mixins: [treeselectMixin],\n  computed: {\n    wrapperClass: function wrapperClass() {\n      return {\n        'vue-treeselect': true,\n        'vue-treeselect--single': this.single,\n        'vue-treeselect--multi': this.multiple,\n        'vue-treeselect--searchable': this.searchable,\n        'vue-treeselect--disabled': this.disabled,\n        'vue-treeselect--focused': this.trigger.isFocused,\n        'vue-treeselect--has-value': this.hasValue,\n        'vue-treeselect--open': this.menu.isOpen,\n        'vue-treeselect--open-above': this.menu.placement === 'top',\n        'vue-treeselect--open-below': this.menu.placement === 'bottom',\n        'vue-treeselect--branch-nodes-disabled': this.disableBranchNodes,\n        'vue-treeselect--append-to-body': this.appendToBody\n      };\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    return h(\"div\", {\n      ref: \"wrapper\",\n      \"class\": this.wrapperClass\n    }, [h(HiddenFields), h(Control, {\n      ref: \"control\"\n    }), this.appendToBody ? h(MenuPortal, {\n      ref: \"portal\"\n    }) : h(Menu, {\n      ref: \"menu\"\n    })]);\n  }\n};", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Treeselect.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Treeselect.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Treeselect.vue?vue&type=script&lang=js&\"\nexport * from \"./Treeselect.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/mnt/c/Projects/vue-treeselect/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('aebf116c')) {\n      api.createRecord('aebf116c', component.options)\n    } else {\n      api.reload('aebf116c', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/components/Treeselect.vue\"\nexport default component.exports", "import Treeselect from './components/Treeselect';\nimport treeselectMixin from './mixins/treeselectMixin';\nimport './style.less';\nexport default Treeselect;\nexport { Treeselect, treeselectMixin };\nexport { LOAD_ROOT_OPTIONS, LOAD_CHILDREN_OPTIONS, ASYNC_SEARCH } from './constants';\nexport var VERSION = PKG_VERSION;"], "sourceRoot": ""}