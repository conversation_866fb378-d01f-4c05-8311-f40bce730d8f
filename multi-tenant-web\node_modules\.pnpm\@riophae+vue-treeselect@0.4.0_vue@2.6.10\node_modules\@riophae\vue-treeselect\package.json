{"name": "@riophae/vue-treeselect", "version": "0.4.0", "description": "A multi-select component with nested options support for Vue.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://vue-treeselect.js.org/", "repository": "riophae/vue-treeselect", "main": "dist/vue-treeselect.cjs.js", "unpkg": "dist/vue-treeselect.umd.min.js", "css": "dist/vue-treeselect.min.css", "files": ["src", "dist"], "scripts": {"dev": "node build/dev-server.js", "build-library": "node build/build-library.js", "build-docs": "rimraf gh-pages && mkdir gh-pages && node build/build-docs.js", "gh-pages": "npm run build-docs && gh-pages --dist gh-pages --branch gh-pages --dotfiles", "cleanup-cov": "rimraf test/unit/coverage", "unit": "npm run cleanup-cov && karma start test/unit/karma.conf.js --watch", "testonly": "npm run cleanup-cov && karma start test/unit/karma.config.js --single-run", "test": "npm run testonly", "pretest": "npm run lint", "lint:js": "eslint --ext .js --ext .vue --cache --cache-location node_modules/.cache/eslint --rule 'no-console: 2' .", "lint:css": "stylelint '**/*.less'", "lint": "npm run lint:js && npm run lint:css", "verify-builds": "size-limit && node build/verify-builds.js", "finish": "npm test && npm run build-library && npm run verify-builds"}, "pre-commit": "lint", "dependencies": {"@babel/runtime": "^7.3.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "easings-css": "^1.0.0", "fuzzysearch": "^1.0.3", "is-promise": "^2.1.0", "lodash": "^4.0.0", "material-colors": "^1.2.6", "watch-size": "^2.0.0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/preset-env": "^7.3.1", "@size-limit/preset-big-lib": "^2.0.2", "@vue/test-utils": "1.0.0-beta.16", "autoprefixer": "^9.4.6", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.0", "babel-plugin-istanbul": "^5.0.1", "babel-plugin-transform-vue-jsx": "^4.0.1", "cache-loader": "^4.0.1", "chalk": "^2.3.2", "codecov": "^3.0.0", "connect-history-api-fallback": "^1.5.0", "copy-webpack-plugin": "^5.0.3", "css-loader": "^3.0.0", "entities": "^2.0.0", "eslint": "^6.1.0", "eslint-config-riophae": "^0.9.1", "eslint-friendly-formatter": "^4.0.0", "eslint-import-resolver-webpack": "^0.11.0", "eslint-loader": "^3.0.0", "eslint-plugin-import": "^2.15.0", "eslint-plugin-react": "^7.10.0", "eslint-plugin-unicorn": "^11.0.2", "eslint-plugin-vue": "^5.1.0", "eventsource-polyfill": "^0.9.6", "express": "^4.16.3", "friendly-errors-webpack-plugin": "^1.7.0", "gh-pages": "^2.0.0", "html-webpack-plugin": "^3.1.0", "jasmine-core": "^3.1.0", "jstransformer-markdown-it": "^2.0.0", "karma": "^4.0.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.1", "karma-jasmine": "^2.0.0", "karma-jasmine-matchers": "^4.0.1", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.32", "karma-webpack": "^4.0.2", "less": "^3.0.1", "less-loader": "^5.0.0", "mini-css-extract-plugin": "^0.8.0", "open": "^6.2.0", "optimize-css-assets-webpack-plugin": "^5.0.0", "ora": "^4.0.1", "postcss-loader": "^3.0.0", "pre-commit": "^1.2.2", "pug": "^2.0.0", "pug-loader": "^2.4.0", "puppeteer": "^1.19.0", "raw-loader": "^3.0.0", "regenerator-runtime": "^0.13.1", "rimraf": "^3.0.0", "run-series": "^1.1.8", "script-loader": "^0.7.2", "shallow-equal": "^1.0.0", "shelljs": "^0.8.1", "string.prototype.repeat": "^0.2.0", "strip-indent": "^3.0.0", "stylelint": "^11.0.0", "stylelint-config-xo-space": "^0.13.0", "terser-webpack-plugin": "^2.1.0", "url-loader": "^2.0.1", "vodal": "^2.3.3", "vue": "^2.2.0", "vue-loader": "^15.6.0", "vue-style-loader": "^4.0.2", "vue-template-compiler": "^2.4.4", "vuex": "^3.0.1", "webpack": "^4.6.0", "webpack-bundle-analyzer": "^3.0.2", "webpack-cdn-plugin": "^3.1.4", "webpack-dev-middleware": "^3.1.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0", "webpack-node-externals": "^1.7.2", "yaku": "^0.19.3"}, "peerDependencies": {"vue": "^2.2.0"}, "keywords": ["vue", "component", "tree", "treeview", "select", "dropdown", "treeselect", "multiselect", "form", "control", "input", "ui"]}