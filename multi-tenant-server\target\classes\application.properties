spring.profiles.active=dev

spring.application.name=multi_tenant

#####################druid###########################
# ��ʼ������
spring.datasource.druid.initialSize=5
# ��С���ӳ�����
spring.datasource.druid.minIdle=10
# ������ӳ�����
spring.datasource.druid.maxActive=20
# ���û�ȡ���ӵȴ���ʱ��ʱ��
spring.datasource.druid.maxWait=60000
# ���ü����òŽ���һ�μ�⣬�����Ҫ�رյĿ������ӣ���λ�Ǻ���
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
# ����һ�������ڳ�����С�����ʱ�䣬��λ�Ǻ���
spring.datasource.druid.minEvictableIdleTimeMillis=300000
# ����һ�������ڳ�����������ʱ�䣬��λ�Ǻ���
spring.datasource.druid.maxEvictableIdleTimeMillis=900000
# ���ü�������Ƿ���Ч
spring.datasource.druid.validationQuery=SELECT 1 FROM DUAL
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.webStatFilter.enabled=true
spring.datasource.druid.statViewServlet.enabled=true
# ���ð��������������������з���
spring.datasource.druid.statViewServlet.allow=
spring.datasource.druid.statViewServlet.url-pattern=/druid/*
# ����̨�����û���������
spring.datasource.druid.statViewServlet.login-username=
spring.datasource.druid.statViewServlet.login-password=

# ��SQL��¼
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=1000
spring.datasource.druid.filter.stat.merge-sql=true
spring.datasource.druid.filter.wall.config.multi-statement-allow=true
#####################druid###########################